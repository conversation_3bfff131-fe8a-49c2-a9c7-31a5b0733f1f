using Microsoft.EntityFrameworkCore;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 库存服务
/// </summary>
public class StockService : BaseService<Stock>
{
    public StockService(WcsDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 根据库存编码查找库存
    /// </summary>
    /// <param name="stockCode">库存编码</param>
    /// <returns>库存信息</returns>
    public async Task<Stock?> FindByCodeAsync(string stockCode)
    {
        return await _dbSet.FirstOrDefaultAsync(s => s.StockCode == stockCode);
    }

    /// <summary>
    /// 根据巷道ID获取库存列表
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>库存列表</returns>
    public async Task<List<Stock>> GetStocksByLaneAsync(string laneCode)
    {
        return await _dbSet
            .Where(s => s.LaneCode == laneCode)
            .OrderBy(s => s.X)
            .ThenBy(s => s.Y)
            .ThenBy(s => s.Z)
            .ToListAsync();
    }

    /// <summary>
    /// 根据坐标查找库存
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <param name="x">X坐标</param>
    /// <param name="y">Y坐标</param>
    /// <param name="z">Z坐标</param>
    /// <returns>库存信息</returns>
    public async Task<Stock?> FindByPositionAsync(string laneCode, string x, string y, string z)
    {
        return await _dbSet.FirstOrDefaultAsync(s =>
            s.LaneCode == laneCode &&
            s.X == x &&
            s.Y == y &&
            s.Z == z);
    }

    /// <summary>
    /// 获取空闲货位
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>空闲货位列表</returns>
    public async Task<List<Stock>> GetEmptyStocksAsync(string laneCode)
    {
        return await _dbSet
            .Where(s => s.LaneCode == laneCode &&
                       string.IsNullOrEmpty(s.GoodsCode) &&
                       s.Status == 1) // 无任务状态
            .ToListAsync();
    }

    /// <summary>
    /// 获取有货货位
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>有货货位列表</returns>
    public async Task<List<Stock>> GetOccupiedStocksAsync(string laneCode)
    {
        return await _dbSet
            .Where(s => s.LaneCode == laneCode && !string.IsNullOrEmpty(s.GoodsCode))
            .ToListAsync();
    }

    /// <summary>
    /// 根据货物编码查找库存
    /// </summary>
    /// <param name="goodsCode">货物编码</param>
    /// <returns>库存列表</returns>
    public async Task<List<Stock>> FindByGoodsCodeAsync(string goodsCode)
    {
        return await _dbSet
            .Where(s => s.GoodsCode == goodsCode)
            .ToListAsync();
    }

    /// <summary>
    /// 更新库存状态
    /// </summary>
    /// <param name="stockId">库存ID</param>
    /// <param name="status">状态</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateStatusAsync(ulong stockId, sbyte status)
    {
        return await UpdateAsync(stockId, new Dictionary<string, object> { { "Status", status } });
    }

    /// <summary>
    /// 入库操作
    /// </summary>
    /// <param name="stockId">库存ID</param>
    /// <param name="goodsCode">货物编码</param>
    /// <returns>是否成功</returns>
    public async Task<bool> InboundAsync(ulong stockId, string goodsCode)
    {
        var updates = new Dictionary<string, object>
        {
            { "GoodsCode", goodsCode },
            { "Status", 1 } // 无任务状态
        };
        return await UpdateAsync(stockId, updates);
    }

    /// <summary>
    /// 出库操作
    /// </summary>
    /// <param name="stockId">库存ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> OutboundAsync(ulong stockId)
    {
        var updates = new Dictionary<string, object>
        {
            { "GoodsCode", string.Empty },
            { "Status", 1 } // 无任务状态
        };
        return await UpdateAsync(stockId, updates);
    }

    /// <summary>
    /// 获取库存统计信息
    /// </summary>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <returns>统计信息</returns>
    public async Task<Dictionary<string, int>> GetStockStatisticsAsync(string? laneCode = null)
    {
        var query = _dbSet.AsQueryable();

        if (!string.IsNullOrEmpty(laneCode))
        {
            query = query.Where(s => s.LaneCode == laneCode);
        }

        var totalCount = await query.CountAsync();
        var occupiedCount = await query.CountAsync(s => !string.IsNullOrEmpty(s.GoodsCode));
        var emptyCount = totalCount - occupiedCount;
        var inboundingCount = await query.CountAsync(s => s.Status == 2); // 入库中
        var outboundingCount = await query.CountAsync(s => s.Status == 3); // 出库中
        var disabledCount = await query.CountAsync(s => s.Status == 4); // 禁用

        return new Dictionary<string, int>
        {
            { "Total", totalCount },
            { "Occupied", occupiedCount },
            { "Empty", emptyCount },
            { "Inbounding", inboundingCount },
            { "Outbounding", outboundingCount },
            { "Disabled", disabledCount }
        };
    }

    /// <summary>
    /// 搜索库存
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    public async Task<(List<Stock> Items, long Total)> SearchStocksAsync(string keyword, int pageIndex, int pageSize)
    {
        return await GetPagedAsync(pageIndex, pageSize, query =>
            query.Where(s => s.StockCode.Contains(keyword) ||
                           s.GoodsCode.Contains(keyword) ||
                           s.AbcType.Contains(keyword))
                 .OrderBy(s => s.LaneCode)
                 .ThenBy(s => s.X)
                 .ThenBy(s => s.Y)
                 .ThenBy(s => s.Z));
    }

    /// <summary>
    /// 检查库存编码是否已存在
    /// </summary>
    /// <param name="stockCode">库存编码</param>
    /// <param name="excludeId">排除的库存ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsStockCodeExistsAsync(string stockCode, ulong? excludeId = null)
    {
        var query = _dbSet.Where(s => s.StockCode == stockCode);
        if (excludeId.HasValue)
        {
            query = query.Where(s => s.Id != excludeId.Value);
        }
        return await query.AnyAsync();
    }

    /// <summary>
    /// 创建库存
    /// </summary>
    /// <param name="stock">库存对象</param>
    /// <returns>创建的库存</returns>
    public override async Task<Stock> CreateAsync(Stock stock)
    {
        // 检查库存编码是否已存在
        if (await IsStockCodeExistsAsync(stock.StockCode))
        {
            throw new InvalidOperationException("库存编码已存在");
        }

        // 检查坐标是否已存在
        var existingStock = await FindByPositionAsync(stock.LaneCode, stock.X, stock.Y, stock.Z);
        if (existingStock != null)
        {
            throw new InvalidOperationException("该坐标位置已存在库存");
        }

        return await base.CreateAsync(stock);
    }
}
