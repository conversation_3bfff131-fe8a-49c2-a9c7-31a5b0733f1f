2025-06-25 08:43:50.425 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/dept/deptlist - null null
2025-06-25 08:43:50.469 +08:00 [INF] 请求开始: GET /api/dept/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:43:50.473 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:43:50.474 +08:00 [INF] 请求完成: GET /api/dept/deptlist - 状态码: 404 - 耗时: 6ms - 响应大小: 0bytes
2025-06-25 08:43:50.478 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/dept/deptlist - 404 0 null 54.2998ms
2025-06-25 08:43:50.481 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/dept/deptlist, Response status code: 404
2025-06-25 08:44:49.688 +08:00 [INF] 使用内存存储服务
2025-06-25 08:44:50.963 +08:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 08:44:50.970 +08:00 [INF] 数据库和表创建成功
2025-06-25 08:44:51.271 +08:00 [INF] Executed DbCommand (103ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:44:51.276 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 08:44:51.344 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 08:44:51.381 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 08:44:51.383 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 08:44:51.383 +08:00 [INF] Hosting environment: Development
2025-06-25 08:44:51.384 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 08:45:12.717 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:45:12.751 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:45:12.756 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:45:12.757 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:45:12.769 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:45:13.014 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:45:13.022 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:45:13.068 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 296.7985ms
2025-06-25 08:45:13.070 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:45:13.072 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 321ms - 响应大小: 324bytes
2025-06-25 08:45:13.079 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 362.7808ms
2025-06-25 08:46:06.838 +08:00 [INF] 使用内存存储服务
2025-06-25 08:46:08.348 +08:00 [INF] Executed DbCommand (55ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 08:46:08.355 +08:00 [INF] 数据库和表创建成功
2025-06-25 08:46:08.536 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:46:08.542 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 08:46:08.615 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 08:46:08.656 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 08:46:08.658 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 08:46:08.659 +08:00 [INF] Hosting environment: Development
2025-06-25 08:46:08.659 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 08:46:31.896 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:46:31.939 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:46:31.942 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:46:31.944 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:46:31.955 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:46:32.256 +08:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:46:32.263 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:46:32.310 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 352.2429ms
2025-06-25 08:46:32.312 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:46:32.313 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 375ms - 响应大小: 318bytes
2025-06-25 08:46:32.320 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 424.6381ms
2025-06-25 08:47:13.685 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 2
2025-06-25 08:47:13.692 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:13.694 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:13.703 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:13.714 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(WcsNet.DTOs.RefreshTokenRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:13.974 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:47:13.989 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 273.3709ms
2025-06-25 08:47:13.989 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:13.990 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 296ms - 响应大小: 246bytes
2025-06-25 08:47:13.992 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 307.1018ms
2025-06-25 08:47:14.287 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-25 08:47:14.292 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:14.293 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-25 08:47:14.295 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 8.5251ms
2025-06-25 08:47:14.298 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-25 08:47:24.247 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 2
2025-06-25 08:47:24.251 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:24.252 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:24.255 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:24.257 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(WcsNet.DTOs.RefreshTokenRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:24.390 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:47:24.391 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 130.0536ms
2025-06-25 08:47:24.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:24.393 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 141ms - 响应大小: 246bytes
2025-06-25 08:47:24.395 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 148.0213ms
2025-06-25 08:47:32.346 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-25 08:47:32.351 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:32.352 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:32.357 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 08:47:32.362 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:32.591 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 08:47:32.812 +08:00 [INF] Executed DbCommand (88ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 08:47:32.887 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 08:47:32.967 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 08:47:32.992 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:32.996 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 631.3103ms
2025-06-25 08:47:32.997 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 08:47:32.998 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 645ms - 响应大小: 449bytes
2025-06-25 08:47:33.000 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 653.6943ms
2025-06-25 08:47:33.007 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 08:47:33.010 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.013 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 08:47:33.018 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 08:47:33.428 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 08:47:33.479 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:33.492 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 471.9074ms
2025-06-25 08:47:33.493 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 08:47:33.495 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 483ms - 响应大小: 6882bytes
2025-06-25 08:47:33.497 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 489.9974ms
2025-06-25 08:47:33.633 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-25 08:47:33.636 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.645 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 9ms - 响应大小: 0bytes
2025-06-25 08:47:33.649 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 15.9669ms
2025-06-25 08:47:33.658 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-25 08:47:33.663 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.665 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.668 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.673 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 08:47:33.676 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.678 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.678 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.682 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 08:47:33.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 26.184ms
2025-06-25 08:47:33.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 22.2004ms
2025-06-25 08:47:33.708 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.709 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.710 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 41ms - 响应大小: 577bytes
2025-06-25 08:47:33.710 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 46ms - 响应大小: 801bytes
2025-06-25 08:47:33.712 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 53.1434ms
2025-06-25 08:47:33.715 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 56.5169ms
2025-06-25 08:47:33.833 +08:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Executed DbCommand (43ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:33.969 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.970 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.972 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.979 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.976 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.978 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.975 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:33.981 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.982 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.983 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.985 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.987 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.988 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.992 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.6704ms
2025-06-25 08:47:33.992 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 299.6864ms
2025-06-25 08:47:33.993 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 7.2126ms
2025-06-25 08:47:33.994 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 6.3601ms
2025-06-25 08:47:33.995 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.996 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 08:47:33.997 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.998 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.999 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 29ms - 响应大小: 1103bytes
2025-06-25 08:47:33.999 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 334ms - 响应大小: 1236bytes
2025-06-25 08:47:34.000 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 27ms - 响应大小: 570bytes
2025-06-25 08:47:34.001 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 30ms - 响应大小: 573bytes
2025-06-25 08:47:34.002 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 35.5732ms
2025-06-25 08:47:34.004 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 345.4294ms
2025-06-25 08:47:34.009 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 42.1341ms
2025-06-25 08:47:34.010 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 43.729ms
2025-06-25 08:47:37.890 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 08:47:37.892 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:37.894 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:37.898 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:47:38.336 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:47:38.356 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:38.368 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 467.0663ms
2025-06-25 08:47:38.373 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.375 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 482ms - 响应大小: 347bytes
2025-06-25 08:47:38.377 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 486.9104ms
2025-06-25 08:47:38.383 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - null null
2025-06-25 08:47:38.384 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:38.385 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.386 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:47:38.569 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:47:38.580 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:38.582 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 194.5045ms
2025-06-25 08:47:38.584 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.587 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/2 - 状态码: 200 - 耗时: 202ms - 响应大小: 544bytes
2025-06-25 08:47:38.591 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - 200 null application/json; charset=utf-8 207.4004ms
2025-06-25 08:47:38.597 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:47:38.602 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:38.611 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:38.624 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:38.896 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:47:38.969 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:38.974 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:38.988 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 349.108ms
2025-06-25 08:47:38.991 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:38.992 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 390ms - 响应大小: 1561bytes
2025-06-25 08:47:38.996 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 398.7865ms
2025-06-25 08:47:43.773 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/10 - application/json 121
2025-06-25 08:47:43.776 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:43.778 +08:00 [INF] 请求开始: PUT /api/devices/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:43.780 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 08:47:43.786 +08:00 [INF] Route matched with {action = "UpdateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDevice(UInt64, WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:44.035 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 08:47:44.209 +08:00 [INF] Executed DbCommand (78ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 64), @__id_1='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE (`w`.`device_code` = @__request_DeviceCode_0) AND (`w`.`Id` <> @__id_1))
2025-06-25 08:47:44.392 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@p1='?' (DbType = UInt64), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device` SET `updated_at` = @p0
WHERE `Id` = @p1;
SELECT ROW_COUNT();
2025-06-25 08:47:44.413 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:44.416 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet) in 628.1695ms
2025-06-25 08:47:44.417 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 08:47:44.418 +08:00 [INF] 请求完成: PUT /api/devices/10 - 状态码: 200 - 耗时: 640ms - 响应大小: 48bytes
2025-06-25 08:47:44.421 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/10 - 200 null application/json; charset=utf-8 647.8281ms
2025-06-25 08:47:44.446 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:47:44.448 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:44.451 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:44.452 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:44.744 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:47:44.802 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:44.807 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:44.808 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 351.802ms
2025-06-25 08:47:44.809 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:44.810 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 362ms - 响应大小: 1561bytes
2025-06-25 08:47:44.812 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 366.0879ms
2025-06-25 08:48:01.038 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 121
2025-06-25 08:48:01.040 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:48:01.041 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:01.043 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:01.049 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:01.309 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:48:01.313 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 263.086ms
2025-06-25 08:48:01.314 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:01.316 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 400 - 耗时: 274ms - 响应大小: 381bytes
2025-06-25 08:48:01.317 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 400 null application/problem+json; charset=utf-8 278.9731ms
2025-06-25 08:48:17.990 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 121
2025-06-25 08:48:17.993 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:48:17.994 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:17.997 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:18.001 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:18.284 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:48:18.298 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 291.3991ms
2025-06-25 08:48:18.323 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:18.340 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 400 - 耗时: 345ms - 响应大小: 381bytes
2025-06-25 08:48:18.353 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 400 null application/problem+json; charset=utf-8 362.497ms
2025-06-25 08:48:48.213 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - null null
2025-06-25 08:48:48.216 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.218 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.222 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:48.456 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:48.459 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:48.461 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 199.6799ms
2025-06-25 08:48:48.462 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.464 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/7 - 状态码: 200 - 耗时: 248ms - 响应大小: 268bytes
2025-06-25 08:48:48.466 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - 200 null application/json; charset=utf-8 252.7738ms
2025-06-25 08:48:48.477 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - null null
2025-06-25 08:48:48.480 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.482 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.483 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:48.741 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:48.743 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:48.745 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 258.7605ms
2025-06-25 08:48:48.747 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.748 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/8 - 状态码: 200 - 耗时: 268ms - 响应大小: 431bytes
2025-06-25 08:48:48.750 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - 200 null application/json; charset=utf-8 272.6623ms
2025-06-25 08:48:48.756 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - null null
2025-06-25 08:48:48.758 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.760 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.787 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:49.331 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:49.333 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:49.335 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 544.1288ms
2025-06-25 08:48:49.336 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.337 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/9 - 状态码: 200 - 耗时: 578ms - 响应大小: 1014bytes
2025-06-25 08:48:49.339 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - 200 null application/json; charset=utf-8 582.391ms
2025-06-25 08:48:49.658 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 08:48:49.661 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:49.662 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.663 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:49.887 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:49.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:49.891 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 226.1678ms
2025-06-25 08:48:49.892 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.893 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 231ms - 响应大小: 347bytes
2025-06-25 08:48:49.895 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 236.6732ms
2025-06-25 08:48:49.915 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-25 08:48:49.917 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:49.919 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:48:49.920 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:50.287 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:48:50.356 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:48:50.359 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:48:50.360 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 438.419ms
2025-06-25 08:48:50.361 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:48:50.363 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 445ms - 响应大小: 1561bytes
2025-06-25 08:48:50.365 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 449.4592ms
2025-06-25 08:48:50.384 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - null null
2025-06-25 08:48:50.390 +08:00 [INF] 请求开始: GET /api/devices/props/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:50.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:50.397 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:50.632 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:50.635 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:50.639 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 239.6751ms
2025-06-25 08:48:50.640 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:50.641 +08:00 [INF] 请求完成: GET /api/devices/props/10 - 状态码: 200 - 耗时: 251ms - 响应大小: 36bytes
2025-06-25 08:48:50.642 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - 200 null application/json; charset=utf-8 257.9688ms
2025-06-25 08:48:55.526 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - null null
2025-06-25 08:48:55.529 +08:00 [INF] 请求开始: GET /api/devices/props/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:55.531 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:55.532 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:55.770 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:55.772 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:55.774 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 240.4474ms
2025-06-25 08:48:55.775 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:55.776 +08:00 [INF] 请求完成: GET /api/devices/props/10 - 状态码: 200 - 耗时: 246ms - 响应大小: 36bytes
2025-06-25 08:48:55.779 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - 200 null application/json; charset=utf-8 252.8028ms
2025-06-25 08:48:57.889 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/3 - null null
2025-06-25 08:48:57.895 +08:00 [INF] 请求开始: GET /api/devices/props/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:57.899 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:57.900 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:58.270 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:58.280 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:58.282 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 378.9311ms
2025-06-25 08:48:58.283 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:58.284 +08:00 [INF] 请求完成: GET /api/devices/props/3 - 状态码: 200 - 耗时: 388ms - 响应大小: 36bytes
2025-06-25 08:48:58.286 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/3 - 200 null application/json; charset=utf-8 397.4155ms
2025-06-25 08:49:01.959 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/6 - null null
2025-06-25 08:49:01.961 +08:00 [INF] 请求开始: GET /api/devices/props/6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:01.963 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:01.964 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:02.277 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:02.279 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:02.280 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 308.8275ms
2025-06-25 08:49:02.280 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:02.281 +08:00 [INF] 请求完成: GET /api/devices/props/6 - 状态码: 200 - 耗时: 319ms - 响应大小: 36bytes
2025-06-25 08:49:02.282 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/6 - 200 null application/json; charset=utf-8 323.0865ms
2025-06-25 08:49:08.972 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-25 08:49:08.975 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:08.981 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:08.986 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:09.283 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:09.287 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:09.290 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 298.7381ms
2025-06-25 08:49:09.291 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:09.292 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 316ms - 响应大小: 1399bytes
2025-06-25 08:49:09.294 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 321.7362ms
2025-06-25 08:49:12.593 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - null 0
2025-06-25 08:49:12.595 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:49:12.596 +08:00 [INF] 请求开始: DELETE /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:12.598 +08:00 [INF] 请求完成: DELETE /api/devices/property/7 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-25 08:49:12.599 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - 404 0 null 5.7579ms
2025-06-25 08:49:12.603 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: DELETE http://localhost:8666/api/devices/property/7, Response status code: 404
2025-06-25 08:49:52.738 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-25 08:49:52.740 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:52.743 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:52.743 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:52.962 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:52.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:52.972 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 227.0148ms
2025-06-25 08:49:52.974 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:52.982 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 241ms - 响应大小: 1399bytes
2025-06-25 08:49:52.988 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 249.3906ms
2025-06-25 08:50:17.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - null null
2025-06-25 08:50:17.394 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.396 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:50:17.396 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:50:17.521 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:17.524 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.526 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:17.530 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:17.531 +08:00 [INF] Executed DbCommand (33ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:50:17.533 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.535 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 135.8412ms
2025-06-25 08:50:17.538 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:50:17.542 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/11 - 状态码: 200 - 耗时: 147ms - 响应大小: 362bytes
2025-06-25 08:50:17.545 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - 200 null application/json; charset=utf-8 153.3984ms
2025-06-25 08:50:17.555 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups - null null
2025-06-25 08:50:17.558 +08:00 [INF] 请求开始: GET /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.560 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 08:50:17.564 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:17.684 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:17.762 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:17.765 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.772 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 240.1804ms
2025-06-25 08:50:17.774 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:17.774 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 250ms - 响应大小: 488bytes
2025-06-25 08:50:17.776 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 255.0351ms
2025-06-25 08:50:17.780 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 08:50:17.901 +08:00 [INF] Executed DbCommand (70ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:17.904 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.911 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 345.5823ms
2025-06-25 08:50:17.912 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 08:50:17.913 +08:00 [INF] 请求完成: GET /api/task/actgroups - 状态码: 200 - 耗时: 355ms - 响应大小: 964bytes
2025-06-25 08:50:17.914 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups - 200 null application/json; charset=utf-8 359.6658ms
2025-06-25 08:50:17.931 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - null null
2025-06-25 08:50:17.933 +08:00 [INF] 请求开始: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.935 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 08:50:17.941 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:18.240 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 08:50:18.300 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:18.303 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:18.308 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 366.0142ms
2025-06-25 08:50:18.309 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 08:50:18.310 +08:00 [INF] 请求完成: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 377ms - 响应大小: 1738bytes
2025-06-25 08:50:18.312 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 380.6009ms
2025-06-25 08:50:25.009 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:25.011 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:25.013 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:25.014 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:25.379 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:25.454 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:25.457 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:25.458 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 441.6666ms
2025-06-25 08:50:25.459 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:25.460 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 449ms - 响应大小: 488bytes
2025-06-25 08:50:25.462 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 453.3592ms
2025-06-25 08:50:29.326 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:29.328 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:29.330 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:29.331 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:29.650 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:29.707 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:29.708 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:29.709 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 376.2081ms
2025-06-25 08:50:29.710 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:29.711 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 382ms - 响应大小: 488bytes
2025-06-25 08:50:29.713 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 386.3435ms
2025-06-25 08:54:18.349 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:54:18.351 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:18.352 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:54:18.352 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:54:18.782 +08:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:54:18.783 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:54:18.784 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 430.6627ms
2025-06-25 08:54:18.785 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:54:18.786 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 435ms - 响应大小: 318bytes
2025-06-25 08:54:18.788 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 438.5125ms
2025-06-25 08:54:20.556 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:54:20.556 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-25 08:54:20.560 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:20.562 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:20.566 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 08:54:20.569 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 08:54:20.583 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.UserPagedResponse]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-25 08:54:20.584 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-25 08:54:20.824 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:54:20.830 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 08:54:20.831 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 08:54:20.875 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, CAST(`s`.`Status` AS char) AS `Status`, CASE
    WHEN `s`.`Gender` = 1 THEN '男'
    WHEN `s`.`Gender` = 2 THEN '女'
    ELSE '未知'
END AS `Gender`, `s`.`dept_id` AS `DeptId`, COALESCE(`s`.`created_at`, CURRENT_TIMESTAMP()) AS `CreateTime`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:54:20.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.UserPagedResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:54:20.886 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 297.4895ms
2025-06-25 08:54:20.888 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 08:54:20.889 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 329ms - 响应大小: 390bytes
2025-06-25 08:54:20.891 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 334.6593ms
2025-06-25 08:54:20.964 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`role_name` AS `name`, `s`.`role_code` AS `code`
FROM `sys_role` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`
2025-06-25 08:54:20.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:54:20.970 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 379.7722ms
2025-06-25 08:54:20.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 08:54:20.972 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 410ms - 响应大小: 206bytes
2025-06-25 08:54:20.975 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 418.5151ms
2025-06-25 09:01:15.182 +08:00 [INF] 使用内存存储服务
2025-06-25 09:01:16.348 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:01:16.360 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:01:16.576 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:01:16.593 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:01:16.663 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:01:16.704 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:01:16.728 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:01:16.729 +08:00 [INF] Hosting environment: Development
2025-06-25 09:01:16.730 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:01:42.105 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:01:42.138 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:01:42.142 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:01:42.143 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:01:42.163 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Department]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:01:42.580 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:01:42.624 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Department, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:01:42.673 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 507.2167ms
2025-06-25 09:01:42.675 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:01:42.676 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 538ms - 响应大小: 1245bytes
2025-06-25 09:01:42.684 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 580.1193ms
2025-06-25 09:01:54.201 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-25 09:01:54.207 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:01:54.208 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 09:01:54.212 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Role]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-25 09:01:54.711 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`Perms`, `s`.`Remark`, `s`.`role_code`, `s`.`role_name`, `s`.`role_type`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_role` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`
2025-06-25 09:01:54.724 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Role, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:01:54.728 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 513.8285ms
2025-06-25 09:01:54.728 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 09:01:54.729 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 522ms - 响应大小: 999bytes
2025-06-25 09:01:54.730 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 528.768ms
2025-06-25 09:14:56.618 +08:00 [INF] 使用内存存储服务
2025-06-25 09:14:58.250 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:14:58.256 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:14:58.392 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:14:58.397 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:14:58.467 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:14:58.506 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:14:58.508 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:14:58.508 +08:00 [INF] Hosting environment: Development
2025-06-25 09:14:58.509 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:15:41.471 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:15:41.516 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:15:41.521 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:15:41.523 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:15:41.535 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Department]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:15:41.810 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:15:41.852 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Department, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:15:41.911 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 373.8436ms
2025-06-25 09:15:41.914 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:15:41.915 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 401ms - 响应大小: 1293bytes
2025-06-25 09:15:41.925 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 456.0743ms
2025-06-25 09:15:52.913 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:15:52.919 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:15:52.920 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:15:52.921 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Department]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:15:53.306 +08:00 [INF] Executed DbCommand (66ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:15:53.308 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Department, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:15:53.309 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 387.0437ms
2025-06-25 09:15:53.310 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:15:53.310 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 391ms - 响应大小: 1293bytes
2025-06-25 09:15:53.311 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 398.3647ms
2025-06-25 09:16:12.872 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?page_size=2&page_no=1 - null null
2025-06-25 09:16:12.875 +08:00 [INF] 请求开始: GET /api/sys/users?page_size=2&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:16:12.877 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 09:16:12.886 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.UserPagedResponse]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-25 09:16:13.161 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:16:13.167 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 09:16:13.168 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 09:16:13.303 +08:00 [INF] Executed DbCommand (62ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, CAST(`s`.`Status` AS char) AS `Status`, CASE
    WHEN `s`.`Gender` = 1 THEN '男'
    WHEN `s`.`Gender` = 2 THEN '女'
    ELSE '未知'
END AS `Gender`, `s`.`dept_id` AS `DeptId`, COALESCE(`s`.`created_at`, CURRENT_TIMESTAMP()) AS `CreateTime`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:16:13.305 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.UserPagedResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:16:13.310 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 422.838ms
2025-06-25 09:16:13.311 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 09:16:13.312 +08:00 [INF] 请求完成: GET /api/sys/users?page_size=2&page_no=1 - 状态码: 200 - 耗时: 436ms - 响应大小: 394bytes
2025-06-25 09:16:13.313 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?page_size=2&page_no=1 - 200 null application/json; charset=utf-8 441.2699ms
2025-06-25 09:16:25.906 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_size=2&page_no=1 - null null
2025-06-25 09:16:25.909 +08:00 [INF] 请求开始: GET /api/devices?page_size=2&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:16:25.911 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:16:25.926 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:16:26.161 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:16:26.256 +08:00 [INF] Executed DbCommand (64ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:16:26.260 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:16:26.270 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 341.3913ms
2025-06-25 09:16:26.271 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:16:26.272 +08:00 [INF] 请求完成: GET /api/devices?page_size=2&page_no=1 - 状态码: 200 - 耗时: 363ms - 响应大小: 548bytes
2025-06-25 09:16:26.274 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_size=2&page_no=1 - 200 null application/json; charset=utf-8 368.086ms
2025-06-25 09:26:59.669 +08:00 [INF] 使用内存存储服务
2025-06-25 09:27:01.051 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:27:01.057 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:27:01.206 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:27:01.212 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:27:01.282 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:27:01.319 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:27:01.321 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:27:01.322 +08:00 [INF] Hosting environment: Development
2025-06-25 09:27:01.322 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:27:20.234 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:27:20.268 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:27:20.272 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:27:20.273 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:27:20.285 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:27:20.579 +08:00 [INF] Executed DbCommand (82ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:27:20.591 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:27:20.646 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 357.3119ms
2025-06-25 09:27:20.648 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:27:20.649 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 381ms - 响应大小: 324bytes
2025-06-25 09:27:20.657 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 423.5207ms
2025-06-25 09:28:28.268 +08:00 [INF] 使用内存存储服务
2025-06-25 09:28:29.673 +08:00 [INF] Executed DbCommand (72ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:28:29.681 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:28:29.925 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:28:29.930 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:28:30.003 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:28:30.040 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:28:30.042 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:28:30.043 +08:00 [INF] Hosting environment: Development
2025-06-25 09:28:30.043 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:28:53.653 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:28:53.686 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:28:53.689 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:28:53.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:28:53.703 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DeptListDto]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:28:53.908 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `Label`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:28:53.917 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DeptListDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:28:53.960 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 253.1833ms
2025-06-25 09:28:53.961 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:28:53.962 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 277ms - 响应大小: 318bytes
2025-06-25 09:28:53.969 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 317.0617ms
2025-06-25 09:29:07.637 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?page_size=1&page_no=1 - null null
2025-06-25 09:29:07.642 +08:00 [INF] 请求开始: GET /api/sys/users?page_size=1&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:29:07.644 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 09:29:07.651 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.UserPagedResponse]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-25 09:29:07.986 +08:00 [INF] Executed DbCommand (96ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:29:07.991 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 09:29:07.991 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 09:29:08.202 +08:00 [INF] Executed DbCommand (101ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, CAST(`s`.`Status` AS char) AS `Status`, CASE
    WHEN `s`.`Gender` = 1 THEN '男'
    WHEN `s`.`Gender` = 2 THEN '女'
    ELSE '未知'
END AS `Gender`, `s`.`dept_id` AS `DeptId`, COALESCE(`s`.`created_at`, CURRENT_TIMESTAMP()) AS `CreateTime`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:29:08.204 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.UserPagedResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:29:08.213 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 561.1118ms
2025-06-25 09:29:08.214 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 09:29:08.215 +08:00 [INF] 请求完成: GET /api/sys/users?page_size=1&page_no=1 - 状态码: 200 - 耗时: 573ms - 响应大小: 227bytes
2025-06-25 09:29:08.216 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?page_size=1&page_no=1 - 200 null application/json; charset=utf-8 579.7759ms
2025-06-25 09:29:15.917 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_size=1&page_no=1 - null null
2025-06-25 09:29:15.920 +08:00 [INF] 请求开始: GET /api/devices?page_size=1&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:29:15.922 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:29:15.928 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:29:16.266 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:29:16.390 +08:00 [INF] Executed DbCommand (100ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:29:16.393 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:29:16.401 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 471.1516ms
2025-06-25 09:29:16.401 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:29:16.402 +08:00 [INF] 请求完成: GET /api/devices?page_size=1&page_no=1 - 状态码: 200 - 耗时: 481ms - 响应大小: 299bytes
2025-06-25 09:29:16.403 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_size=1&page_no=1 - 200 null application/json; charset=utf-8 486.2159ms
2025-06-25 09:32:04.472 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 09:32:04.473 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:04.474 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:32:04.479 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:32:04.682 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:32:04.744 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:32:04.760 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 280.2276ms
2025-06-25 09:32:04.762 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:32:04.763 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 289ms - 响应大小: 351bytes
2025-06-25 09:32:04.765 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 293.4643ms
2025-06-25 09:32:04.773 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - null null
2025-06-25 09:32:04.776 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:04.779 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:32:04.780 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:32:05.243 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:32:05.251 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:32:05.252 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 467.6836ms
2025-06-25 09:32:05.254 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:32:05.255 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/2 - 状态码: 200 - 耗时: 478ms - 响应大小: 551bytes
2025-06-25 09:32:05.258 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - 200 null application/json; charset=utf-8 484.4523ms
2025-06-25 09:32:05.584 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 09:32:05.586 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:05.588 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:32:05.589 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:32:05.910 +08:00 [INF] Executed DbCommand (96ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:32:06.178 +08:00 [INF] Executed DbCommand (193ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:32:06.183 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:32:06.185 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 593.6612ms
2025-06-25 09:32:06.186 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:32:06.188 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 601ms - 响应大小: 1561bytes
2025-06-25 09:32:06.190 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 606.0627ms
2025-06-25 09:32:08.808 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/10 - application/json 121
2025-06-25 09:32:08.811 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:32:08.813 +08:00 [INF] 请求开始: PUT /api/devices/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:08.822 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 09:32:08.827 +08:00 [INF] Route matched with {action = "UpdateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDevice(UInt64, WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:32:09.298 +08:00 [INF] Executed DbCommand (85ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:32:09.469 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 64), @__id_1='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE (`w`.`device_code` = @__request_DeviceCode_0) AND (`w`.`Id` <> @__id_1))
2025-06-25 09:32:09.754 +08:00 [INF] Executed DbCommand (136ms) [Parameters=[@p1='?' (DbType = UInt64), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device` SET `updated_at` = @p0
WHERE `Id` = @p1;
SELECT ROW_COUNT();
2025-06-25 09:32:09.766 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:32:09.770 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet) in 941.2822ms
2025-06-25 09:32:09.772 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 09:32:09.773 +08:00 [INF] 请求完成: PUT /api/devices/10 - 状态码: 200 - 耗时: 960ms - 响应大小: 48bytes
2025-06-25 09:32:09.776 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/10 - 200 null application/json; charset=utf-8 968.0866ms
2025-06-25 09:32:10.107 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 09:32:10.110 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:10.112 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:32:10.113 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:32:10.378 +08:00 [INF] Executed DbCommand (95ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:32:10.446 +08:00 [INF] Executed DbCommand (46ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:32:10.450 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:32:10.452 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 335.583ms
2025-06-25 09:32:10.454 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:32:10.456 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 346ms - 响应大小: 1561bytes
2025-06-25 09:32:10.458 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 350.5035ms
2025-06-25 09:32:22.133 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 141
2025-06-25 09:32:22.141 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:32:22.144 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:32:22.156 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 09:32:22.166 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:32:22.378 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 09:32:22.406 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 237.1546ms
2025-06-25 09:32:22.408 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 09:32:22.410 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 400 - 耗时: 265ms - 响应大小: 381bytes
2025-06-25 09:32:22.411 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 400 null application/problem+json; charset=utf-8 278.0498ms
2025-06-25 09:33:03.133 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - null null
2025-06-25 09:33:03.136 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:03.138 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:03.140 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:33:03.257 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:33:03.288 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:03.289 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 146.4483ms
2025-06-25 09:33:03.290 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:03.293 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/7 - 状态码: 200 - 耗时: 156ms - 响应大小: 271bytes
2025-06-25 09:33:03.295 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - 200 null application/json; charset=utf-8 161.9573ms
2025-06-25 09:33:03.394 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - null null
2025-06-25 09:33:03.398 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:03.400 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:03.401 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:33:03.714 +08:00 [INF] Executed DbCommand (49ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:33:03.719 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:03.720 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 315.6118ms
2025-06-25 09:33:03.721 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:03.722 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/8 - 状态码: 200 - 耗时: 324ms - 响应大小: 436bytes
2025-06-25 09:33:03.724 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - 200 null application/json; charset=utf-8 330.2172ms
2025-06-25 09:33:03.731 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - null null
2025-06-25 09:33:03.733 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:03.735 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:03.736 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:33:04.282 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:33:04.287 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:04.288 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 549.7733ms
2025-06-25 09:33:04.289 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:04.290 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/9 - 状态码: 200 - 耗时: 556ms - 响应大小: 1027bytes
2025-06-25 09:33:04.292 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - 200 null application/json; charset=utf-8 561.0134ms
2025-06-25 09:33:04.606 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 09:33:04.612 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:04.614 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:04.615 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:33:04.903 +08:00 [INF] Executed DbCommand (106ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:33:04.905 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:04.907 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 289.8812ms
2025-06-25 09:33:04.908 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:33:04.909 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 297ms - 响应大小: 351bytes
2025-06-25 09:33:04.912 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 305.9383ms
2025-06-25 09:33:04.918 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-25 09:33:04.919 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:04.920 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:33:04.921 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:33:05.256 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:33:05.433 +08:00 [INF] Executed DbCommand (141ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:33:05.435 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:33:05.437 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 513.5034ms
2025-06-25 09:33:05.438 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:33:05.440 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 520ms - 响应大小: 1561bytes
2025-06-25 09:33:05.442 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 524.3278ms
2025-06-25 09:33:05.767 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - null null
2025-06-25 09:33:05.776 +08:00 [INF] 请求开始: GET /api/devices/props/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:05.777 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 09:33:05.782 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:33:05.968 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 09:33:05.969 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:05.973 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 189.3365ms
2025-06-25 09:33:05.975 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 09:33:05.977 +08:00 [INF] 请求完成: GET /api/devices/props/10 - 状态码: 200 - 耗时: 200ms - 响应大小: 36bytes
2025-06-25 09:33:05.978 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - 200 null application/json; charset=utf-8 211.4986ms
2025-06-25 09:33:11.441 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-25 09:33:11.445 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:11.464 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 09:33:11.466 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:33:11.889 +08:00 [INF] Executed DbCommand (63ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 09:33:11.903 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:33:11.907 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 436.5244ms
2025-06-25 09:33:11.909 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 09:33:11.911 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 465ms - 响应大小: 1447bytes
2025-06-25 09:33:11.912 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 471.4088ms
2025-06-25 09:33:29.248 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 142
2025-06-25 09:33:29.251 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:33:29.252 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:33:29.254 +08:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-06-25 09:33:29.255 +08:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-06-25 09:33:29.256 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 405 - 耗时: 4ms - 响应大小: 0bytes
2025-06-25 09:33:29.258 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 405 0 null 9.3616ms
2025-06-25 09:34:06.693 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/property/7 - application/json 145
2025-06-25 09:34:06.696 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:34:06.697 +08:00 [INF] 请求开始: PUT /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:06.700 +08:00 [INF] 请求完成: PUT /api/devices/property/7 - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-25 09:34:06.704 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/property/7 - 404 0 null 11.384ms
2025-06-25 09:34:06.712 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: PUT http://localhost:8666/api/devices/property/7, Response status code: 404
2025-06-25 09:34:34.307 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - null null
2025-06-25 09:34:34.309 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:34.311 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:34:34.312 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:34:34.454 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:34:34.459 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:34.463 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 148.4621ms
2025-06-25 09:34:34.464 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:34:34.466 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/10 - 状态码: 200 - 耗时: 156ms - 响应大小: 381bytes
2025-06-25 09:34:34.468 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - 200 null application/json; charset=utf-8 160.9984ms
2025-06-25 09:34:34.480 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 09:34:34.483 +08:00 [INF] 请求开始: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:34.484 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:34.489 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:34.587 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 09:34:34.639 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:34:34.643 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:34.650 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 159.1831ms
2025-06-25 09:34:34.652 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:34.655 +08:00 [INF] 请求完成: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 171ms - 响应大小: 980bytes
2025-06-25 09:34:34.657 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 176.7729ms
2025-06-25 09:34:44.760 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/task/actgroups - application/json 109
2025-06-25 09:34:44.763 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:34:44.764 +08:00 [INF] 请求开始: POST /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:44.771 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.CreateActGroup (WcsNet)'
2025-06-25 09:34:44.779 +08:00 [INF] Route matched with {action = "CreateActGroup", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] CreateActGroup(WcsNet.Controllers.CreateActGroupRequest) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:45.168 +08:00 [INF] Executed DbCommand (23ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (DbType = Int64), @p2='?' (Size = 128), @p3='?' (Size = 16), @p4='?' (Size = 255), @p5='?' (DbType = Int32), @p6='?' (DbType = SByte), @p7='?' (DbType = DateTime), @p8='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_actuator_group` (`created_at`, `created_id`, `group_name`, `group_type`, `Remark`, `sort_number`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
SELECT `Id`
FROM `wcs_actuator_group`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:34:45.222 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:45.224 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.CreateActGroup (WcsNet) in 442.5152ms
2025-06-25 09:34:45.225 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.CreateActGroup (WcsNet)'
2025-06-25 09:34:45.226 +08:00 [INF] 请求完成: POST /api/task/actgroups - 状态码: 200 - 耗时: 462ms - 响应大小: 63bytes
2025-06-25 09:34:45.232 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/task/actgroups - 200 null application/json; charset=utf-8 471.784ms
2025-06-25 09:34:45.571 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 09:34:45.574 +08:00 [INF] 请求开始: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:45.576 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:45.577 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:45.829 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 09:34:45.881 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:34:45.883 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:45.885 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 303.8859ms
2025-06-25 09:34:45.887 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:45.890 +08:00 [INF] 请求完成: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 315ms - 响应大小: 1133bytes
2025-06-25 09:34:45.892 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 321.1485ms
2025-06-25 09:34:50.536 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/actgroups/12 - null 0
2025-06-25 09:34:50.545 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:34:50.547 +08:00 [INF] 请求开始: DELETE /api/task/actgroups/12 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:50.563 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.DeleteActGroup (WcsNet)'
2025-06-25 09:34:50.571 +08:00 [INF] Route matched with {action = "DeleteActGroup", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteActGroup(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:50.737 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`group_name`, `w`.`group_type`, `w`.`Remark`, `w`.`sort_number`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_actuator_group` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:34:50.768 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_actuator_group`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-25 09:34:50.775 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:50.782 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.DeleteActGroup (WcsNet) in 206.12ms
2025-06-25 09:34:50.796 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.DeleteActGroup (WcsNet)'
2025-06-25 09:34:50.801 +08:00 [INF] 请求完成: DELETE /api/task/actgroups/12 - 状态码: 200 - 耗时: 253ms - 响应大小: 63bytes
2025-06-25 09:34:50.817 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/actgroups/12 - 200 null application/json; charset=utf-8 280.5621ms
2025-06-25 09:34:51.144 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 09:34:51.151 +08:00 [INF] 请求开始: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:51.155 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:51.156 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:51.348 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 09:34:51.358 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:34:51.363 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:51.365 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 203.7804ms
2025-06-25 09:34:51.366 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:51.367 +08:00 [INF] 请求完成: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 216ms - 响应大小: 980bytes
2025-06-25 09:34:51.368 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 224.016ms
2025-06-25 09:34:52.944 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - null null
2025-06-25 09:34:52.948 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:52.953 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:34:52.958 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:34:53.215 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:34:53.218 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:53.218 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 257.4188ms
2025-06-25 09:34:53.219 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:34:53.219 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/11 - 状态码: 200 - 耗时: 271ms - 响应大小: 366bytes
2025-06-25 09:34:53.220 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - 200 null application/json; charset=utf-8 276.0066ms
2025-06-25 09:34:53.542 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups - null null
2025-06-25 09:34:53.546 +08:00 [INF] 请求开始: GET /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:53.549 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:53.554 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:53.764 +08:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 09:34:53.818 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:34:53.820 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:53.822 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 262.2719ms
2025-06-25 09:34:53.823 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 09:34:53.824 +08:00 [INF] 请求完成: GET /api/task/actgroups - 状态码: 200 - 耗时: 278ms - 响应大小: 980bytes
2025-06-25 09:34:53.826 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups - 200 null application/json; charset=utf-8 283.9827ms
2025-06-25 09:34:53.834 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - null null
2025-06-25 09:34:53.837 +08:00 [INF] 请求开始: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:34:53.839 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:34:53.850 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:34:54.184 +08:00 [INF] Executed DbCommand (23ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 09:34:54.262 +08:00 [INF] Executed DbCommand (61ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:34:54.265 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:34:54.269 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 417.1388ms
2025-06-25 09:34:54.275 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:34:54.276 +08:00 [INF] 请求完成: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 438ms - 响应大小: 1783bytes
2025-06-25 09:34:54.278 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 444.6255ms
2025-06-25 09:35:07.685 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/task/acts - application/json 137
2025-06-25 09:35:07.688 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:35:07.689 +08:00 [INF] 请求开始: POST /api/task/acts - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:35:07.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.CreateAct (WcsNet)'
2025-06-25 09:35:07.695 +08:00 [INF] Route matched with {action = "CreateAct", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] CreateAct(WcsNet.Controllers.CreateActRequest) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:35:08.166 +08:00 [INF] Executed DbCommand (117ms) [Parameters=[@p0='?' (Size = 128), @p1='?' (DbType = SByte), @p2='?' (Size = 255), @p3='?' (DbType = DateTime), @p4='?' (DbType = Int64), @p5='?' (DbType = UInt64), @p6='?' (DbType = SByte), @p7='?' (Size = 128), @p8='?' (Size = 255), @p9='?' (DbType = DateTime), @p10='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_actuator` (`act_name`, `act_type`, `class_name`, `created_at`, `created_id`, `group_id`, `Level`, `method_name`, `Remark`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10);
SELECT `Id`
FROM `wcs_actuator`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:35:08.222 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:35:08.230 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.CreateAct (WcsNet) in 532.9529ms
2025-06-25 09:35:08.231 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.CreateAct (WcsNet)'
2025-06-25 09:35:08.232 +08:00 [INF] 请求完成: POST /api/task/acts - 状态码: 200 - 耗时: 543ms - 响应大小: 57bytes
2025-06-25 09:35:08.234 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/task/acts - 200 null application/json; charset=utf-8 548.6936ms
2025-06-25 09:35:08.263 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - null null
2025-06-25 09:35:08.265 +08:00 [INF] 请求开始: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:35:08.268 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:35:08.270 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:35:08.413 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 09:35:08.478 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:35:08.480 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:35:08.481 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 208.8833ms
2025-06-25 09:35:08.488 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:35:08.492 +08:00 [INF] 请求完成: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 226ms - 响应大小: 1935bytes
2025-06-25 09:35:08.495 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 232.7143ms
2025-06-25 09:35:16.033 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/acts/10 - null 0
2025-06-25 09:35:16.036 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:35:16.037 +08:00 [INF] 请求开始: DELETE /api/task/acts/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:35:16.040 +08:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-06-25 09:35:16.042 +08:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-06-25 09:35:16.043 +08:00 [INF] 请求完成: DELETE /api/task/acts/10 - 状态码: 405 - 耗时: 5ms - 响应大小: 0bytes
2025-06-25 09:35:16.045 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/acts/10 - 405 0 null 11.8369ms
2025-06-25 09:35:52.454 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/acts/11 - null 0
2025-06-25 09:35:52.456 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:35:52.457 +08:00 [INF] 请求开始: DELETE /api/task/acts/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:35:52.459 +08:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-06-25 09:35:52.460 +08:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-06-25 09:35:52.461 +08:00 [INF] 请求完成: DELETE /api/task/acts/11 - 状态码: 405 - 耗时: 3ms - 响应大小: 0bytes
2025-06-25 09:35:52.462 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/acts/11 - 405 0 null 8.4473ms
2025-06-25 09:36:09.847 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 09:36:09.849 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:09.851 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:09.855 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:36:10.202 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 09:36:10.283 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:10.286 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:10.292 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 435.38ms
2025-06-25 09:36:10.294 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:10.296 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 446ms - 响应大小: 504bytes
2025-06-25 09:36:10.298 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 451.0478ms
2025-06-25 09:36:22.335 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/task/paths - application/json 110
2025-06-25 09:36:22.338 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:36:22.339 +08:00 [INF] 请求开始: POST /api/task/paths - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:22.348 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.CreatePath (WcsNet)'
2025-06-25 09:36:22.355 +08:00 [INF] Route matched with {action = "CreatePath", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] CreatePath(WcsNet.Controllers.CreatePathRequest) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:36:22.866 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@p0='?' (DbType = Int32), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int64), @p3='?' (Size = 128), @p4='?' (Size = 255), @p5='?' (Size = 128), @p6='?' (DbType = Int32), @p7='?' (DbType = DateTime), @p8='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_path_define` (`block_time`, `created_at`, `created_id`, `end_point`, `path_name`, `start_point`, `transit_time`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8);
SELECT `Id`
FROM `wcs_path_define`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:36:22.947 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:22.949 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.CreatePath (WcsNet) in 589.6812ms
2025-06-25 09:36:22.950 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.CreatePath (WcsNet)'
2025-06-25 09:36:22.951 +08:00 [INF] 请求完成: POST /api/task/paths - 状态码: 200 - 耗时: 611ms - 响应大小: 54bytes
2025-06-25 09:36:22.953 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/task/paths - 200 null application/json; charset=utf-8 618.0586ms
2025-06-25 09:36:23.287 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 09:36:23.289 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:23.291 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:23.292 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:36:23.618 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 09:36:23.722 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:23.724 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:23.726 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 432.0307ms
2025-06-25 09:36:23.728 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:23.730 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 441ms - 响应大小: 728bytes
2025-06-25 09:36:23.732 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 445.3554ms
2025-06-25 09:36:30.194 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/paths/5 - null 0
2025-06-25 09:36:30.197 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:36:30.198 +08:00 [INF] 请求开始: DELETE /api/task/paths/5 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:30.203 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.DeletePath (WcsNet)'
2025-06-25 09:36:30.207 +08:00 [INF] Route matched with {action = "DeletePath", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeletePath(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:36:30.376 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`block_time`, `w`.`created_at`, `w`.`created_id`, `w`.`end_point`, `w`.`path_name`, `w`.`start_point`, `w`.`transit_time`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_path_define` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:36:30.452 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_path_define`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-25 09:36:30.454 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:30.456 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.DeletePath (WcsNet) in 246.3247ms
2025-06-25 09:36:30.457 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.DeletePath (WcsNet)'
2025-06-25 09:36:30.460 +08:00 [INF] 请求完成: DELETE /api/task/paths/5 - 状态码: 200 - 耗时: 261ms - 响应大小: 54bytes
2025-06-25 09:36:30.462 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/paths/5 - 200 null application/json; charset=utf-8 268.3015ms
2025-06-25 09:36:30.798 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 09:36:30.801 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:30.802 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:30.804 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:36:31.081 +08:00 [INF] Executed DbCommand (99ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 09:36:31.141 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:31.145 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:31.152 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 346.1046ms
2025-06-25 09:36:31.153 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 09:36:31.154 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 353ms - 响应大小: 504bytes
2025-06-25 09:36:31.156 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 358.2226ms
2025-06-25 09:36:33.986 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - null null
2025-06-25 09:36:33.988 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/5 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:33.989 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:36:33.990 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 09:36:34.211 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:36:34.213 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:34.214 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 221.8794ms
2025-06-25 09:36:34.216 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 09:36:34.217 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/5 - 状态码: 200 - 耗时: 229ms - 响应大小: 196bytes
2025-06-25 09:36:34.219 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - 200 null application/json; charset=utf-8 233.0613ms
2025-06-25 09:36:34.227 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:36:34.229 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:34.231 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:34.237 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetWorks(System.String, System.Nullable`1[System.SByte], System.String, System.String, Int32, Int32) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:34.419 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-25 09:36:34.519 +08:00 [INF] Executed DbCommand (57ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:34.548 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:34.555 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 315.9352ms
2025-06-25 09:36:34.556 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:34.558 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 328ms - 响应大小: 2525bytes
2025-06-25 09:36:34.562 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 334.7611ms
2025-06-25 09:36:43.468 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/works - application/json 123
2025-06-25 09:36:43.471 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:36:43.472 +08:00 [INF] 请求开始: POST /api/works - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:43.475 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.CreateWork (WcsNet)'
2025-06-25 09:36:43.488 +08:00 [INF] Route matched with {action = "CreateWork", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.Models.Work]]] CreateWork(WcsNet.Models.Work) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:43.926 +08:00 [INF] Executed DbCommand (54ms) [Parameters=[@__work_ItemCode_0='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
WHERE `w`.`item_code` = @__work_ItemCode_0
LIMIT 1
2025-06-25 09:36:44.217 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (DbType = Int64), @p2='?' (Size = 4000), @p3='?' (Size = 128), @p4='?' (DbType = UInt64), @p5='?' (DbType = SByte), @p6='?' (Size = 4000), @p7='?' (DbType = SByte), @p8='?' (DbType = SByte), @p9='?' (DbType = SByte), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_work` (`created_at`, `created_id`, `err_msg`, `item_code`, `lane_id`, `max_retries`, `Parameters`, `Retries`, `Status`, `task_type`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
SELECT `Id`
FROM `wcs_work`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:36:44.293 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:36:44.298 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.CreateWork (WcsNet) in 779.4522ms
2025-06-25 09:36:44.299 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.CreateWork (WcsNet)'
2025-06-25 09:36:44.300 +08:00 [INF] 请求完成: POST /api/works - 状态码: 200 - 耗时: 828ms - 响应大小: 271bytes
2025-06-25 09:36:44.302 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/works - 200 null application/json; charset=utf-8 834.0211ms
2025-06-25 09:36:44.330 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:36:44.333 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:44.335 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:44.335 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetWorks(System.String, System.Nullable`1[System.SByte], System.String, System.String, Int32, Int32) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:44.583 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-25 09:36:44.632 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:44.635 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:44.636 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 294.1905ms
2025-06-25 09:36:44.638 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:44.639 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 305ms - 响应大小: 2521bytes
2025-06-25 09:36:44.641 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 310.1218ms
2025-06-25 09:36:49.608 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/works/3521 - null 0
2025-06-25 09:36:49.611 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:36:49.612 +08:00 [INF] 请求开始: DELETE /api/works/3521 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:49.614 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.DeleteWork (WcsNet)'
2025-06-25 09:36:49.618 +08:00 [INF] Route matched with {action = "DeleteWork", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse]] DeleteWork(UInt64) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:50.085 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:36:50.154 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_work`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-25 09:36:50.155 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse'.
2025-06-25 09:36:50.156 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.DeleteWork (WcsNet) in 536.1039ms
2025-06-25 09:36:50.157 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.DeleteWork (WcsNet)'
2025-06-25 09:36:50.160 +08:00 [INF] 请求完成: DELETE /api/works/3521 - 状态码: 200 - 耗时: 548ms - 响应大小: 49bytes
2025-06-25 09:36:50.164 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/works/3521 - 200 null application/json; charset=utf-8 556.1598ms
2025-06-25 09:36:50.185 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:36:50.187 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:50.190 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:50.191 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetWorks(System.String, System.Nullable`1[System.SByte], System.String, System.String, Int32, Int32) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:50.392 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-25 09:36:50.468 +08:00 [INF] Executed DbCommand (55ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:50.471 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:50.472 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 277.9519ms
2025-06-25 09:36:50.473 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:50.474 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 286ms - 响应大小: 2537bytes
2025-06-25 09:36:50.477 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 291.5992ms
2025-06-25 09:36:52.741 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works/steps/3522 - null null
2025-06-25 09:36:52.746 +08:00 [INF] 请求开始: GET /api/works/steps/3522 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:52.747 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet)'
2025-06-25 09:36:52.752 +08:00 [INF] Route matched with {action = "GetWorkSteps", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.WorkStep]]]] GetWorkSteps(UInt64) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:53.005 +08:00 [INF] Executed DbCommand (100ms) [Parameters=[@__workId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`actuator_id`, `w`.`created_at`, `w`.`created_id`, `w`.`device_type`, `w`.`end_time`, `w`.`op_type`, `w`.`Params`, `w`.`Remark`, `w`.`Status`, `w`.`step_number`, `w`.`updated_at`, `w`.`updated_id`, `w`.`work_id`
FROM `wcs_work_step` AS `w`
WHERE `w`.`work_id` = @__workId_0
ORDER BY `w`.`step_number`
2025-06-25 09:36:53.007 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.WorkStep, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:53.012 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet) in 257.328ms
2025-06-25 09:36:53.014 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet)'
2025-06-25 09:36:53.016 +08:00 [INF] 请求完成: GET /api/works/steps/3522 - 状态码: 200 - 耗时: 270ms - 响应大小: 36bytes
2025-06-25 09:36:53.017 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works/steps/3522 - 200 null application/json; charset=utf-8 276.2719ms
2025-06-25 09:36:54.451 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works/steps/3520 - null null
2025-06-25 09:36:54.453 +08:00 [INF] 请求开始: GET /api/works/steps/3520 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:54.456 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet)'
2025-06-25 09:36:54.457 +08:00 [INF] Route matched with {action = "GetWorkSteps", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.WorkStep]]]] GetWorkSteps(UInt64) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:54.662 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[@__workId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`actuator_id`, `w`.`created_at`, `w`.`created_id`, `w`.`device_type`, `w`.`end_time`, `w`.`op_type`, `w`.`Params`, `w`.`Remark`, `w`.`Status`, `w`.`step_number`, `w`.`updated_at`, `w`.`updated_id`, `w`.`work_id`
FROM `wcs_work_step` AS `w`
WHERE `w`.`work_id` = @__workId_0
ORDER BY `w`.`step_number`
2025-06-25 09:36:54.689 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.WorkStep, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:54.697 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet) in 235.6588ms
2025-06-25 09:36:54.698 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorkSteps (WcsNet)'
2025-06-25 09:36:54.699 +08:00 [INF] 请求完成: GET /api/works/steps/3520 - 状态码: 200 - 耗时: 245ms - 响应大小: 2277bytes
2025-06-25 09:36:54.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works/steps/3520 - 200 null application/json; charset=utf-8 251.484ms
2025-06-25 09:36:57.534 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/works/3522 - null 0
2025-06-25 09:36:57.549 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:36:57.550 +08:00 [INF] 请求开始: DELETE /api/works/3522 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:57.552 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.DeleteWork (WcsNet)'
2025-06-25 09:36:57.553 +08:00 [INF] Route matched with {action = "DeleteWork", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse]] DeleteWork(UInt64) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:57.785 +08:00 [INF] Executed DbCommand (80ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:36:57.836 +08:00 [INF] Executed DbCommand (33ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_work`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-25 09:36:57.839 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse'.
2025-06-25 09:36:57.840 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.DeleteWork (WcsNet) in 285.0571ms
2025-06-25 09:36:57.841 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.DeleteWork (WcsNet)'
2025-06-25 09:36:57.842 +08:00 [INF] 请求完成: DELETE /api/works/3522 - 状态码: 200 - 耗时: 292ms - 响应大小: 49bytes
2025-06-25 09:36:57.844 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/works/3522 - 200 null application/json; charset=utf-8 310.6469ms
2025-06-25 09:36:57.866 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:36:57.869 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:36:57.871 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:57.872 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetWorks(System.String, System.Nullable`1[System.SByte], System.String, System.String, Int32, Int32) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-25 09:36:58.190 +08:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-25 09:36:58.271 +08:00 [INF] Executed DbCommand (69ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:36:58.276 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:36:58.281 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 406.549ms
2025-06-25 09:36:58.283 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-25 09:36:58.284 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 414ms - 响应大小: 2541bytes
2025-06-25 09:36:58.286 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 419.4555ms
2025-06-25 09:37:01.422 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:37:01.424 +08:00 [INF] 请求开始: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:01.428 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.ApiLogController.GetApiLogs (WcsNet)'
2025-06-25 09:37:01.433 +08:00 [INF] Route matched with {action = "GetApiLogs", controller = "ApiLog"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetApiLogs(Int32, Int32, System.String, System.Nullable`1[System.Int32]) on controller WcsNet.Controllers.ApiLogController (WcsNet).
2025-06-25 09:37:01.870 +08:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_api_log` AS `w`
2025-06-25 09:37:02.000 +08:00 [INF] Executed DbCommand (90ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`api_name` AS `ApiName`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`, COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP()) AS `UpdatedAt`, `w`.`Status`
FROM `wcs_api_log` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:02.002 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:02.004 +08:00 [INF] Executed action WcsNet.Controllers.ApiLogController.GetApiLogs (WcsNet) in 568.9281ms
2025-06-25 09:37:02.005 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.ApiLogController.GetApiLogs (WcsNet)'
2025-06-25 09:37:02.005 +08:00 [INF] 请求完成: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 581ms - 响应大小: 1368bytes
2025-06-25 09:37:02.007 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 584.82ms
2025-06-25 09:37:03.963 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/apilogs/21051 - null null
2025-06-25 09:37:03.965 +08:00 [INF] 请求开始: GET /api/apilogs/21051 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:03.967 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.ApiLogController.GetApiLog (WcsNet)'
2025-06-25 09:37:03.969 +08:00 [INF] Route matched with {action = "GetApiLog", controller = "ApiLog"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.ApiLogDto]] GetApiLog(Int32) on controller WcsNet.Controllers.ApiLogController (WcsNet).
2025-06-25 09:37:04.238 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.ApiLogDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:37:04.242 +08:00 [INF] Executed action WcsNet.Controllers.ApiLogController.GetApiLog (WcsNet) in 270.6653ms
2025-06-25 09:37:04.243 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.ApiLogController.GetApiLog (WcsNet)'
2025-06-25 09:37:04.244 +08:00 [INF] 请求完成: GET /api/apilogs/21051 - 状态码: 200 - 耗时: 279ms - 响应大小: 311bytes
2025-06-25 09:37:04.246 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/apilogs/21051 - 200 null application/json; charset=utf-8 282.9074ms
2025-06-25 09:37:06.516 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:37:06.516 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-25 09:37:06.518 +08:00 [INF] 请求开始: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:06.520 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:06.523 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:06.525 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:37:06.526 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:37:06.529 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:37:06.715 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 09:37:06.770 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:06.773 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:37:06.774 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 245.9938ms
2025-06-25 09:37:06.775 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 09:37:06.776 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 256ms - 响应大小: 1561bytes
2025-06-25 09:37:06.778 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 262.6082ms
2025-06-25 09:37:06.823 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:37:06.929 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:06.933 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:06.939 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 407.9878ms
2025-06-25 09:37:06.941 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:06.944 +08:00 [INF] 请求完成: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 425ms - 响应大小: 1266bytes
2025-06-25 09:37:06.946 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 430.6336ms
2025-06-25 09:37:11.917 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/alarms/8938 - null 0
2025-06-25 09:37:11.920 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:37:11.921 +08:00 [INF] 请求开始: DELETE /api/alarms/8938 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:11.923 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.DeleteAlarm (WcsNet)'
2025-06-25 09:37:11.928 +08:00 [INF] Route matched with {action = "DeleteAlarm", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteAlarm(UInt64) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:37:12.342 +08:00 [INF] Executed DbCommand (24ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type`, `l`.`Category`, `l`.`Code`, `l`.`created_at`, `l`.`device_id`, `l`.`Remark`
FROM `log_alarm` AS `l`
WHERE `l`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:37:12.516 +08:00 [INF] Executed DbCommand (79ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `log_alarm`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-25 09:37:12.517 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:12.519 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.DeleteAlarm (WcsNet) in 587.4617ms
2025-06-25 09:37:12.520 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.DeleteAlarm (WcsNet)'
2025-06-25 09:37:12.521 +08:00 [INF] 请求完成: DELETE /api/alarms/8938 - 状态码: 200 - 耗时: 600ms - 响应大小: 54bytes
2025-06-25 09:37:12.523 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/alarms/8938 - 200 null application/json; charset=utf-8 606.0402ms
2025-06-25 09:37:12.545 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:37:12.547 +08:00 [INF] 请求开始: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:12.549 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:12.550 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:37:12.771 +08:00 [INF] Executed DbCommand (58ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:37:12.889 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:12.892 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:12.893 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 340.3463ms
2025-06-25 09:37:12.895 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:12.896 +08:00 [INF] 请求完成: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 348ms - 响应大小: 1266bytes
2025-06-25 09:37:12.898 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 353.0902ms
2025-06-25 09:37:14.648 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-25 09:37:14.651 +08:00 [INF] 请求开始: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:14.653 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:14.655 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:37:14.907 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:37:14.961 +08:00 [INF] Executed DbCommand (43ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:14.974 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:14.976 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 317.0375ms
2025-06-25 09:37:14.979 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:37:14.981 +08:00 [INF] 请求完成: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 330ms - 响应大小: 1266bytes
2025-06-25 09:37:14.987 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 339.3426ms
2025-06-25 09:37:18.287 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - null null
2025-06-25 09:37:18.289 +08:00 [INF] 请求开始: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:18.291 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.GetLanes (WcsNet)'
2025-06-25 09:37:18.298 +08:00 [INF] Route matched with {action = "GetLanes", controller = "Lane"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.LaneDto]]]] GetLanes(Int32, Int32) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:37:18.600 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_lane` AS `w`
2025-06-25 09:37:18.656 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT CAST(`w`.`Id` AS signed), `w`.`lane_name`, `w`.`lane_code`, CASE
    WHEN `w`.`created_at` IS NOT NULL THEN `w`.`created_at`
    ELSE CURRENT_TIMESTAMP()
END, CASE
    WHEN `w`.`updated_at` IS NOT NULL THEN `w`.`updated_at`
    ELSE CURRENT_TIMESTAMP()
END
FROM `wcs_lane` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:37:18.660 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:37:18.665 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.GetLanes (WcsNet) in 362.8193ms
2025-06-25 09:37:18.667 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.GetLanes (WcsNet)'
2025-06-25 09:37:18.668 +08:00 [INF] 请求完成: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 378ms - 响应大小: 437bytes
2025-06-25 09:37:18.670 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 382.8695ms
2025-06-25 09:37:23.723 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/lanes/useable/devices?id=0 - null null
2025-06-25 09:37:23.725 +08:00 [INF] 请求开始: GET /api/lanes/useable/devices?id=0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:23.728 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.GetUseableLaneDevices (WcsNet)'
2025-06-25 09:37:23.732 +08:00 [INF] Route matched with {action = "GetUseableLaneDevices", controller = "Lane"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.LaneDeviceDto]]] GetUseableLaneDevices(System.Nullable`1[System.Int32]) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:37:23.968 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.LaneDeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:37:23.970 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.GetUseableLaneDevices (WcsNet) in 236.1607ms
2025-06-25 09:37:23.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.GetUseableLaneDevices (WcsNet)'
2025-06-25 09:37:23.971 +08:00 [INF] 请求完成: GET /api/lanes/useable/devices?id=0 - 状态码: 200 - 耗时: 246ms - 响应大小: 575bytes
2025-06-25 09:37:23.972 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/lanes/useable/devices?id=0 - 200 null application/json; charset=utf-8 249.4786ms
2025-06-25 09:37:30.701 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/lanes - application/json 624
2025-06-25 09:37:30.703 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:37:30.705 +08:00 [INF] 请求开始: POST /api/lanes - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:30.708 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:37:30.716 +08:00 [INF] Route matched with {action = "CreateLane", controller = "Lane"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LaneDto]]] CreateLane(WcsNet.Controllers.CreateLaneRequest) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:37:31.238 +08:00 [ERR] Failed executing DbCommand (13ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (DbType = Int64), @p2='?' (Size = 64), @p3='?' (Size = 64), @p4='?' (DbType = Int32), @p5='?' (Size = 255), @p6='?' (DbType = Int32), @p7='?' (DbType = SByte), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_lane` (`created_at`, `created_id`, `lane_code`, `lane_name`, `lane_type`, `Remark`, `sort_number`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9);
SELECT `Id`
FROM `wcs_lane`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:37:31.318 +08:00 [ERR] An exception occurred in the database while saving changes for context type 'WcsNet.Models.WcsDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> MySqlConnector.MySqlException (0x80004005): Unknown column 'lane_type' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> MySqlConnector.MySqlException (0x80004005): Unknown column 'lane_type' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-06-25 09:37:31.332 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:37:31.334 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.CreateLane (WcsNet) in 612.5779ms
2025-06-25 09:37:31.335 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:37:31.336 +08:00 [INF] 请求完成: POST /api/lanes - 状态码: 200 - 耗时: 631ms - 响应大小: 120bytes
2025-06-25 09:37:31.337 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/lanes - 200 null application/json; charset=utf-8 636.9088ms
2025-06-25 09:37:38.626 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/lanes - application/json 624
2025-06-25 09:37:38.628 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:37:38.629 +08:00 [INF] 请求开始: POST /api/lanes - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:38.631 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:37:38.632 +08:00 [INF] Route matched with {action = "CreateLane", controller = "Lane"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LaneDto]]] CreateLane(WcsNet.Controllers.CreateLaneRequest) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:37:38.983 +08:00 [ERR] Failed executing DbCommand (44ms) [Parameters=[@p0='?' (DbType = DateTime), @p1='?' (DbType = Int64), @p2='?' (Size = 64), @p3='?' (Size = 64), @p4='?' (DbType = Int32), @p5='?' (Size = 255), @p6='?' (DbType = Int32), @p7='?' (DbType = SByte), @p8='?' (DbType = DateTime), @p9='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_lane` (`created_at`, `created_id`, `lane_code`, `lane_name`, `lane_type`, `Remark`, `sort_number`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9);
SELECT `Id`
FROM `wcs_lane`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:37:39.028 +08:00 [ERR] An exception occurred in the database while saving changes for context type 'WcsNet.Models.WcsDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> MySqlConnector.MySqlException (0x80004005): Unknown column 'lane_type' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> MySqlConnector.MySqlException (0x80004005): Unknown column 'lane_type' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-06-25 09:37:39.041 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:37:39.043 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.CreateLane (WcsNet) in 408.8249ms
2025-06-25 09:37:39.044 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:37:39.045 +08:00 [INF] 请求完成: POST /api/lanes - 状态码: 200 - 耗时: 415ms - 响应大小: 120bytes
2025-06-25 09:37:39.046 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/lanes - 200 null application/json; charset=utf-8 420.4863ms
2025-06-25 09:37:56.536 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/lanes - application/json 623
2025-06-25 09:37:56.539 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:37:56.541 +08:00 [INF] 请求开始: POST /api/lanes - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:37:56.548 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:37:56.549 +08:00 [INF] Route matched with {action = "CreateLane", controller = "Lane"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LaneDto]]] CreateLane(WcsNet.Controllers.CreateLaneRequest) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:38:11.577 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.CreateLane (WcsNet) in 15018.6763ms
2025-06-25 09:38:11.578 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:38:11.580 +08:00 [ERR] 请求处理异常: POST /api/lanes - 耗时: 15038ms
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method1081(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
2025-06-25 09:38:11.607 +08:00 [ERR] An unhandled exception has occurred while executing the request.
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method1081(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-25 09:38:11.631 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/lanes - 500 null text/plain; charset=utf-8 15094.5033ms
2025-06-25 09:38:17.508 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/lanes - application/json 623
2025-06-25 09:38:17.512 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:38:17.512 +08:00 [INF] 请求开始: POST /api/lanes - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:38:17.514 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:38:17.515 +08:00 [INF] Route matched with {action = "CreateLane", controller = "Lane"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LaneDto]]] CreateLane(WcsNet.Controllers.CreateLaneRequest) on controller WcsNet.Controllers.LaneController (WcsNet).
2025-06-25 09:38:32.533 +08:00 [INF] Executed action WcsNet.Controllers.LaneController.CreateLane (WcsNet) in 15014.5904ms
2025-06-25 09:38:32.535 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.LaneController.CreateLane (WcsNet)'
2025-06-25 09:38:32.537 +08:00 [ERR] 请求处理异常: POST /api/lanes - 耗时: 15024ms
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method1081(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
2025-06-25 09:38:32.551 +08:00 [ERR] An unhandled exception has occurred while executing the request.
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method1081(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-25 09:38:32.568 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/lanes - 500 null text/plain; charset=utf-8 15060.3458ms
2025-06-25 09:38:34.862 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:38:34.865 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:38:34.866 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:38:34.867 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DeptListDto]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:38:49.876 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 15007.1578ms
2025-06-25 09:38:49.878 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:38:49.879 +08:00 [ERR] 请求处理异常: GET /api/sys/deptlist - 耗时: 15014ms
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method32(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
2025-06-25 09:38:49.889 +08:00 [ERR] An unhandled exception has occurred while executing the request.
MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
 ---> MySqlConnector.MySqlException (0x80004005): Connect Timeout expired.
   at MySqlConnector.Core.ServerSession.OpenTcpSocketAsync(ConnectionSettings cs, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 1046
   at MySqlConnector.Core.ServerSession.ConnectAsync(ConnectionSettings cs, MySqlConnection connection, Int64 startingTimestamp, ILoadBalancer loadBalancer, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 425
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 932
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 938
   at MySqlConnector.MySqlConnection.CreateSessionAsync(ConnectionPool pool, Int64 startingTimestamp, Activity activity, Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 951
   at MySqlConnector.MySqlConnection.OpenAsync(Nullable`1 ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlConnection.cs:line 419
   at MySqlConnector.MySqlConnection.Open() in /_/src/MySqlConnector/MySqlConnection.cs:line 381
   at Microsoft.EntityFrameworkCore.ServerVersion.AutoDetect(String connectionString)
   at Program.<>c__DisplayClass0_0.<<Main>$>b__0(DbContextOptionsBuilder options) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 30
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass1_0`2.<AddDbContext>b__0(IServiceProvider _, DbContextOptionsBuilder b)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.CreateDbContextOptions[TContext](IServiceProvider applicationServiceProvider, Action`2 optionsAction)
   at Microsoft.Extensions.DependencyInjection.EntityFrameworkServiceCollectionExtensions.<>c__DisplayClass17_0`1.<AddCoreServices>b__0(IServiceProvider p)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at ResolveService(ILEmitResolverBuilderRuntimeContext, ServiceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method32(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at WcsNet.Middleware.AuthenticationMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\AuthenticationMiddleware.cs:line 39
   at WcsNet.Middleware.LoggingMiddleware.InvokeAsync(HttpContext context) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Middleware\LoggingMiddleware.cs:line 36
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-25 09:38:49.906 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 500 null text/plain; charset=utf-8 15043.8116ms
2025-06-25 09:44:16.940 +08:00 [INF] 使用内存存储服务
2025-06-25 09:46:24.444 +08:00 [INF] 使用内存存储服务
2025-06-25 09:46:27.272 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:46:27.291 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:46:27.592 +08:00 [INF] Executed DbCommand (86ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:46:27.610 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:46:27.684 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:46:27.737 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:46:27.743 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:46:27.753 +08:00 [INF] Hosting environment: Development
2025-06-25 09:46:27.753 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:47:10.128 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 09:47:10.192 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:10.199 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:47:10.202 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:10.224 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 09:47:10.824 +08:00 [ERR] Failed executing DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`Remark`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Status`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 09:47:10.854 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:47:10.877 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:10.957 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 726.627ms
2025-06-25 09:47:10.960 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:10.964 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 774ms - 响应大小: 74bytes
2025-06-25 09:47:10.981 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 855.2485ms
2025-06-25 09:47:15.275 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 09:47:15.283 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:15.285 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:15.287 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 09:47:15.629 +08:00 [ERR] Failed executing DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`Remark`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Status`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 09:47:15.635 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:47:15.659 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:15.662 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 370.2054ms
2025-06-25 09:47:15.664 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:15.666 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 382ms - 响应大小: 74bytes
2025-06-25 09:47:15.669 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 393.588ms
2025-06-25 09:47:21.490 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 09:47:21.619 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.057 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-25 09:47:22.066 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:22.335 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 09:47:22.107 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-25 09:47:22.107 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-25 09:47:22.129 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-25 09:47:22.329 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.091 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-25 09:47:22.383 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.389 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.398 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.423 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 94ms - 响应大小: 0bytes
2025-06-25 09:47:22.440 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.455 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.469 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:22.486 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.503 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 446.0822ms
2025-06-25 09:47:22.529 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.600 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 09:47:22.608 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:47:22.614 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 09:47:22.628 +08:00 [ERR] Failed executing DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`Remark`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Status`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 09:47:22.634 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-25 09:47:22.645 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 09:47:22.691 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:22.732 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:22.762 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Remark' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:47:22.787 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:22.825 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-25 09:47:22.834 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 102.9532ms
2025-06-25 09:47:22.836 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 175.666ms
2025-06-25 09:47:22.888 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:22.890 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 103.4094ms
2025-06-25 09:47:22.898 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:22.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.906 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.919 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 559.937ms
2025-06-25 09:47:22.921 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 09:47:22.929 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 09:47:22.931 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 533ms - 响应大小: 1102bytes
2025-06-25 09:47:22.935 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 552ms - 响应大小: 577bytes
2025-06-25 09:47:22.939 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:47:22.950 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 509ms - 响应大小: 800bytes
2025-06-25 09:47:22.957 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 09:47:22.965 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 836.3668ms
2025-06-25 09:47:22.975 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 868.248ms
2025-06-25 09:47:22.977 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 1357ms - 响应大小: 74bytes
2025-06-25 09:47:22.980 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 888.981ms
2025-06-25 09:47:22.987 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:23.006 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 1515.4034ms
2025-06-25 09:47:23.011 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 26.5836ms
2025-06-25 09:47:23.016 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 09:47:23.021 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 122ms - 响应大小: 570bytes
2025-06-25 09:47:23.023 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 198.4883ms
2025-06-25 09:47:23.227 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:47:23.280 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-25 09:47:23.414 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:23.452 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 09:47:23.523 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 09:47:23.544 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:23.549 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 4.5718ms
2025-06-25 09:47:23.558 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 09:47:23.562 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 147ms - 响应大小: 569bytes
2025-06-25 09:47:23.571 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 290.0498ms
2025-06-25 09:47:23.594 +08:00 [INF] Executed DbCommand (122ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='60']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:47:23.613 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:23.650 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 954.6346ms
2025-06-25 09:47:23.652 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:23.653 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 1263ms - 响应大小: 1266bytes
2025-06-25 09:47:23.657 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 1550.0511ms
2025-06-25 09:47:27.540 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 207
2025-06-25 09:47:27.544 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:47:27.552 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 09:47:27.558 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:47:27.891 +08:00 [INF] Executed DbCommand (98ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE `w`.`device_code` = @__request_DeviceCode_0)
2025-06-25 09:47:28.497 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='?' (Size = 15), @p1='?' (DbType = SByte), @p2='?' (DbType = DateTime), @p3='?' (DbType = Int64), @p4='?' (Size = 64), @p5='?' (Size = 64), @p6='?' (DbType = SByte), @p7='?' (DbType = SByte), @p8='?' (DbType = Int64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = SByte), @p12='?' (DbType = DateTime), @p13='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='60']
INSERT INTO `wcs_device` (`Addr`, `comm_type`, `created_at`, `created_id`, `device_code`, `device_name`, `device_type`, `Online`, `Port`, `Position`, `run_status`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
SELECT `Id`
FROM `wcs_device`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:47:28.541 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:28.552 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 991.8765ms
2025-06-25 09:47:28.553 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 09:47:28.554 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 200 - 耗时: 1010ms - 响应大小: 304bytes
2025-06-25 09:47:28.556 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 1015.8756ms
2025-06-25 09:47:32.362 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-25 09:47:32.366 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:32.368 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:32.369 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:47:32.596 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:47:32.647 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='60']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:47:32.650 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:32.651 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 280.7051ms
2025-06-25 09:47:32.652 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:32.653 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 287ms - 响应大小: 1266bytes
2025-06-25 09:47:32.655 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 293.0626ms
2025-06-25 09:47:42.035 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-25 09:47:42.037 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:42.038 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:42.038 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 09:47:42.287 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 09:47:42.377 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='60']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:47:42.378 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:47:42.379 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 339.2008ms
2025-06-25 09:47:42.379 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 09:47:42.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 342ms - 响应大小: 1266bytes
2025-06-25 09:47:42.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 345.3222ms
2025-06-25 09:47:42.551 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 203
2025-06-25 09:47:42.553 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:47:42.554 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:47:42.558 +08:00 [INF] Route matched with {action = "CreateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] CreateDeviceProperty(WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:47:42.773 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 09:47:42.786 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet) in 227.1224ms
2025-06-25 09:47:42.787 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:47:42.787 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 400 - 耗时: 234ms - 响应大小: 285bytes
2025-06-25 09:47:42.789 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 400 null application/problem+json; charset=utf-8 237.348ms
2025-06-25 09:47:48.907 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-25 09:47:48.909 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:47:48.909 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:47:48.911 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:47:48.913 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 09:47:49.266 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 09:47:49.343 +08:00 [ERR] Failed executing DbCommand (30ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`Sex`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 09:47:49.346 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Sex' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Sex' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:47:49.352 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:47:49.354 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 440.3514ms
2025-06-25 09:47:49.355 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:47:49.355 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 445ms - 响应大小: 71bytes
2025-06-25 09:47:49.357 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 450.2345ms
2025-06-25 09:48:00.832 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-25 09:48:00.834 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:48:00.834 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:48:00.836 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:48:00.836 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 09:48:01.101 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 09:48:01.133 +08:00 [ERR] Failed executing DbCommand (17ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`Sex`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 09:48:01.135 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Sex' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Sex' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:48:01.138 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:48:01.139 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 301.503ms
2025-06-25 09:48:01.140 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:48:01.140 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 305ms - 响应大小: 71bytes
2025-06-25 09:48:01.142 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 310.1824ms
2025-06-25 09:48:52.078 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 203
2025-06-25 09:48:52.082 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:48:52.086 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:48:52.087 +08:00 [INF] Route matched with {action = "CreateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] CreateDeviceProperty(WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:48:52.408 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 09:48:52.411 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet) in 319.5629ms
2025-06-25 09:48:52.412 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:48:52.413 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 400 - 耗时: 331ms - 响应大小: 285bytes
2025-06-25 09:48:52.415 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 400 null application/problem+json; charset=utf-8 336.4328ms
2025-06-25 09:49:37.870 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/acts/1 - null 0
2025-06-25 09:49:37.876 +08:00 [INF] 请求开始: DELETE /api/task/acts/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:49:37.879 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.DeleteAct (WcsNet)'
2025-06-25 09:49:37.883 +08:00 [INF] Route matched with {action = "DeleteAct", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteAct(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:49:38.353 +08:00 [INF] Executed DbCommand (73ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name`, `w`.`act_type`, `w`.`class_name`, `w`.`created_at`, `w`.`created_id`, `w`.`group_id`, `w`.`Level`, `w`.`method_name`, `w`.`Remark`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_actuator` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:49:38.356 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:49:38.358 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.DeleteAct (WcsNet) in 468.835ms
2025-06-25 09:49:38.359 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.DeleteAct (WcsNet)'
2025-06-25 09:49:38.361 +08:00 [INF] 请求完成: DELETE /api/task/acts/1 - 状态码: 200 - 耗时: 484ms - 响应大小: 51bytes
2025-06-25 09:49:38.366 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/acts/1 - 200 null application/json; charset=utf-8 495.7745ms
2025-06-25 09:49:51.231 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?page_size=2&page_no=1 - null null
2025-06-25 09:49:51.233 +08:00 [INF] 请求开始: GET /api/task/acts?page_size=2&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:49:51.235 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:49:51.239 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:49:51.473 +08:00 [INF] Executed DbCommand (58ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 09:49:51.553 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`class_name` AS `ClassName`, `w`.`method_name` AS `MethodName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 09:49:51.555 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:49:51.558 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 317.1226ms
2025-06-25 09:49:51.558 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 09:49:51.559 +08:00 [INF] 请求完成: GET /api/task/acts?page_size=2&page_no=1 - 状态码: 200 - 耗时: 325ms - 响应大小: 582bytes
2025-06-25 09:49:51.560 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?page_size=2&page_no=1 - 200 null application/json; charset=utf-8 328.7192ms
2025-06-25 09:50:57.177 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 209
2025-06-25 09:50:57.198 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:50:57.211 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:50:57.232 +08:00 [INF] Route matched with {action = "CreateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] CreateDeviceProperty(WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:50:57.498 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@p0='?' (Size = 64), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int64), @p3='?' (DbType = UInt64), @p4='?' (DbType = SByte), @p5='?' (DbType = SByte), @p6='?' (Size = 16), @p7='?' (Size = 64), @p8='?' (DbType = SByte), @p9='?' (Size = 64), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='60']
INSERT INTO `wcs_device_property` (`Addr`, `created_at`, `created_id`, `device_id`, `Direction`, `modbus_type`, `plc_type`, `prop_code`, `prop_length`, `prop_name`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
SELECT `Id`
FROM `wcs_device_property`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 09:50:57.546 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:50:57.552 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet) in 318.1774ms
2025-06-25 09:50:57.556 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 09:50:57.559 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 200 - 耗时: 360ms - 响应大小: 293bytes
2025-06-25 09:50:57.562 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 200 null application/json; charset=utf-8 384.3068ms
2025-06-25 09:51:38.245 +08:00 [INF] 使用内存存储服务
2025-06-25 09:51:40.225 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:51:40.284 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:51:40.470 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:51:40.572 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:51:40.946 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:51:40.992 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:51:41.011 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:51:41.018 +08:00 [INF] Hosting environment: Development
2025-06-25 09:51:41.019 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:52:32.283 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:52:32.356 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:52:32.362 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:52:32.366 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:52:32.400 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DeptListDto]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:52:32.933 +08:00 [INF] Executed DbCommand (84ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`dept_name` AS `Label`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:52:32.959 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DeptListDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:52:33.067 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 660.9933ms
2025-06-25 09:52:33.077 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:52:33.081 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 727ms - 响应大小: 318bytes
2025-06-25 09:52:33.106 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 825.3111ms
2025-06-25 09:52:48.444 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/property/8 - application/json 212
2025-06-25 09:52:49.026 +08:00 [INF] 请求开始: PUT /api/devices/property/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:52:49.051 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-25 09:52:49.063 +08:00 [INF] Route matched with {action = "UpdateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDeviceProperty(UInt64, WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 09:52:49.719 +08:00 [INF] Executed DbCommand (83ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 09:52:49.985 +08:00 [INF] Executed DbCommand (91ms) [Parameters=[@p7='?' (DbType = UInt64), @p0='?' (Size = 64), @p1='?' (DbType = SByte), @p2='?' (DbType = SByte), @p3='?' (Size = 16), @p4='?' (Size = 64), @p5='?' (DbType = SByte), @p6='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='60']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device_property` SET `Addr` = @p0, `Direction` = @p1, `modbus_type` = @p2, `plc_type` = @p3, `prop_code` = @p4, `prop_length` = @p5, `updated_at` = @p6
WHERE `Id` = @p7;
SELECT ROW_COUNT();
2025-06-25 09:52:49.999 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:52:50.002 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet) in 934.7745ms
2025-06-25 09:52:50.006 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-25 09:52:50.008 +08:00 [INF] 请求完成: PUT /api/devices/property/8 - 状态码: 200 - 耗时: 981ms - 响应大小: 48bytes
2025-06-25 09:52:50.010 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/property/8 - 200 null application/json; charset=utf-8 1566.4389ms
2025-06-25 09:53:04.208 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts/2 - null null
2025-06-25 09:53:04.212 +08:00 [INF] 请求开始: GET /api/task/acts/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:53:04.215 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetAct (WcsNet)'
2025-06-25 09:53:04.219 +08:00 [INF] Route matched with {action = "GetAct", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAct(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 09:53:04.628 +08:00 [INF] Executed DbCommand (89ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`class_name` AS `ClassName`, `w`.`method_name` AS `MethodName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`, COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP()) AS `UpdatedAt`
FROM `wcs_actuator` AS `w`
WHERE `w`.`Id` = @__id_0
LIMIT 1
2025-06-25 09:53:04.639 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:53:04.659 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetAct (WcsNet) in 431.5044ms
2025-06-25 09:53:04.661 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetAct (WcsNet)'
2025-06-25 09:53:04.662 +08:00 [INF] 请求完成: GET /api/task/acts/2 - 状态码: 200 - 耗时: 449ms - 响应大小: 293bytes
2025-06-25 09:53:04.664 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts/2 - 200 null application/json; charset=utf-8 455.5827ms
2025-06-25 09:56:03.670 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-25 09:56:03.673 +08:00 [INF] CORS policy execution successful.
2025-06-25 09:56:03.674 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:56:03.676 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:56:03.682 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 09:56:03.891 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 09:56:03.937 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 09:56:03.953 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 09:56:04.190 +08:00 [INF] Executed DbCommand (152ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 09:56:04.214 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 09:56:04.219 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 534.8424ms
2025-06-25 09:56:04.221 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 09:56:04.222 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 547ms - 响应大小: 453bytes
2025-06-25 09:56:04.225 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 554.8112ms
2025-06-25 09:56:04.232 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 09:56:04.241 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 09:56:04.244 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:56:04.250 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 09:56:04.408 +08:00 [ERR] Failed executing DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Status`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 09:56:04.445 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Status' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.Status' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 09:56:04.465 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:56:04.473 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 221.1357ms
2025-06-25 09:56:04.475 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 09:56:04.476 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 235ms - 响应大小: 74bytes
2025-06-25 09:56:04.480 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 247.1639ms
2025-06-25 09:59:19.282 +08:00 [INF] 使用内存存储服务
2025-06-25 09:59:20.549 +08:00 [INF] Executed DbCommand (104ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:59:20.558 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:59:20.741 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:59:20.747 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:59:20.814 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:59:20.864 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:59:20.868 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:59:20.869 +08:00 [INF] Hosting environment: Development
2025-06-25 09:59:20.870 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 10:00:27.451 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 10:00:27.515 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:00:27.524 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 10:00:27.527 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 10:00:27.547 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DeptListDto]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 10:00:27.809 +08:00 [INF] Executed DbCommand (29ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`dept_name` AS `Label`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 10:00:27.823 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DeptListDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:00:27.900 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 347.967ms
2025-06-25 10:00:27.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 10:00:27.906 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 393ms - 响应大小: 318bytes
2025-06-25 10:00:27.916 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 466.5593ms
2025-06-25 10:00:49.464 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 83
2025-06-25 10:00:49.471 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:00:49.480 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 10:00:49.492 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 10:00:49.760 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 10:00:49.917 +08:00 [INF] Executed DbCommand (79ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 10:00:49.990 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 10:00:50.121 +08:00 [INF] Executed DbCommand (86ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='60']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 10:00:50.136 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 10:00:50.140 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 646.0798ms
2025-06-25 10:00:50.141 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 10:00:50.142 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 671ms - 响应大小: 453bytes
2025-06-25 10:00:50.146 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 681.5359ms
2025-06-25 10:02:00.321 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 209
2025-06-25 10:02:00.325 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:02:00.328 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 10:02:00.334 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 10:02:00.594 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='60']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE `w`.`device_code` = @__request_DeviceCode_0)
2025-06-25 10:02:01.064 +08:00 [INF] Executed DbCommand (95ms) [Parameters=[@p0='?' (Size = 15), @p1='?' (DbType = SByte), @p2='?' (DbType = DateTime), @p3='?' (DbType = Int64), @p4='?' (Size = 64), @p5='?' (Size = 64), @p6='?' (DbType = SByte), @p7='?' (DbType = SByte), @p8='?' (DbType = Int64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = SByte), @p12='?' (DbType = DateTime), @p13='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='60']
INSERT INTO `wcs_device` (`Addr`, `comm_type`, `created_at`, `created_id`, `device_code`, `device_name`, `device_type`, `Online`, `Port`, `Position`, `run_status`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
SELECT `Id`
FROM `wcs_device`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 10:02:01.111 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 10:02:01.124 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 785.3212ms
2025-06-25 10:02:01.125 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 10:02:01.126 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 200 - 耗时: 801ms - 响应大小: 306bytes
2025-06-25 10:02:01.128 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 807.4398ms
2025-06-25 10:03:08.028 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 211
2025-06-25 10:03:08.031 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:03:08.037 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 10:03:08.043 +08:00 [INF] Route matched with {action = "CreateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] CreateDeviceProperty(WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 10:03:08.299 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[@p0='?' (Size = 64), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int64), @p3='?' (DbType = UInt64), @p4='?' (DbType = SByte), @p5='?' (DbType = SByte), @p6='?' (Size = 16), @p7='?' (Size = 64), @p8='?' (DbType = SByte), @p9='?' (Size = 64), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='60']
INSERT INTO `wcs_device_property` (`Addr`, `created_at`, `created_id`, `device_id`, `Direction`, `modbus_type`, `plc_type`, `prop_code`, `prop_length`, `prop_name`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
SELECT `Id`
FROM `wcs_device_property`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-25 10:03:08.329 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 10:03:08.335 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet) in 289.587ms
2025-06-25 10:03:08.337 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-25 10:03:08.338 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 200 - 耗时: 306ms - 响应大小: 296bytes
2025-06-25 10:03:08.340 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 200 null application/json; charset=utf-8 311.1242ms
2025-06-25 10:03:41.727 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/property/9 - application/json 220
2025-06-25 10:03:41.731 +08:00 [INF] 请求开始: PUT /api/devices/property/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:03:41.735 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-25 10:03:41.740 +08:00 [INF] Route matched with {action = "UpdateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDeviceProperty(UInt64, WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 10:03:42.072 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 10:03:42.173 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[@p8='?' (DbType = UInt64), @p0='?' (Size = 64), @p1='?' (DbType = SByte), @p2='?' (DbType = SByte), @p3='?' (Size = 16), @p4='?' (Size = 64), @p5='?' (DbType = SByte), @p6='?' (Size = 64), @p7='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='60']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device_property` SET `Addr` = @p0, `Direction` = @p1, `modbus_type` = @p2, `plc_type` = @p3, `prop_code` = @p4, `prop_length` = @p5, `prop_name` = @p6, `updated_at` = @p7
WHERE `Id` = @p8;
SELECT ROW_COUNT();
2025-06-25 10:03:42.177 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:03:42.179 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet) in 437.0304ms
2025-06-25 10:03:42.182 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-25 10:03:42.183 +08:00 [INF] 请求完成: PUT /api/devices/property/9 - 状态码: 200 - 耗时: 452ms - 响应大小: 48bytes
2025-06-25 10:03:42.185 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/property/9 - 200 null application/json; charset=utf-8 458.013ms
2025-06-25 10:04:26.619 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/task/acts/999 - null 0
2025-06-25 10:04:26.622 +08:00 [INF] 请求开始: DELETE /api/task/acts/999 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:04:26.627 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.DeleteAct (WcsNet)'
2025-06-25 10:04:26.633 +08:00 [INF] Route matched with {action = "DeleteAct", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteAct(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 10:04:26.904 +08:00 [INF] Executed DbCommand (29ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name`, `w`.`act_type`, `w`.`class_name`, `w`.`created_at`, `w`.`created_id`, `w`.`group_id`, `w`.`Level`, `w`.`method_name`, `w`.`Remark`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_actuator` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 10:04:26.908 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:04:26.910 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.DeleteAct (WcsNet) in 273.1161ms
2025-06-25 10:04:26.912 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.DeleteAct (WcsNet)'
2025-06-25 10:04:26.914 +08:00 [INF] 请求完成: DELETE /api/task/acts/999 - 状态码: 200 - 耗时: 291ms - 响应大小: 51bytes
2025-06-25 10:04:26.918 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/task/acts/999 - 200 null application/json; charset=utf-8 298.878ms
2025-06-25 10:05:03.279 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?page_size=2&page_no=1 - null null
2025-06-25 10:05:03.282 +08:00 [INF] 请求开始: GET /api/task/acts?page_size=2&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:05:03.284 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 10:05:03.290 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 10:05:03.517 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='60']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 10:05:03.595 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`class_name` AS `ClassName`, `w`.`method_name` AS `MethodName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 10:05:03.601 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:05:03.623 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 330.8838ms
2025-06-25 10:05:03.627 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 10:05:03.632 +08:00 [INF] 请求完成: GET /api/task/acts?page_size=2&page_no=1 - 状态码: 200 - 耗时: 349ms - 响应大小: 582bytes
2025-06-25 10:05:03.635 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?page_size=2&page_no=1 - 200 null application/json; charset=utf-8 356.9068ms
2025-06-25 10:05:49.203 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts/2 - null null
2025-06-25 10:05:49.208 +08:00 [INF] 请求开始: GET /api/task/acts/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:05:49.221 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetAct (WcsNet)'
2025-06-25 10:05:49.234 +08:00 [INF] Route matched with {action = "GetAct", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAct(UInt64) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 10:05:49.440 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`class_name` AS `ClassName`, `w`.`method_name` AS `MethodName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`, COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP()) AS `UpdatedAt`
FROM `wcs_actuator` AS `w`
WHERE `w`.`Id` = @__id_0
LIMIT 1
2025-06-25 10:05:49.444 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:05:49.448 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetAct (WcsNet) in 201.0772ms
2025-06-25 10:05:49.449 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetAct (WcsNet)'
2025-06-25 10:05:49.450 +08:00 [INF] 请求完成: GET /api/task/acts/2 - 状态码: 200 - 耗时: 241ms - 响应大小: 293bytes
2025-06-25 10:05:49.452 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts/2 - 200 null application/json; charset=utf-8 248.7266ms
2025-06-25 10:06:21.482 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/apilogs/999 - null 0
2025-06-25 10:06:21.485 +08:00 [INF] 请求开始: DELETE /api/apilogs/999 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 10:06:21.487 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.ApiLogController.DeleteApiLog (WcsNet)'
2025-06-25 10:06:21.500 +08:00 [INF] Route matched with {action = "DeleteApiLog", controller = "ApiLog"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteApiLog(UInt64) on controller WcsNet.Controllers.ApiLogController (WcsNet).
2025-06-25 10:06:21.980 +08:00 [ERR] Failed executing DbCommand (108ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='60']
SELECT `w`.`Id`, `w`.`api_name`, `w`.`client_ip`, `w`.`created_at`, `w`.`created_id`, `w`.`Method`, `w`.`request_body`, `w`.`response_body`, `w`.`response_time`, `w`.`Status`, `w`.`status_code`, `w`.`updated_at`, `w`.`updated_id`, `w`.`Url`, `w`.`user_agent`, `w`.`user_id`, `w`.`work_id`
FROM `wcs_api_log` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 10:06:22.015 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 'w.client_ip' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 'w.client_ip' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-25 10:06:22.026 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 10:06:22.029 +08:00 [INF] Executed action WcsNet.Controllers.ApiLogController.DeleteApiLog (WcsNet) in 526.5007ms
2025-06-25 10:06:22.030 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.ApiLogController.DeleteApiLog (WcsNet)'
2025-06-25 10:06:22.032 +08:00 [INF] 请求完成: DELETE /api/apilogs/999 - 状态码: 200 - 耗时: 547ms - 响应大小: 77bytes
2025-06-25 10:06:22.036 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/apilogs/999 - 200 null application/json; charset=utf-8 554.1435ms
