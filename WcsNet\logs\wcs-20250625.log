2025-06-25 08:43:50.425 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/dept/deptlist - null null
2025-06-25 08:43:50.469 +08:00 [INF] 请求开始: GET /api/dept/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:43:50.473 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:43:50.474 +08:00 [INF] 请求完成: GET /api/dept/deptlist - 状态码: 404 - 耗时: 6ms - 响应大小: 0bytes
2025-06-25 08:43:50.478 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/dept/deptlist - 404 0 null 54.2998ms
2025-06-25 08:43:50.481 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/dept/deptlist, Response status code: 404
2025-06-25 08:44:49.688 +08:00 [INF] 使用内存存储服务
2025-06-25 08:44:50.963 +08:00 [INF] Executed DbCommand (21ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 08:44:50.970 +08:00 [INF] 数据库和表创建成功
2025-06-25 08:44:51.271 +08:00 [INF] Executed DbCommand (103ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:44:51.276 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 08:44:51.344 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 08:44:51.381 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 08:44:51.383 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 08:44:51.383 +08:00 [INF] Hosting environment: Development
2025-06-25 08:44:51.384 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 08:45:12.717 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:45:12.751 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:45:12.756 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:45:12.757 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:45:12.769 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:45:13.014 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:45:13.022 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:45:13.068 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 296.7985ms
2025-06-25 08:45:13.070 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:45:13.072 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 321ms - 响应大小: 324bytes
2025-06-25 08:45:13.079 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 362.7808ms
2025-06-25 08:46:06.838 +08:00 [INF] 使用内存存储服务
2025-06-25 08:46:08.348 +08:00 [INF] Executed DbCommand (55ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 08:46:08.355 +08:00 [INF] 数据库和表创建成功
2025-06-25 08:46:08.536 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:46:08.542 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 08:46:08.615 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 08:46:08.656 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 08:46:08.658 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 08:46:08.659 +08:00 [INF] Hosting environment: Development
2025-06-25 08:46:08.659 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 08:46:31.896 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:46:31.939 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 08:46:31.942 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 08:46:31.944 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:46:31.955 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:46:32.256 +08:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:46:32.263 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:46:32.310 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 352.2429ms
2025-06-25 08:46:32.312 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:46:32.313 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 375ms - 响应大小: 318bytes
2025-06-25 08:46:32.320 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 424.6381ms
2025-06-25 08:47:13.685 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 2
2025-06-25 08:47:13.692 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:13.694 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:13.703 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:13.714 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(WcsNet.DTOs.RefreshTokenRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:13.974 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:47:13.989 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 273.3709ms
2025-06-25 08:47:13.989 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:13.990 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 296ms - 响应大小: 246bytes
2025-06-25 08:47:13.992 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 307.1018ms
2025-06-25 08:47:14.287 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-25 08:47:14.292 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:14.293 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-25 08:47:14.295 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 8.5251ms
2025-06-25 08:47:14.298 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-25 08:47:24.247 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 2
2025-06-25 08:47:24.251 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:24.252 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:24.255 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:24.257 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(WcsNet.DTOs.RefreshTokenRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:24.390 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:47:24.391 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 130.0536ms
2025-06-25 08:47:24.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-25 08:47:24.393 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 141ms - 响应大小: 246bytes
2025-06-25 08:47:24.395 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 148.0213ms
2025-06-25 08:47:32.346 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-25 08:47:32.351 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:32.352 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:32.357 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 08:47:32.362 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-25 08:47:32.591 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 08:47:32.812 +08:00 [INF] Executed DbCommand (88ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 08:47:32.887 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-25 08:47:32.967 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-25 08:47:32.992 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:32.996 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 631.3103ms
2025-06-25 08:47:32.997 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-25 08:47:32.998 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 645ms - 响应大小: 449bytes
2025-06-25 08:47:33.000 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 653.6943ms
2025-06-25 08:47:33.007 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-25 08:47:33.010 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.013 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 08:47:33.018 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-25 08:47:33.428 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`Component`, `s`.`created_at`, `s`.`created_id`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`Icon`, `s`.`Keepalive`, `s`.`Name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`Rank`, `s`.`Redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`Title`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`Rank`, `s`.`Id`
2025-06-25 08:47:33.479 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:33.492 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 471.9074ms
2025-06-25 08:47:33.493 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-25 08:47:33.495 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 483ms - 响应大小: 6882bytes
2025-06-25 08:47:33.497 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 489.9974ms
2025-06-25 08:47:33.633 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-25 08:47:33.636 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.645 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 9ms - 响应大小: 0bytes
2025-06-25 08:47:33.649 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 15.9669ms
2025-06-25 08:47:33.658 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-25 08:47:33.659 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-25 08:47:33.663 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.665 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.668 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.673 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 08:47:33.676 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.678 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.678 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.682 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetAlarms(Int32, Int32, System.Nullable`1[System.Int32], System.Nullable`1[System.UInt32]) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-25 08:47:33.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 26.184ms
2025-06-25 08:47:33.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 22.2004ms
2025-06-25 08:47:33.708 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.709 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.710 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 41ms - 响应大小: 577bytes
2025-06-25 08:47:33.710 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 46ms - 响应大小: 801bytes
2025-06-25 08:47:33.712 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 53.1434ms
2025-06-25 08:47:33.715 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 56.5169ms
2025-06-25 08:47:33.833 +08:00 [INF] Executed DbCommand (46ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `log_alarm` AS `l`
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-25 08:47:33.967 +08:00 [INF] Executed DbCommand (43ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `l`.`Id`, `l`.`alarm_type` AS `AlarmType`, `l`.`Category`, `l`.`Code`, `l`.`device_id` AS `DeviceId`, `l`.`Remark`, COALESCE(`l`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `log_alarm` AS `l`
ORDER BY `l`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:33.969 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.970 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.972 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:33.979 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.976 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.978 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.975 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:33.981 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.982 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.983 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-25 08:47:33.985 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.987 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.988 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:33.992 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.6704ms
2025-06-25 08:47:33.992 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 299.6864ms
2025-06-25 08:47:33.993 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 7.2126ms
2025-06-25 08:47:33.994 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 6.3601ms
2025-06-25 08:47:33.995 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-25 08:47:33.996 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-25 08:47:33.997 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.998 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-25 08:47:33.999 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 29ms - 响应大小: 1103bytes
2025-06-25 08:47:33.999 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 334ms - 响应大小: 1236bytes
2025-06-25 08:47:34.000 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 27ms - 响应大小: 570bytes
2025-06-25 08:47:34.001 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 30ms - 响应大小: 573bytes
2025-06-25 08:47:34.002 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 35.5732ms
2025-06-25 08:47:34.004 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 345.4294ms
2025-06-25 08:47:34.009 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 42.1341ms
2025-06-25 08:47:34.010 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 43.729ms
2025-06-25 08:47:37.890 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 08:47:37.892 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:37.894 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:37.898 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:47:38.336 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:47:38.356 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:38.368 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 467.0663ms
2025-06-25 08:47:38.373 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.375 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 482ms - 响应大小: 347bytes
2025-06-25 08:47:38.377 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 486.9104ms
2025-06-25 08:47:38.383 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - null null
2025-06-25 08:47:38.384 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:38.385 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.386 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:47:38.569 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:47:38.580 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:38.582 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 194.5045ms
2025-06-25 08:47:38.584 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:47:38.587 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/2 - 状态码: 200 - 耗时: 202ms - 响应大小: 544bytes
2025-06-25 08:47:38.591 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - 200 null application/json; charset=utf-8 207.4004ms
2025-06-25 08:47:38.597 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:47:38.602 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:38.611 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:38.624 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:38.896 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:47:38.969 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:38.974 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:38.988 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 349.108ms
2025-06-25 08:47:38.991 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:38.992 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 390ms - 响应大小: 1561bytes
2025-06-25 08:47:38.996 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 398.7865ms
2025-06-25 08:47:43.773 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/10 - application/json 121
2025-06-25 08:47:43.776 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:47:43.778 +08:00 [INF] 请求开始: PUT /api/devices/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:43.780 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 08:47:43.786 +08:00 [INF] Route matched with {action = "UpdateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDevice(UInt64, WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:44.035 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-25 08:47:44.209 +08:00 [INF] Executed DbCommand (78ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 64), @__id_1='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE (`w`.`device_code` = @__request_DeviceCode_0) AND (`w`.`Id` <> @__id_1))
2025-06-25 08:47:44.392 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[@p1='?' (DbType = UInt64), @p0='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device` SET `updated_at` = @p0
WHERE `Id` = @p1;
SELECT ROW_COUNT();
2025-06-25 08:47:44.413 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:47:44.416 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet) in 628.1695ms
2025-06-25 08:47:44.417 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-25 08:47:44.418 +08:00 [INF] 请求完成: PUT /api/devices/10 - 状态码: 200 - 耗时: 640ms - 响应大小: 48bytes
2025-06-25 08:47:44.421 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/10 - 200 null application/json; charset=utf-8 647.8281ms
2025-06-25 08:47:44.446 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:47:44.448 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:47:44.451 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:44.452 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:47:44.744 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:47:44.802 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:47:44.807 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:47:44.808 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 351.802ms
2025-06-25 08:47:44.809 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:47:44.810 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 362ms - 响应大小: 1561bytes
2025-06-25 08:47:44.812 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 366.0879ms
2025-06-25 08:48:01.038 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 121
2025-06-25 08:48:01.040 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:48:01.041 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:01.043 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:01.049 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:01.309 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:48:01.313 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 263.086ms
2025-06-25 08:48:01.314 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:01.316 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 400 - 耗时: 274ms - 响应大小: 381bytes
2025-06-25 08:48:01.317 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 400 null application/problem+json; charset=utf-8 278.9731ms
2025-06-25 08:48:17.990 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 121
2025-06-25 08:48:17.993 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:48:17.994 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:17.997 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:18.001 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:18.284 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-25 08:48:18.298 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 291.3991ms
2025-06-25 08:48:18.323 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-25 08:48:18.340 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 400 - 耗时: 345ms - 响应大小: 381bytes
2025-06-25 08:48:18.353 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 400 null application/problem+json; charset=utf-8 362.497ms
2025-06-25 08:48:48.213 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - null null
2025-06-25 08:48:48.216 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.218 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.222 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:48.456 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:48.459 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:48.461 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 199.6799ms
2025-06-25 08:48:48.462 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.464 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/7 - 状态码: 200 - 耗时: 248ms - 响应大小: 268bytes
2025-06-25 08:48:48.466 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - 200 null application/json; charset=utf-8 252.7738ms
2025-06-25 08:48:48.477 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - null null
2025-06-25 08:48:48.480 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.482 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.483 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:48.741 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:48.743 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:48.745 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 258.7605ms
2025-06-25 08:48:48.747 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.748 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/8 - 状态码: 200 - 耗时: 268ms - 响应大小: 431bytes
2025-06-25 08:48:48.750 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - 200 null application/json; charset=utf-8 272.6623ms
2025-06-25 08:48:48.756 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - null null
2025-06-25 08:48:48.758 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:48.760 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:48.787 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:49.331 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:49.333 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:49.335 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 544.1288ms
2025-06-25 08:48:49.336 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.337 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/9 - 状态码: 200 - 耗时: 578ms - 响应大小: 1014bytes
2025-06-25 08:48:49.339 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - 200 null application/json; charset=utf-8 582.391ms
2025-06-25 08:48:49.658 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-25 08:48:49.661 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:49.662 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.663 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:48:49.887 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:48:49.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:49.891 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 226.1678ms
2025-06-25 08:48:49.892 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:48:49.893 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 231ms - 响应大小: 347bytes
2025-06-25 08:48:49.895 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 236.6732ms
2025-06-25 08:48:49.915 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-25 08:48:49.917 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:49.919 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:48:49.920 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:50.287 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-25 08:48:50.356 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:48:50.359 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:48:50.360 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 438.419ms
2025-06-25 08:48:50.361 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-25 08:48:50.363 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 445ms - 响应大小: 1561bytes
2025-06-25 08:48:50.365 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 449.4592ms
2025-06-25 08:48:50.384 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - null null
2025-06-25 08:48:50.390 +08:00 [INF] 请求开始: GET /api/devices/props/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:50.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:50.397 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:50.632 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:50.635 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:50.639 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 239.6751ms
2025-06-25 08:48:50.640 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:50.641 +08:00 [INF] 请求完成: GET /api/devices/props/10 - 状态码: 200 - 耗时: 251ms - 响应大小: 36bytes
2025-06-25 08:48:50.642 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - 200 null application/json; charset=utf-8 257.9688ms
2025-06-25 08:48:55.526 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - null null
2025-06-25 08:48:55.529 +08:00 [INF] 请求开始: GET /api/devices/props/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:55.531 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:55.532 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:55.770 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:55.772 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:55.774 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 240.4474ms
2025-06-25 08:48:55.775 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:55.776 +08:00 [INF] 请求完成: GET /api/devices/props/10 - 状态码: 200 - 耗时: 246ms - 响应大小: 36bytes
2025-06-25 08:48:55.779 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/10 - 200 null application/json; charset=utf-8 252.8028ms
2025-06-25 08:48:57.889 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/3 - null null
2025-06-25 08:48:57.895 +08:00 [INF] 请求开始: GET /api/devices/props/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:48:57.899 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:57.900 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:48:58.270 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:48:58.280 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:48:58.282 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 378.9311ms
2025-06-25 08:48:58.283 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:48:58.284 +08:00 [INF] 请求完成: GET /api/devices/props/3 - 状态码: 200 - 耗时: 388ms - 响应大小: 36bytes
2025-06-25 08:48:58.286 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/3 - 200 null application/json; charset=utf-8 397.4155ms
2025-06-25 08:49:01.959 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/6 - null null
2025-06-25 08:49:01.961 +08:00 [INF] 请求开始: GET /api/devices/props/6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:01.963 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:01.964 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:02.277 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:02.279 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:02.280 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 308.8275ms
2025-06-25 08:49:02.280 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:02.281 +08:00 [INF] 请求完成: GET /api/devices/props/6 - 状态码: 200 - 耗时: 319ms - 响应大小: 36bytes
2025-06-25 08:49:02.282 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/6 - 200 null application/json; charset=utf-8 323.0865ms
2025-06-25 08:49:08.972 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-25 08:49:08.975 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:08.981 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:08.986 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:09.283 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:09.287 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:09.290 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 298.7381ms
2025-06-25 08:49:09.291 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:09.292 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 316ms - 响应大小: 1399bytes
2025-06-25 08:49:09.294 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 321.7362ms
2025-06-25 08:49:12.593 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - null 0
2025-06-25 08:49:12.595 +08:00 [INF] CORS policy execution successful.
2025-06-25 08:49:12.596 +08:00 [INF] 请求开始: DELETE /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:12.598 +08:00 [INF] 请求完成: DELETE /api/devices/property/7 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-25 08:49:12.599 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - 404 0 null 5.7579ms
2025-06-25 08:49:12.603 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: DELETE http://localhost:8666/api/devices/property/7, Response status code: 404
2025-06-25 08:49:52.738 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-25 08:49:52.740 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:49:52.743 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:52.743 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-25 08:49:52.962 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, '' AS `Remark`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-25 08:49:52.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:49:52.972 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 227.0148ms
2025-06-25 08:49:52.974 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-25 08:49:52.982 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 241ms - 响应大小: 1399bytes
2025-06-25 08:49:52.988 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 249.3906ms
2025-06-25 08:50:17.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - null null
2025-06-25 08:50:17.394 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.396 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:50:17.396 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-25 08:50:17.521 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:17.524 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.526 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:17.530 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:17.531 +08:00 [INF] Executed DbCommand (33ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:50:17.533 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.535 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 135.8412ms
2025-06-25 08:50:17.538 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-25 08:50:17.542 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/11 - 状态码: 200 - 耗时: 147ms - 响应大小: 362bytes
2025-06-25 08:50:17.545 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - 200 null application/json; charset=utf-8 153.3984ms
2025-06-25 08:50:17.555 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups - null null
2025-06-25 08:50:17.558 +08:00 [INF] 请求开始: GET /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.560 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 08:50:17.564 +08:00 [INF] Route matched with {action = "GetActGroups", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActGroups(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:17.684 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:17.762 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:17.765 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.772 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 240.1804ms
2025-06-25 08:50:17.774 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:17.774 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 250ms - 响应大小: 488bytes
2025-06-25 08:50:17.776 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 255.0351ms
2025-06-25 08:50:17.780 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator_group` AS `w`
2025-06-25 08:50:17.901 +08:00 [INF] Executed DbCommand (70ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`group_name` AS `GroupName`, `w`.`group_type` AS `GroupType`, `w`.`sort_number` AS `SortNumber`, `w`.`Remark`, `w`.`Status`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator_group` AS `w`
ORDER BY `w`.`sort_number`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:17.904 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:17.911 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActGroups (WcsNet) in 345.5823ms
2025-06-25 08:50:17.912 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActGroups (WcsNet)'
2025-06-25 08:50:17.913 +08:00 [INF] 请求完成: GET /api/task/actgroups - 状态码: 200 - 耗时: 355ms - 响应大小: 964bytes
2025-06-25 08:50:17.914 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups - 200 null application/json; charset=utf-8 359.6658ms
2025-06-25 08:50:17.931 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - null null
2025-06-25 08:50:17.933 +08:00 [INF] 请求开始: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:17.935 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 08:50:17.941 +08:00 [INF] Route matched with {action = "GetActs", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetActs(System.String, System.Nullable`1[System.UInt64], Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:18.240 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_actuator` AS `w`
2025-06-25 08:50:18.300 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`act_name` AS `ActName`, `w`.`group_id` AS `GroupId`, COALESCE((
    SELECT `w0`.`group_name`
    FROM `wcs_actuator_group` AS `w0`
    WHERE `w0`.`Id` = `w`.`group_id`
    LIMIT 1), '') AS `GroupName`, `w`.`act_type` AS `ActType`, `w`.`Level`, `w`.`Remark`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()) AS `CreatedAt`
FROM `wcs_actuator` AS `w`
ORDER BY `w`.`Level`, `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:18.303 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:18.308 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetActs (WcsNet) in 366.0142ms
2025-06-25 08:50:18.309 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetActs (WcsNet)'
2025-06-25 08:50:18.310 +08:00 [INF] 请求完成: GET /api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 377ms - 响应大小: 1738bytes
2025-06-25 08:50:18.312 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/acts?act_name=&group_id=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 380.6009ms
2025-06-25 08:50:25.009 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:25.011 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:25.013 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:25.014 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:25.379 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:25.454 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:25.457 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:25.458 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 441.6666ms
2025-06-25 08:50:25.459 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:25.460 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 449ms - 响应大小: 488bytes
2025-06-25 08:50:25.462 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 453.3592ms
2025-06-25 08:50:29.326 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-25 08:50:29.328 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:50:29.330 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:29.331 +08:00 [INF] Route matched with {action = "GetPaths", controller = "Task"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetPaths(Int32, Int32) on controller WcsNet.Controllers.TaskController (WcsNet).
2025-06-25 08:50:29.650 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_path_define` AS `w`
2025-06-25 08:50:29.707 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`path_name`, `w`.`start_point`, `w`.`end_point`, `w`.`transit_time`, `w`.`block_time`, COALESCE(`w`.`created_at`, CURRENT_TIMESTAMP()), COALESCE(`w`.`updated_at`, CURRENT_TIMESTAMP())
FROM `wcs_path_define` AS `w`
ORDER BY `w`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:50:29.708 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:50:29.709 +08:00 [INF] Executed action WcsNet.Controllers.TaskController.GetPaths (WcsNet) in 376.2081ms
2025-06-25 08:50:29.710 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.TaskController.GetPaths (WcsNet)'
2025-06-25 08:50:29.711 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 382ms - 响应大小: 488bytes
2025-06-25 08:50:29.713 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 386.3435ms
2025-06-25 08:54:18.349 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 08:54:18.351 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:18.352 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:54:18.352 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 08:54:18.782 +08:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`dept_name` AS `label`, `s`.`parent_id` AS `parentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 08:54:18.783 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:54:18.784 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 430.6627ms
2025-06-25 08:54:18.785 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 08:54:18.786 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 435ms - 响应大小: 318bytes
2025-06-25 08:54:18.788 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 438.5125ms
2025-06-25 08:54:20.556 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-25 08:54:20.556 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-25 08:54:20.560 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:20.562 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-25 08:54:20.566 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 08:54:20.569 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 08:54:20.583 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.UserPagedResponse]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-25 08:54:20.584 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-25 08:54:20.824 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 08:54:20.830 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 08:54:20.831 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-25 08:54:20.875 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, CAST(`s`.`Status` AS char) AS `Status`, CASE
    WHEN `s`.`Gender` = 1 THEN '男'
    WHEN `s`.`Gender` = 2 THEN '女'
    ELSE '未知'
END AS `Gender`, `s`.`dept_id` AS `DeptId`, COALESCE(`s`.`created_at`, CURRENT_TIMESTAMP()) AS `CreateTime`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-25 08:54:20.880 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.UserPagedResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-25 08:54:20.886 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 297.4895ms
2025-06-25 08:54:20.888 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-25 08:54:20.889 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 329ms - 响应大小: 390bytes
2025-06-25 08:54:20.891 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 334.6593ms
2025-06-25 08:54:20.964 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id` AS `id`, `s`.`role_name` AS `name`, `s`.`role_code` AS `code`
FROM `sys_role` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`
2025-06-25 08:54:20.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 08:54:20.970 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 379.7722ms
2025-06-25 08:54:20.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 08:54:20.972 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 410ms - 响应大小: 206bytes
2025-06-25 08:54:20.975 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 418.5151ms
2025-06-25 09:01:15.182 +08:00 [INF] 使用内存存储服务
2025-06-25 09:01:16.348 +08:00 [INF] Executed DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-25 09:01:16.360 +08:00 [INF] 数据库和表创建成功
2025-06-25 09:01:16.576 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-25 09:01:16.593 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-25 09:01:16.663 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-25 09:01:16.704 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-25 09:01:16.728 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 09:01:16.729 +08:00 [INF] Hosting environment: Development
2025-06-25 09:01:16.730 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-25 09:01:42.105 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-25 09:01:42.138 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:01:42.142 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-25 09:01:42.143 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:01:42.163 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Department]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-25 09:01:42.580 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-25 09:01:42.624 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Department, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:01:42.673 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 507.2167ms
2025-06-25 09:01:42.675 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-25 09:01:42.676 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 538ms - 响应大小: 1245bytes
2025-06-25 09:01:42.684 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 580.1193ms
2025-06-25 09:01:54.201 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-25 09:01:54.207 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-25 09:01:54.208 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 09:01:54.212 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.Models.Role]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-25 09:01:54.711 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_at`, `s`.`created_id`, `s`.`Perms`, `s`.`Remark`, `s`.`role_code`, `s`.`role_name`, `s`.`role_type`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_role` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`
2025-06-25 09:01:54.724 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Role, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-25 09:01:54.728 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 513.8285ms
2025-06-25 09:01:54.728 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-25 09:01:54.729 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 522ms - 响应大小: 999bytes
2025-06-25 09:01:54.730 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 528.768ms
