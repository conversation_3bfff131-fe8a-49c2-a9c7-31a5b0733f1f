using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_role")]
public class Role : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("role_name")]
    public string RoleName { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("role_code")]
    public string RoleCode { get; set; } = string.Empty;

    [Column("role_type")]
    public sbyte RoleType { get; set; } = 1;

    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;

    public sbyte Status { get; set; } = 1;

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;

    [StringLength(255)]
    public string Perms { get; set; } = string.Empty;
}
