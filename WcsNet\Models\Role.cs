using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

/// <summary>
/// 角色实体（与Go版本Role保持一致）
/// </summary>
[Table("sys_role")]
public class Role
{
    /// <summary>
    /// 角色ID
    /// </summary>
    [Key]
    public uint Id { get; set; }

    /// <summary>
    /// 角色名称
    /// </summary>
    [Column("role_name")]
    [StringLength(32)]
    public string RoleName { get; set; } = string.Empty;

    /// <summary>
    /// 角色编码
    /// </summary>
    [Column("role_code")]
    [StringLength(32)]
    public string RoleCode { get; set; } = string.Empty;

    /// <summary>
    /// 角色类型 1 系统管理员 2 系统普通角色 3普通角色
    /// </summary>
    [Column("role_type")]
    public sbyte? RoleType { get; set; } = 3;

    /// <summary>
    /// 排序值
    /// </summary>
    [Column("sort_number")]
    public int SortNumber { get; set; }

    /// <summary>
    /// 状态 1启用 2停用
    /// </summary>
    [Column("status")]
    public sbyte Status { get; set; } = 1;

    /// <summary>
    /// 备注
    /// </summary>
    [Column("remark")]
    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 权限id,用半角逗号分隔
    /// </summary>
    [Column("perms")]
    [StringLength(1024)]
    public string Perms { get; set; } = string.Empty;
}
