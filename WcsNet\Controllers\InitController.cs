using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;
using WcsNet.Configuration;

namespace WcsNet.Controllers;

/// <summary>
/// 数据库初始化控制器
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class InitController : ControllerBase
{
    private readonly WcsDbContext _context;
    private readonly UserService _userService;
    private readonly IConfiguration _configuration;

    public InitController(WcsDbContext context, UserService userService, IConfiguration configuration)
    {
        _context = context;
        _userService = userService;
        _configuration = configuration;
    }

    /// <summary>
    /// 检查数据库状态
    /// </summary>
    /// <returns>数据库状态信息</returns>
    [HttpGet("status")]
    public async Task<ActionResult<ApiResponse<object>>> GetDatabaseStatus()
    {
        try
        {
            var userCount = await _context.Users.CountAsync();
            var roleCount = await _context.Roles.CountAsync();
            var menuCount = await _context.Permissions.CountAsync();
            var deptCount = await _context.Departments.CountAsync();
            var dictCount = await _context.Dicts.CountAsync();

            var status = new
            {
                users = userCount,
                roles = roleCount,
                menus = menuCount,
                departments = deptCount,
                dicts = dictCount,
                initialized = userCount > 0 && roleCount > 0 && menuCount > 0 && dictCount > 0
            };

            return Ok(ApiResponse<object>.Success(status));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 初始化数据库数据
    /// </summary>
    /// <returns>初始化结果</returns>
    [HttpPost("database")]
    public async Task<ActionResult<ApiResponse<string>>> InitializeDatabase()
    {
        try
        {
            var results = new List<string>();

            // 初始化角色数据
            var roleCount = await _context.Roles.CountAsync();
            if (roleCount == 0)
            {
                await InitializeRoleDataAsync();
                results.Add("已初始化角色数据");
            }

            // 初始化菜单数据
            var menuCount = await _context.Permissions.CountAsync();
            if (menuCount == 0)
            {
                await InitializeMenuDataAsync();
                results.Add("已初始化菜单数据");
            }

            // 初始化部门数据
            var deptCount = await _context.Departments.CountAsync();
            if (deptCount == 0)
            {
                await InitializeDepartmentDataAsync();
                results.Add("已初始化部门数据");
            }

            // 初始化字典数据
            var dictCount = await _context.Dicts.CountAsync();
            if (dictCount == 0)
            {
                await InitializeDictDataAsync();
                results.Add("已初始化字典数据");
            }

            // 初始化默认用户
            var userCount = await _context.Users.CountAsync();
            if (userCount == 0)
            {
                await InitializeUserDataAsync();
                results.Add("已创建默认用户: admin/123456");
            }

            if (results.Count == 0)
            {
                results.Add("数据库已经初始化，无需重复操作");
            }

            return Ok(ApiResponse<string>.Success(string.Join("; ", results)));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 重置数据库（清空所有数据并重新初始化）
    /// </summary>
    /// <returns>重置结果</returns>
    [HttpPost("reset")]
    public async Task<ActionResult<ApiResponse<string>>> ResetDatabase()
    {
        try
        {
            // 清空所有数据
            _context.Users.RemoveRange(_context.Users);
            _context.Roles.RemoveRange(_context.Roles);
            _context.Permissions.RemoveRange(_context.Permissions);
            _context.Departments.RemoveRange(_context.Departments);
            _context.DictValues.RemoveRange(_context.DictValues);
            _context.Dicts.RemoveRange(_context.Dicts);
            await _context.SaveChangesAsync();

            // 重新初始化
            await InitializeRoleDataAsync();
            await InitializeMenuDataAsync();
            await InitializeDepartmentDataAsync();
            await InitializeDictDataAsync();
            await InitializeUserDataAsync();

            return Ok(ApiResponse<string>.Success("数据库已重置并重新初始化"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 初始化角色数据
    /// </summary>
    private async Task InitializeRoleDataAsync()
    {
        var roles = new List<Role>
        {
            new()
            {
                Id = 1,
                RoleName = "超级管理员",
                RoleCode = "admin",
                RoleType = 1,
                SortNumber = 0,
                Status = 1,
                Remark = "系统超级管理员",
                Perms = "1,300,301,302,303,304"
            }
        };

        _context.Roles.AddRange(roles);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 初始化菜单数据
    /// </summary>
    private async Task InitializeMenuDataAsync()
    {
        var permissions = new List<Permission>
        {
            // 首页
            new()
            {
                Id = 1,
                PermType = 0,
                Title = "menus.pureHome",
                ParentId = 0,
                RoutePath = "/welcome",
                Name = "Welcome",
                Component = "welcome",
                Redirect = "",
                Rank = 0,
                Icon = "ep:home-filled",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            },
            // 系统管理
            new()
            {
                Id = 300,
                PermType = 0,
                Title = "系统管理",
                ParentId = 0,
                RoutePath = "/system",
                Name = "System",
                Component = "",
                Redirect = "",
                Rank = 80,
                Icon = "ri:settings-3-line",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            },
            // 用户管理
            new()
            {
                Id = 301,
                PermType = 0,
                Title = "用户管理",
                ParentId = 300,
                RoutePath = "/system/user/index",
                Name = "SystemUser",
                Component = "system/user/index",
                Redirect = "",
                Rank = 0,
                Icon = "ri:admin-line",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            },
            // 角色管理
            new()
            {
                Id = 302,
                PermType = 0,
                Title = "角色管理",
                ParentId = 300,
                RoutePath = "/system/role/index",
                Name = "SystemRole",
                Component = "system/role/index",
                Redirect = "",
                Rank = 0,
                Icon = "ri:admin-fill",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            },
            // 菜单管理
            new()
            {
                Id = 303,
                PermType = 0,
                Title = "菜单管理",
                ParentId = 300,
                RoutePath = "/system/menu/index",
                Name = "SystemMenu",
                Component = "system/menu/index",
                Redirect = "",
                Rank = 0,
                Icon = "ep:menu",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            },
            // 部门管理
            new()
            {
                Id = 304,
                PermType = 0,
                Title = "部门管理",
                ParentId = 300,
                RoutePath = "/system/dept/index",
                Name = "SystemDept",
                Component = "system/dept/index",
                Redirect = "",
                Rank = 0,
                Icon = "ri:git-branch-line",
                ExtraIcon = "",
                ActivePath = "",
                FrameSrc = "",
                FrameLoading = 1,
                ShowLink = 1,
                HideTag = 0,
                Keepalive = 0,
                FixedTag = 0,
                ShowParent = 0,
                PermCode = ""
            }
        };

        _context.Permissions.AddRange(permissions);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 初始化部门数据
    /// </summary>
    private async Task InitializeDepartmentDataAsync()
    {
        var departments = new List<Department>
        {
            new()
            {
                Id = 1,
                DeptName = "总公司",
                ParentId = 0,
                SortNumber = 0,
                Status = 1,
                Brief = "总公司"
            }
        };

        _context.Departments.AddRange(departments);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 初始化用户数据
    /// </summary>
    private async Task InitializeUserDataAsync()
    {
        var appConfig = _configuration.GetSection("App").Get<AppConfig>() ?? new AppConfig();
        
        var defaultUser = new User
        {
            Account = "admin",
            Password = UserService.HashPassword("123456", appConfig.Secret.Salt, false),
            Realname = "管理员",
            Email = "<EMAIL>",
            Mobile = "***********",
            Status = 1,
            RoleId = 1,
            DeptId = 1
        };
        
        _context.Users.Add(defaultUser);
        await _context.SaveChangesAsync();
    }

    /// <summary>
    /// 初始化字典数据
    /// </summary>
    private async Task InitializeDictDataAsync()
    {
        // 创建字典
        var dicts = new List<Dict>
        {
            new()
            {
                Id = 1,
                DictCode = "user_status",
                DictName = "用户状态",
                Status = 1,
                Remark = "用户状态字典"
            },
            new()
            {
                Id = 2,
                DictCode = "role_type",
                DictName = "角色类型",
                Status = 1,
                Remark = "角色类型字典"
            },
            new()
            {
                Id = 3,
                DictCode = "menu_type",
                DictName = "菜单类型",
                Status = 1,
                Remark = "菜单类型字典"
            }
        };

        _context.Dicts.AddRange(dicts);
        await _context.SaveChangesAsync();

        // 创建字典值
        var dictValues = new List<DictValue>
        {
            // 用户状态字典值
            new()
            {
                Id = 1,
                DictId = 1,
                DictLabel = "启用",
                DictVal = "1",
                SortNumber = 1,
                Status = 1
            },
            new()
            {
                Id = 2,
                DictId = 1,
                DictLabel = "停用",
                DictVal = "0",
                SortNumber = 2,
                Status = 1
            },
            // 角色类型字典值
            new()
            {
                Id = 3,
                DictId = 2,
                DictLabel = "系统管理员",
                DictVal = "1",
                SortNumber = 1,
                Status = 1
            },
            new()
            {
                Id = 4,
                DictId = 2,
                DictLabel = "系统普通角色",
                DictVal = "2",
                SortNumber = 2,
                Status = 1
            },
            new()
            {
                Id = 5,
                DictId = 2,
                DictLabel = "普通角色",
                DictVal = "3",
                SortNumber = 3,
                Status = 1
            },
            // 菜单类型字典值
            new()
            {
                Id = 6,
                DictId = 3,
                DictLabel = "菜单",
                DictVal = "0",
                SortNumber = 1,
                Status = 1
            },
            new()
            {
                Id = 7,
                DictId = 3,
                DictLabel = "iframe",
                DictVal = "1",
                SortNumber = 2,
                Status = 1
            },
            new()
            {
                Id = 8,
                DictId = 3,
                DictLabel = "外链",
                DictVal = "2",
                SortNumber = 3,
                Status = 1
            },
            new()
            {
                Id = 9,
                DictId = 3,
                DictLabel = "按钮",
                DictVal = "3",
                SortNumber = 4,
                Status = 1
            }
        };

        _context.DictValues.AddRange(dictValues);
        await _context.SaveChangesAsync();
    }
}
