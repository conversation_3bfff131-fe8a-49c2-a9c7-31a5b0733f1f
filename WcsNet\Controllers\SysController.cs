using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    private readonly PermissionService _permissionService;
    private readonly WcsDbContext _context;

    public SysController(PermissionService permissionService, WcsDbContext context)
    {
        _permissionService = permissionService;
        _context = context;
    }

    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetRoleMenus()
    {
        try
        {
            // 从JWT token中获取用户角色ID，如果没有则默认为1（超级管理员）
            var roleIdClaim = User.FindFirst("role_id")?.Value;
            var roleId = int.TryParse(roleIdClaim, out var id) ? id : 1;

            // 从数据库获取用户角色对应的菜单
            var permissions = await _permissionService.GetRoleMenuListAsync(roleId);
            var menuDtos = _permissionService.ConvertToMenuDtos(permissions);
            var menus = _permissionService.BuildMenuTree(menuDtos);

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取登录日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="username">用户名（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>登录日志列表</returns>
    [HttpGet("logs/loginlog")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<LoginLogDto>>>> GetLoginLogs(
        int page_size = 10,
        int page_no = 1,
        string? username = null,
        int? status = null)
    {
        try
        {
            // 从数据库获取登录日志数据
            var query = _context.LoginLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(username))
            {
                query = query.Where(l => l.Username.Contains(username));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var total = await query.CountAsync();
            var loginLogs = await query
                .OrderByDescending(l => l.LoginTime)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new LoginLogDto
                {
                    Id = (int)l.Id,
                    Username = l.Username,
                    LoginIp = l.LoginIp,
                    LoginLocation = l.LoginLocation,
                    Browser = l.Browser,
                    Os = l.Os,
                    Status = l.Status,
                    Message = l.Message,
                    LoginTime = l.LoginTime
                })
                .ToListAsync();

            var response = new SimplePagedResponse<LoginLogDto>
            {
                List = loginLogs,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<LoginLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<LoginLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取操作日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="module">模块</param>
    /// <param name="status">状态</param>
    /// <returns>操作日志列表</returns>
    [HttpGet("logs/operation")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<OperationLogDto>>>> GetOperationLogs(
        int page_size = 10,
        int page_no = 1,
        string? module = null,
        int? status = null)
    {
        try
        {
            // 从数据库获取操作日志数据
            var query = _context.OperationLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(module))
            {
                query = query.Where(l => l.Module.Contains(module));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var total = await query.CountAsync();
            var logs = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new OperationLogDto
                {
                    Id = (int)l.Id,
                    OperatorName = l.OperatorName,
                    Username = l.Username,
                    Module = l.Module,
                    Operation = l.Operation,
                    Description = l.Description,
                    Location = l.Location,
                    OperationIp = l.OperationIp,
                    IpAddress = l.IpAddress,
                    OperationSystem = l.OperationSystem,
                    BrowserType = l.BrowserType,
                    UserAgent = l.UserAgent,
                    OperationStatus = l.OperationStatus,
                    Status = l.Status,
                    ExecutionTime = l.ExecutionTime,
                    CreateTime = l.CreatedAt ?? DateTime.Now,
                    OperationTime = l.OperationTime
                })
                .ToListAsync();

            var response = new SimplePagedResponse<OperationLogDto>
            {
                List = logs,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<OperationLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<OperationLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取部门列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>部门列表</returns>
    [HttpGet("depts")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<DeptDto>>>> GetDepts(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取部门数据
            var total = await _context.Departments.CountAsync();
            var departments = await _context.Departments
                .OrderBy(d => d.SortNumber)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(d => new DeptDto
                {
                    Id = (int)d.Id,
                    DeptName = d.DeptName,
                    DeptCode = d.DeptCode,
                    ParentId = (int)d.ParentId,
                    Sort = d.SortNumber,
                    Status = d.Status,
                    CreateTime = d.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new SimplePagedResponse<DeptDto>
            {
                List = departments,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<DeptDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<DeptDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取简易部门列表
    /// </summary>
    /// <returns>部门列表</returns>
    [HttpGet("deptlist")]
    public async Task<ActionResult<ApiResponse<List<DeptDto>>>> GetDeptList()
    {
        try
        {
            // 从数据库获取简易部门数据
            var depts = await _context.Departments
                .OrderBy(d => d.SortNumber)
                .Select(d => new DeptDto
                {
                    Id = (int)d.Id,
                    DeptName = d.DeptName,
                    DeptCode = d.DeptCode,
                    ParentId = (int)d.ParentId,
                    Sort = d.SortNumber,
                    Status = d.Status,
                    CreateTime = d.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            return Ok(ApiResponse<List<DeptDto>>.Success(depts));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<DeptDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <param name="request">部门创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("depts")]
    public async Task<ActionResult<ApiResponse<DeptDto>>> CreateDept([FromBody] CreateDeptRequest request)
    {
        try
        {
            var department = new Department
            {
                DeptName = request.DeptName,
                DeptCode = request.DeptCode,
                ParentId = (ulong)request.ParentId,
                SortNumber = request.Sort,
                Status = 1, // 启用状态
                CreatedAt = DateTime.Now
            };

            _context.Departments.Add(department);
            await _context.SaveChangesAsync();

            var deptDto = new DeptDto
            {
                Id = (int)department.Id,
                DeptName = department.DeptName,
                DeptCode = department.DeptCode,
                ParentId = (int)department.ParentId,
                Sort = department.SortNumber,
                Status = department.Status,
                CreateTime = department.CreatedAt ?? DateTime.Now
            };

            return Ok(ApiResponse<DeptDto>.Success(deptDto));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DeptDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <param name="request">部门更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("depts/{id}")]
    public ActionResult<ApiResponse<object>> UpdateDept(int id, [FromBody] UpdateDeptRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "部门更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("depts/{id}")]
    public ActionResult<ApiResponse<object>> DeleteDept(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "部门删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取菜单列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>菜单列表</returns>
    [HttpGet("menus")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<SystemMenuDto>>>> GetMenus(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取菜单数据（权限表中的菜单类型数据）
            var total = await _context.Permissions.Where(p => p.PermType < 3).CountAsync();
            var permissions = await _context.Permissions
                .Where(p => p.PermType < 3) // 菜单类型
                .OrderBy(p => p.Rank)
                .ThenBy(p => p.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(p => new SystemMenuDto
                {
                    Id = (int)p.Id,
                    MenuName = p.Title,
                    MenuType = p.PermType == 1 ? "menu" : "button",
                    Path = p.RoutePath,
                    Component = p.Component,
                    ParentId = (int)p.ParentId,
                    Sort = p.Rank,
                    Status = p.ShowLink == 1 ? 1 : 0,
                    Visible = p.ShowLink == 1,
                    CreateTime = DateTime.Now
                })
                .ToListAsync();

            var response = new SimplePagedResponse<SystemMenuDto>
            {
                List = permissions,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<SystemMenuDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<SystemMenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取菜单列表（简化版）
    /// </summary>
    /// <returns>菜单列表</returns>
    [HttpGet("menulist")]
    public async Task<ActionResult<ApiResponse<List<SystemMenuDto>>>> GetMenuList()
    {
        try
        {
            // 从数据库获取菜单数据
            var menus = await _context.Permissions
                .Where(p => p.PermType < 3) // 菜单类型
                .OrderBy(p => p.Rank)
                .ThenBy(p => p.Id)
                .Select(p => new SystemMenuDto
                {
                    Id = (int)p.Id,
                    MenuName = p.Title,
                    MenuType = p.PermType == 1 ? "menu" : "button",
                    Path = p.RoutePath,
                    Component = p.Component,
                    ParentId = (int)p.ParentId,
                    Sort = p.Rank,
                    Status = p.ShowLink == 1 ? 1 : 0,
                    Visible = p.ShowLink == 1,
                    CreateTime = DateTime.Now
                })
                .ToListAsync();

            return Ok(ApiResponse<List<SystemMenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<SystemMenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建菜单
    /// </summary>
    /// <param name="request">菜单创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("menus")]
    public ActionResult<ApiResponse<SystemMenuDto>> CreateMenu([FromBody] CreateMenuRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var menu = new SystemMenuDto
            {
                Id = newId,
                MenuName = request.MenuName,
                MenuType = request.MenuType,
                Path = request.Path,
                Component = request.Component,
                ParentId = request.ParentId,
                Sort = request.Sort,
                Status = 1,
                Visible = true,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<SystemMenuDto>.Success(menu));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SystemMenuDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <param name="request">菜单更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("menus/{id}")]
    public ActionResult<ApiResponse<object>> UpdateMenu(int id, [FromBody] UpdateMenuRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "菜单更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("menus/{id}")]
    public ActionResult<ApiResponse<object>> DeleteMenu(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "菜单删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>角色列表</returns>
    [HttpGet("roles")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<RoleDto>>>> GetRoles(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取角色数据
            var total = await _context.Roles.CountAsync();
            var roles = await _context.Roles
                .OrderBy(r => r.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(r => new RoleDto
                {
                    Id = (int)r.Id,
                    RoleName = r.RoleName,
                    RoleCode = r.RoleCode,
                    Status = r.Status,
                    Remark = r.Remark,
                    CreateTime = r.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new SimplePagedResponse<RoleDto>
            {
                List = roles,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<RoleDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<RoleDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取所有角色列表
    /// </summary>
    /// <returns>角色列表</returns>
    [HttpGet("roles/all")]
    public ActionResult<ApiResponse<List<RoleDto>>> GetAllRoles()
    {
        try
        {
            var roles = new List<RoleDto>
            {
                new RoleDto { Id = 1, RoleName = "超级管理员", RoleCode = "admin", Status = 1, Remark = "系统管理员" },
                new RoleDto { Id = 2, RoleName = "普通用户", RoleCode = "user", Status = 1, Remark = "普通用户" },
                new RoleDto { Id = 4, RoleName = "操作员1", RoleCode = "operator1", Status = 1, Remark = "操作员1" }
            };

            return Ok(ApiResponse<List<RoleDto>>.Success(roles));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<RoleDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="request">角色创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("roles")]
    public ActionResult<ApiResponse<RoleDto>> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var role = new RoleDto
            {
                Id = newId,
                RoleName = request.RoleName,
                RoleCode = request.RoleCode,
                Status = 1,
                Remark = request.Remark,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<RoleDto>.Success(role));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<RoleDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">角色更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("roles/{id}")]
    public ActionResult<ApiResponse<object>> UpdateRole(int id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "角色更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("roles/{id}")]
    public ActionResult<ApiResponse<object>> DeleteRole(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "角色删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 修改角色状态
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="status">状态</param>
    /// <returns>修改结果</returns>
    [HttpPut("roles/{id}/status/{status}")]
    public ActionResult<ApiResponse<object>> ChangeRoleStatus(int id, int status)
    {
        try
        {
            // 模拟状态修改操作
            return Ok(ApiResponse<object>.Success(new { message = "角色状态修改成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取字典列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>字典列表</returns>
    [HttpGet("dicts")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<DictDto>>>> GetDicts(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取字典数据
            var total = await _context.Dicts.CountAsync();
            var dicts = await _context.Dicts
                .OrderBy(d => d.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(d => new DictDto
                {
                    Id = (int)d.Id,
                    DictName = d.DictName,
                    DictType = d.DictType,
                    Status = d.Status,
                    Remark = d.Remark,
                    CreateTime = d.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new SimplePagedResponse<DictDto>
            {
                List = dicts,
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<DictDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<DictDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典
    /// </summary>
    /// <param name="request">字典创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("dicts")]
    public ActionResult<ApiResponse<DictDto>> CreateDict([FromBody] CreateDictRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var dict = new DictDto
            {
                Id = newId,
                DictName = request.DictName,
                DictType = request.DictType,
                Status = 1,
                Remark = request.Remark,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<DictDto>.Success(dict));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DictDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <param name="request">字典更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("dicts/{id}")]
    public ActionResult<ApiResponse<object>> UpdateDict(int id, [FromBody] UpdateDictRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "字典更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dicts/{id}")]
    public ActionResult<ApiResponse<object>> DeleteDict(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "字典删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取角色菜单权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>菜单ID列表</returns>
    [HttpGet("roles/menus/{id}")]
    public ActionResult<ApiResponse<List<int>>> GetRoleMenuIds(int id)
    {
        try
        {
            // 模拟角色菜单权限数据
            var menuIds = new List<int> { 1, 2, 3, 4, 5 };
            return Ok(ApiResponse<List<int>>.Success(menuIds));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 设置角色菜单权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">菜单ID列表</param>
    /// <returns>设置结果</returns>
    [HttpPut("roles/menus/{id}")]
    public ActionResult<ApiResponse<object>> SaveRoleMenus(int id, [FromBody] RoleMenuRequest request)
    {
        try
        {
            // 模拟保存角色菜单权限
            return Ok(ApiResponse<object>.Success(new { message = "角色菜单权限设置成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }


}
