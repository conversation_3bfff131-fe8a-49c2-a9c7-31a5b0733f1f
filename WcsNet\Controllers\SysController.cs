using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;
using System.Security.Claims;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    private readonly PermissionService _permissionService;

    public SysController(PermissionService permissionService)
    {
        _permissionService = permissionService;
    }

    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetRoleMenus()
    {
        try
        {
            // 从JWT token中获取用户角色ID，如果没有则默认为1（超级管理员）
            var roleIdClaim = User.FindFirst("role_id")?.Value;
            var roleId = int.TryParse(roleIdClaim, out var id) ? id : 1;

            // 从数据库获取用户角色对应的菜单
            var permissions = await _permissionService.GetRoleMenuListAsync(roleId);
            var menuDtos = _permissionService.ConvertToMenuDtos(permissions);
            var menus = _permissionService.BuildMenuTree(menuDtos);

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取登录日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="username">用户名（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>登录日志列表</returns>
    [HttpGet("logs/loginlog")]
    public ActionResult<ApiResponse<PagedResponse<LoginLogDto>>> GetLoginLogs(
        int page_size = 10,
        int page_no = 1,
        string? username = null,
        int? status = null)
    {
        try
        {
            // 模拟登录日志数据
            var loginLogs = new List<LoginLogDto>();
            var random = new Random();
            var usernames = new[] { "admin", "user1", "user2", "test", "manager" };
            var browsers = new[] { "Chrome", "Firefox", "Safari", "Edge" };
            var oses = new[] { "Windows 10", "Windows 11", "macOS", "Linux" };
            var locations = new[] { "北京", "上海", "广州", "深圳", "杭州" };

            for (int i = 1; i <= page_size; i++)
            {
                var logStatus = random.Next(0, 10) < 8 ? 1 : 0; // 80%成功，20%失败
                var user = usernames[random.Next(usernames.Length)];

                loginLogs.Add(new LoginLogDto
                {
                    Id = i,
                    Username = user,
                    LoginIp = $"192.168.1.{random.Next(1, 255)}",
                    LoginLocation = locations[random.Next(locations.Length)],
                    Browser = browsers[random.Next(browsers.Length)],
                    Os = oses[random.Next(oses.Length)],
                    Status = logStatus,
                    Message = logStatus == 1 ? "登录成功" : "密码错误",
                    LoginTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            // 如果有筛选条件，进行过滤
            if (!string.IsNullOrEmpty(username))
            {
                loginLogs = loginLogs.Where(l => l.Username.Contains(username)).ToList();
            }

            if (status.HasValue)
            {
                loginLogs = loginLogs.Where(l => l.Status == status.Value).ToList();
            }

            var response = new PagedResponse<LoginLogDto>
            {
                List = loginLogs,
                Total = 1000 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<LoginLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<LoginLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取操作日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="module">模块</param>
    /// <param name="status">状态</param>
    /// <returns>操作日志列表</returns>
    [HttpGet("logs/operation")]
    public ActionResult<ApiResponse<PagedResponse<OperationLogDto>>> GetOperationLogs(
        int page_size = 10,
        int page_no = 1,
        string? module = null,
        int? status = null)
    {
        try
        {
            // 模拟操作日志数据
            var logs = new List<OperationLogDto>();
            var random = new Random();
            var modules = new[] { "用户管理", "设备管理", "任务管理", "系统设置", "权限管理" };
            var operations = new[] { "创建", "更新", "删除", "查询", "导出" };
            var usernames = new[] { "admin", "operator", "manager", "user1", "user2" };

            for (int i = 1; i <= page_size; i++)
            {
                var moduleType = modules[random.Next(modules.Length)];
                var operation = operations[random.Next(operations.Length)];
                var username = usernames[random.Next(usernames.Length)];

                logs.Add(new OperationLogDto
                {
                    Id = i,
                    Username = username,
                    Module = moduleType,
                    Operation = operation,
                    Description = $"{username}在{moduleType}模块执行了{operation}操作",
                    IpAddress = $"192.168.1.{random.Next(1, 255)}",
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    Status = random.Next(0, 2),
                    ExecutionTime = random.Next(10, 1000),
                    OperationTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            var response = new PagedResponse<OperationLogDto>
            {
                List = logs,
                Total = 500 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<OperationLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<OperationLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取部门列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>部门列表</returns>
    [HttpGet("depts")]
    public ActionResult<ApiResponse<PagedResponse<DeptDto>>> GetDepts(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟部门数据
            var depts = new List<DeptDto>();
            var random = new Random();
            var deptNames = new[] { "技术部", "运营部", "财务部", "人事部", "市场部" };

            for (int i = 1; i <= page_size; i++)
            {
                depts.Add(new DeptDto
                {
                    Id = i,
                    DeptName = deptNames[random.Next(deptNames.Length)],
                    DeptCode = $"DEPT_{i:D3}",
                    ParentId = i > 3 ? random.Next(1, 4) : 0,
                    Sort = i,
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 365))
                });
            }

            var response = new PagedResponse<DeptDto>
            {
                List = depts,
                Total = 50 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<DeptDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<DeptDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取简易部门列表
    /// </summary>
    /// <returns>部门列表</returns>
    [HttpGet("deptlist")]
    public ActionResult<ApiResponse<List<DeptDto>>> GetDeptList()
    {
        try
        {
            // 模拟简易部门数据
            var depts = new List<DeptDto>
            {
                new DeptDto { Id = 1, DeptName = "技术部", DeptCode = "TECH", ParentId = 0, Sort = 1, Status = 1 },
                new DeptDto { Id = 2, DeptName = "运营部", DeptCode = "OPS", ParentId = 0, Sort = 2, Status = 1 },
                new DeptDto { Id = 3, DeptName = "财务部", DeptCode = "FIN", ParentId = 0, Sort = 3, Status = 1 },
                new DeptDto { Id = 4, DeptName = "开发组", DeptCode = "DEV", ParentId = 1, Sort = 1, Status = 1 },
                new DeptDto { Id = 5, DeptName = "测试组", DeptCode = "QA", ParentId = 1, Sort = 2, Status = 1 }
            };

            return Ok(ApiResponse<List<DeptDto>>.Success(depts));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<DeptDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <param name="request">部门创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("depts")]
    public ActionResult<ApiResponse<DeptDto>> CreateDept([FromBody] CreateDeptRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var dept = new DeptDto
            {
                Id = newId,
                DeptName = request.DeptName,
                DeptCode = request.DeptCode,
                ParentId = request.ParentId,
                Sort = request.Sort,
                Status = 1, // 启用状态
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<DeptDto>.Success(dept));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DeptDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <param name="request">部门更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("depts/{id}")]
    public ActionResult<ApiResponse<object>> UpdateDept(int id, [FromBody] UpdateDeptRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "部门更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("depts/{id}")]
    public ActionResult<ApiResponse<object>> DeleteDept(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "部门删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取菜单列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>菜单列表</returns>
    [HttpGet("menus")]
    public ActionResult<ApiResponse<PagedResponse<SystemMenuDto>>> GetMenus(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟菜单数据
            var menus = new List<SystemMenuDto>();
            var random = new Random();
            var menuNames = new[] { "/device", "/tasktpl", "/work", "/repo", "/system", "/monitor" };
            var menuTitles = new[] { "设备管理", "任务模板", "工作管理", "仓库管理", "系统管理", "监控管理" };

            for (int i = 0; i < menuNames.Length; i++)
            {
                menus.Add(new SystemMenuDto
                {
                    Id = i + 1,
                    MenuName = menuTitles[i],
                    MenuType = "menu",
                    Path = menuNames[i],
                    Component = menuNames[i],
                    ParentId = 0,
                    Sort = i + 1,
                    Status = 1,
                    Visible = true,
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30))
                });
            }

            var response = new PagedResponse<SystemMenuDto>
            {
                List = menus,
                Total = menus.Count
            };

            return Ok(ApiResponse<PagedResponse<SystemMenuDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<SystemMenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取菜单列表（简化版）
    /// </summary>
    /// <returns>菜单列表</returns>
    [HttpGet("menulist")]
    public ActionResult<ApiResponse<List<SystemMenuDto>>> GetMenuList()
    {
        try
        {
            // 模拟菜单数据
            var menus = new List<SystemMenuDto>
            {
                new SystemMenuDto { Id = 1, MenuName = "设备管理", MenuType = "menu", Path = "/device", Component = "/device", ParentId = 0, Sort = 1, Status = 1, Visible = true },
                new SystemMenuDto { Id = 2, MenuName = "任务模板", MenuType = "menu", Path = "/tasktpl", Component = "/tasktpl", ParentId = 0, Sort = 2, Status = 1, Visible = true },
                new SystemMenuDto { Id = 3, MenuName = "工作管理", MenuType = "menu", Path = "/work", Component = "/work", ParentId = 0, Sort = 3, Status = 1, Visible = true },
                new SystemMenuDto { Id = 4, MenuName = "仓库管理", MenuType = "menu", Path = "/repo", Component = "/repo", ParentId = 0, Sort = 4, Status = 1, Visible = true },
                new SystemMenuDto { Id = 5, MenuName = "系统管理", MenuType = "menu", Path = "/system", Component = "/system", ParentId = 0, Sort = 5, Status = 1, Visible = true },
                new SystemMenuDto { Id = 6, MenuName = "监控管理", MenuType = "menu", Path = "/monitor", Component = "/monitor", ParentId = 0, Sort = 6, Status = 1, Visible = true }
            };

            return Ok(ApiResponse<List<SystemMenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<SystemMenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建菜单
    /// </summary>
    /// <param name="request">菜单创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("menus")]
    public ActionResult<ApiResponse<SystemMenuDto>> CreateMenu([FromBody] CreateMenuRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var menu = new SystemMenuDto
            {
                Id = newId,
                MenuName = request.MenuName,
                MenuType = request.MenuType,
                Path = request.Path,
                Component = request.Component,
                ParentId = request.ParentId,
                Sort = request.Sort,
                Status = 1,
                Visible = true,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<SystemMenuDto>.Success(menu));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SystemMenuDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <param name="request">菜单更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("menus/{id}")]
    public ActionResult<ApiResponse<object>> UpdateMenu(int id, [FromBody] UpdateMenuRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "菜单更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("menus/{id}")]
    public ActionResult<ApiResponse<object>> DeleteMenu(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "菜单删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>角色列表</returns>
    [HttpGet("roles")]
    public ActionResult<ApiResponse<PagedResponse<RoleDto>>> GetRoles(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟角色数据
            var roles = new List<RoleDto>
            {
                new RoleDto { Id = 1, RoleName = "超级管理员", RoleCode = "admin", Status = 1, Remark = "系统管理员", CreateTime = DateTime.Now.AddDays(-30) },
                new RoleDto { Id = 2, RoleName = "普通用户", RoleCode = "user", Status = 1, Remark = "普通用户", CreateTime = DateTime.Now.AddDays(-20) },
                new RoleDto { Id = 4, RoleName = "操作员1", RoleCode = "operator1", Status = 1, Remark = "操作员1", CreateTime = DateTime.Now.AddDays(-10) }
            };

            var response = new PagedResponse<RoleDto>
            {
                List = roles,
                Total = roles.Count
            };

            return Ok(ApiResponse<PagedResponse<RoleDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<RoleDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取所有角色列表
    /// </summary>
    /// <returns>角色列表</returns>
    [HttpGet("roles/all")]
    public ActionResult<ApiResponse<List<RoleDto>>> GetAllRoles()
    {
        try
        {
            var roles = new List<RoleDto>
            {
                new RoleDto { Id = 1, RoleName = "超级管理员", RoleCode = "admin", Status = 1, Remark = "系统管理员" },
                new RoleDto { Id = 2, RoleName = "普通用户", RoleCode = "user", Status = 1, Remark = "普通用户" },
                new RoleDto { Id = 4, RoleName = "操作员1", RoleCode = "operator1", Status = 1, Remark = "操作员1" }
            };

            return Ok(ApiResponse<List<RoleDto>>.Success(roles));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<RoleDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="request">角色创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("roles")]
    public ActionResult<ApiResponse<RoleDto>> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var role = new RoleDto
            {
                Id = newId,
                RoleName = request.RoleName,
                RoleCode = request.RoleCode,
                Status = 1,
                Remark = request.Remark,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<RoleDto>.Success(role));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<RoleDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">角色更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("roles/{id}")]
    public ActionResult<ApiResponse<object>> UpdateRole(int id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "角色更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("roles/{id}")]
    public ActionResult<ApiResponse<object>> DeleteRole(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "角色删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 修改角色状态
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="status">状态</param>
    /// <returns>修改结果</returns>
    [HttpPut("roles/{id}/status/{status}")]
    public ActionResult<ApiResponse<object>> ChangeRoleStatus(int id, int status)
    {
        try
        {
            // 模拟状态修改操作
            return Ok(ApiResponse<object>.Success(new { message = "角色状态修改成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取字典列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>字典列表</returns>
    [HttpGet("dicts")]
    public ActionResult<ApiResponse<PagedResponse<DictDto>>> GetDicts(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟字典数据
            var dicts = new List<DictDto>
            {
                new DictDto { Id = 2, DictName = "通信协议", DictType = "protocol", Status = 1, Remark = "通信协议", CreateTime = DateTime.Now.AddDays(-30) },
                new DictDto { Id = 3, DictName = "设备类型", DictType = "device_type", Status = 1, Remark = "设备类型", CreateTime = DateTime.Now.AddDays(-25) },
                new DictDto { Id = 4, DictName = "任务状态", DictType = "task_status", Status = 1, Remark = "任务状态", CreateTime = DateTime.Now.AddDays(-20) },
                new DictDto { Id = 5, DictName = "", DictType = "", Status = 1, Remark = "", CreateTime = DateTime.Now.AddDays(-15) },
                new DictDto { Id = 6, DictName = "", DictType = "", Status = 1, Remark = "", CreateTime = DateTime.Now.AddDays(-10) },
                new DictDto { Id = 7, DictName = "WCS与PLC通信协议", DictType = "wcs_plc_protocol", Status = 1, Remark = "WCS与PLC通信协议", CreateTime = DateTime.Now.AddDays(-5) },
                new DictDto { Id = 8, DictName = "", DictType = "", Status = 1, Remark = "", CreateTime = DateTime.Now.AddDays(-3) },
                new DictDto { Id = 9, DictName = "", DictType = "", Status = 1, Remark = "", CreateTime = DateTime.Now.AddDays(-2) },
                new DictDto { Id = 10, DictName = "执行器分组字典", DictType = "executor_group", Status = 1, Remark = "执行器分组字典", CreateTime = DateTime.Now.AddDays(-1) },
                new DictDto { Id = 11, DictName = "", DictType = "", Status = 1, Remark = "", CreateTime = DateTime.Now }
            };

            var response = new PagedResponse<DictDto>
            {
                List = dicts,
                Total = dicts.Count
            };

            return Ok(ApiResponse<PagedResponse<DictDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<DictDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典
    /// </summary>
    /// <param name="request">字典创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("dicts")]
    public ActionResult<ApiResponse<DictDto>> CreateDict([FromBody] CreateDictRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(100, 999);

            var dict = new DictDto
            {
                Id = newId,
                DictName = request.DictName,
                DictType = request.DictType,
                Status = 1,
                Remark = request.Remark,
                CreateTime = DateTime.Now
            };

            return Ok(ApiResponse<DictDto>.Success(dict));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DictDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <param name="request">字典更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("dicts/{id}")]
    public ActionResult<ApiResponse<object>> UpdateDict(int id, [FromBody] UpdateDictRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "字典更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dicts/{id}")]
    public ActionResult<ApiResponse<object>> DeleteDict(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "字典删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取角色菜单权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>菜单ID列表</returns>
    [HttpGet("roles/menus/{id}")]
    public ActionResult<ApiResponse<List<int>>> GetRoleMenuIds(int id)
    {
        try
        {
            // 模拟角色菜单权限数据
            var menuIds = new List<int> { 1, 2, 3, 4, 5 };
            return Ok(ApiResponse<List<int>>.Success(menuIds));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 设置角色菜单权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">菜单ID列表</param>
    /// <returns>设置结果</returns>
    [HttpPut("roles/menus/{id}")]
    public ActionResult<ApiResponse<object>> SaveRoleMenus(int id, [FromBody] RoleMenuRequest request)
    {
        try
        {
            // 模拟保存角色菜单权限
            return Ok(ApiResponse<object>.Success(new { message = "角色菜单权限设置成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }


}
