using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public ActionResult<ApiResponse<List<MenuDto>>> GetRoleMenus()
    {
        try
        {
            // 模拟菜单数据，实际应该从数据库获取用户角色对应的菜单
            var menus = new List<MenuDto>
            {
                new MenuDto
                {
                    Id = 1,
                    ParentId = 0,
                    Path = "/welcome",
                    Name = "Welcome",
                    Component = "welcome",
                    Meta = new MenuMeta
                    {
                        Title = "menus.pureHome",
                        Icon = "ep:home-filled",
                        ShowLink = true,
                        Rank = 0
                    }
                },
                new MenuDto
                {
                    Id = 2,
                    ParentId = 0,
                    Path = "/system",
                    Name = "System",
                    Component = "",
                    Redirect = "/system/user",
                    Meta = new MenuMeta
                    {
                        Title = "menus.pureSystemManagement",
                        Icon = "ri:settings-3-line",
                        ShowLink = true,
                        Rank = 1
                    },
                    Children = new List<MenuDto>
                    {
                        new MenuDto
                        {
                            Id = 21,
                            ParentId = 2,
                            Path = "/system/user",
                            Name = "SystemUser",
                            Component = "system/user/index",
                            Meta = new MenuMeta
                            {
                                Title = "menus.pureUser",
                                ShowLink = true,
                                Auths = new List<string> { "btn_add", "btn_edit", "btn_delete" }
                            }
                        },
                        new MenuDto
                        {
                            Id = 22,
                            ParentId = 2,
                            Path = "/system/role",
                            Name = "SystemRole",
                            Component = "system/role/index",
                            Meta = new MenuMeta
                            {
                                Title = "menus.pureRole",
                                ShowLink = true,
                                Auths = new List<string> { "btn_add", "btn_edit", "btn_delete" }
                            }
                        }
                    }
                },
                new MenuDto
                {
                    Id = 3,
                    ParentId = 0,
                    Path = "/work",
                    Name = "Work",
                    Component = "",
                    Redirect = "/work/task",
                    Meta = new MenuMeta
                    {
                        Title = "menus.pureWorkManagement",
                        Icon = "ep:suitcase",
                        ShowLink = true,
                        Rank = 2
                    },
                    Children = new List<MenuDto>
                    {
                        new MenuDto
                        {
                            Id = 31,
                            ParentId = 3,
                            Path = "/work/task",
                            Name = "WorkTask",
                            Component = "work/task/index",
                            Meta = new MenuMeta
                            {
                                Title = "menus.pureTask",
                                ShowLink = true
                            }
                        },
                        new MenuDto
                        {
                            Id = 32,
                            ParentId = 3,
                            Path = "/work/device",
                            Name = "WorkDevice",
                            Component = "work/device/index",
                            Meta = new MenuMeta
                            {
                                Title = "menus.pureDevice",
                                ShowLink = true
                            }
                        }
                    }
                },
                new MenuDto
                {
                    Id = 4,
                    ParentId = 0,
                    Path = "/monitor",
                    Name = "Monitor",
                    Component = "",
                    Redirect = "/monitor/logs",
                    Meta = new MenuMeta
                    {
                        Title = "menus.pureMonitor",
                        Icon = "ep:monitor",
                        ShowLink = true,
                        Rank = 3
                    },
                    Children = new List<MenuDto>
                    {
                        new MenuDto
                        {
                            Id = 41,
                            ParentId = 4,
                            Path = "/monitor/logs",
                            Name = "MonitorLogs",
                            Component = "",
                            Redirect = "/monitor/logs/login",
                            Meta = new MenuMeta
                            {
                                Title = "menus.pureLogs",
                                ShowLink = true
                            },
                            Children = new List<MenuDto>
                            {
                                new MenuDto
                                {
                                    Id = 411,
                                    ParentId = 41,
                                    Path = "/monitor/logs/login",
                                    Name = "MonitorLoginLogs",
                                    Component = "monitor/logs/login/index",
                                    Meta = new MenuMeta
                                    {
                                        Title = "menus.pureLoginLogs",
                                        ShowLink = true
                                    }
                                }
                            }
                        }
                    }
                }
            };

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }


}
