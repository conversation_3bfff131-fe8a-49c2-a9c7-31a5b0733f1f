using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;
using System.Security.Claims;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    private readonly PermissionService _permissionService;

    public SysController(PermissionService permissionService)
    {
        _permissionService = permissionService;
    }

    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetRoleMenus()
    {
        try
        {
            // 从JWT token中获取用户角色ID，如果没有则默认为1（超级管理员）
            var roleIdClaim = User.FindFirst("role_id")?.Value;
            var roleId = int.TryParse(roleIdClaim, out var id) ? id : 1;

            // 从数据库获取用户角色对应的菜单
            var permissions = await _permissionService.GetRoleMenuListAsync(roleId);
            var menuDtos = _permissionService.ConvertToMenuDtos(permissions);
            var menus = _permissionService.BuildMenuTree(menuDtos);

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取登录日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="username">用户名（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>登录日志列表</returns>
    [HttpGet("logs/loginlog")]
    public ActionResult<ApiResponse<PagedResponse<LoginLogDto>>> GetLoginLogs(
        int page_size = 10,
        int page_no = 1,
        string? username = null,
        int? status = null)
    {
        try
        {
            // 模拟登录日志数据
            var loginLogs = new List<LoginLogDto>();
            var random = new Random();
            var usernames = new[] { "admin", "user1", "user2", "test", "manager" };
            var browsers = new[] { "Chrome", "Firefox", "Safari", "Edge" };
            var oses = new[] { "Windows 10", "Windows 11", "macOS", "Linux" };
            var locations = new[] { "北京", "上海", "广州", "深圳", "杭州" };

            for (int i = 1; i <= page_size; i++)
            {
                var logStatus = random.Next(0, 10) < 8 ? 1 : 0; // 80%成功，20%失败
                var user = usernames[random.Next(usernames.Length)];

                loginLogs.Add(new LoginLogDto
                {
                    Id = i,
                    Username = user,
                    LoginIp = $"192.168.1.{random.Next(1, 255)}",
                    LoginLocation = locations[random.Next(locations.Length)],
                    Browser = browsers[random.Next(browsers.Length)],
                    Os = oses[random.Next(oses.Length)],
                    Status = logStatus,
                    Message = logStatus == 1 ? "登录成功" : "密码错误",
                    LoginTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            // 如果有筛选条件，进行过滤
            if (!string.IsNullOrEmpty(username))
            {
                loginLogs = loginLogs.Where(l => l.Username.Contains(username)).ToList();
            }

            if (status.HasValue)
            {
                loginLogs = loginLogs.Where(l => l.Status == status.Value).ToList();
            }

            var response = new PagedResponse<LoginLogDto>
            {
                List = loginLogs,
                Total = 1000 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<LoginLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<LoginLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取操作日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="module">模块</param>
    /// <param name="status">状态</param>
    /// <returns>操作日志列表</returns>
    [HttpGet("logs/operation")]
    public ActionResult<ApiResponse<PagedResponse<OperationLogDto>>> GetOperationLogs(
        int page_size = 10,
        int page_no = 1,
        string? module = null,
        int? status = null)
    {
        try
        {
            // 模拟操作日志数据
            var logs = new List<OperationLogDto>();
            var random = new Random();
            var modules = new[] { "用户管理", "设备管理", "任务管理", "系统设置", "权限管理" };
            var operations = new[] { "创建", "更新", "删除", "查询", "导出" };
            var usernames = new[] { "admin", "operator", "manager", "user1", "user2" };

            for (int i = 1; i <= page_size; i++)
            {
                var moduleType = modules[random.Next(modules.Length)];
                var operation = operations[random.Next(operations.Length)];
                var username = usernames[random.Next(usernames.Length)];

                logs.Add(new OperationLogDto
                {
                    Id = i,
                    Username = username,
                    Module = moduleType,
                    Operation = operation,
                    Description = $"{username}在{moduleType}模块执行了{operation}操作",
                    IpAddress = $"192.168.1.{random.Next(1, 255)}",
                    UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    Status = random.Next(0, 2),
                    ExecutionTime = random.Next(10, 1000),
                    OperationTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            var response = new PagedResponse<OperationLogDto>
            {
                List = logs,
                Total = 500 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<OperationLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<OperationLogDto>>.Error(500, ex.Message));
        }
    }
}
