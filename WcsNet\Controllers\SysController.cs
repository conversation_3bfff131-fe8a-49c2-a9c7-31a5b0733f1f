using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    private readonly PermissionService _permissionService;
    private readonly WcsDbContext _context;

    public SysController(PermissionService permissionService, WcsDbContext context)
    {
        _permissionService = permissionService;
        _context = context;
    }

    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetRoleMenus()
    {
        try
        {
            // 从JWT token中获取用户角色ID，如果没有则默认为1（超级管理员）
            var roleIdClaim = User.FindFirst("role_id")?.Value;
            var roleId = int.TryParse(roleIdClaim, out var id) ? id : 1;

            // 从数据库获取用户角色对应的菜单
            var permissions = await _permissionService.GetRoleMenuListAsync(roleId);
            var menuDtos = _permissionService.ConvertToMenuDtos(permissions);
            var menus = _permissionService.BuildMenuTree(menuDtos);

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取登录日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="username">用户名（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>登录日志列表</returns>
    [HttpGet("logs/loginlog")]
    public async Task<ActionResult<ApiResponse<object>>> GetLoginLogs(
        int page_size = 10,
        int page_no = 1,
        string? username = null,
        int? status = null)
    {
        try
        {
            // 从数据库获取登录日志数据
            var query = _context.LoginLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(username))
            {
                query = query.Where(l => l.Username.Contains(username));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var total = await query.CountAsync();
            var loginLogs = await query
                .OrderByDescending(l => l.LoginTime)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new
                {
                    Id = (int)l.Id,
                    Username = l.Username,
                    LoginIp = l.LoginIp,
                    LoginLocation = l.LoginLocation,
                    Browser = l.Browser,
                    Os = l.Os,
                    Status = l.Status,
                    Message = l.Message,
                    LoginTime = l.LoginTime
                })
                .ToListAsync();

            var response = new
            {
                List = loginLogs,
                Total = total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取操作日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="module">模块</param>
    /// <param name="status">状态</param>
    /// <returns>操作日志列表</returns>
    [HttpGet("logs/operation")]
    public async Task<ActionResult<ApiResponse<object>>> GetOperationLogs(
        int page_size = 10,
        int page_no = 1,
        string? module = null,
        int? status = null)
    {
        try
        {
            // 从数据库获取操作日志数据
            var query = _context.OperationLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(module))
            {
                query = query.Where(l => l.Module.Contains(module));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var total = await query.CountAsync();
            var logs = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new
                {
                    Id = (int)l.Id,
                    OperatorName = l.OperatorName,
                    Username = l.Username,
                    Module = l.Module,
                    Operation = l.Operation,
                    Description = l.Description,
                    Location = l.Location,
                    OperationIp = l.OperationIp,
                    IpAddress = l.IpAddress,
                    OperationSystem = l.OperationSystem,
                    BrowserType = l.BrowserType,
                    UserAgent = l.UserAgent,
                    OperationStatus = l.Status.ToString(),
                    Status = l.Status,
                    ExecutionTime = l.ExecutionTime,
                    CreateTime = l.CreatedAt ?? DateTime.Now,
                    OperationTime = l.OperationTime
                })
                .ToListAsync();

            var response = new
            {
                List = logs,
                Total = total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    // 部门管理API已移至DeptController，这里不再重复实现

    // 菜单管理API已移至MenuController，这里不再重复实现

    // 角色管理API应该在专门的RoleController中实现，这里不再重复实现

    // 字典管理API应该在专门的DictController中实现，这里不再重复实现
    // 角色菜单权限管理API应该在专门的RoleController中实现，这里不再重复实现


}
