using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class SysController : ControllerBase
{
    private readonly PermissionService _permissionService;

    public SysController(PermissionService permissionService)
    {
        _permissionService = permissionService;
    }

    /// <summary>
    /// 获取用户角色菜单（用于动态路由）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("rolemenus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetRoleMenus()
    {
        try
        {
            // 从JWT token中获取用户角色ID，如果没有则默认为1（超级管理员）
            var roleIdClaim = User.FindFirst("role_id")?.Value;
            var roleId = int.TryParse(roleIdClaim, out var id) ? id : 1;

            // 从数据库获取用户角色对应的菜单
            var permissions = await _permissionService.GetRoleMenuListAsync(roleId);
            var menuDtos = _permissionService.ConvertToMenuDtos(permissions);
            var menus = _permissionService.BuildMenuTree(menuDtos);

            return Ok(ApiResponse<List<MenuDto>>.Success(menus));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }



    // 部门管理API已移至DeptController，这里不再重复实现

    // 菜单管理API已移至MenuController，这里不再重复实现

    // 角色管理API应该在专门的RoleController中实现，这里不再重复实现

    // 字典管理API应该在专门的DictController中实现，这里不再重复实现
    // 角色菜单权限管理API应该在专门的RoleController中实现，这里不再重复实现


}
