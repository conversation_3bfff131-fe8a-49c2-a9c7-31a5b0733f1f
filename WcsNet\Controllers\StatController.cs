using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/stat")]
public class StatController : ControllerBase
{
    /// <summary>
    /// 获取任务统计
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <returns>任务统计数据</returns>
    [HttpGet("tasks")]
    public ActionResult<ApiResponse<PagedResponse<StatTaskDto>>> GetTaskStats(int page_size = 10)
    {
        try
        {
            // 模拟统计数据，与Go版本格式保持一致
            var stats = new List<StatTaskDto>();
            var random = new Random();
            var baseDate = DateTime.Now.Date;

            for (int i = page_size - 1; i >= 0; i--)
            {
                var date = baseDate.AddDays(-i);
                var taskCount = (uint)random.Next(10, 50);
                var successCount = (uint)random.Next(5, (int)taskCount);

                stats.Add(new StatTaskDto
                {
                    Date = date,
                    TaskCount = taskCount,
                    SuccessCount = successCount,
                    TotalSuccess = (uint)random.Next(100, 1000) // 累计总成功数
                });
            }

            var response = new PagedResponse<StatTaskDto>
            {
                Items = stats,
                Total = stats.Count,
                PageIndex = 1,
                PageSize = page_size
            };

            return Ok(ApiResponse<PagedResponse<StatTaskDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<StatTaskDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取周任务统计
    /// </summary>
    /// <param name="weekId">周ID (0=上周, 1=本周)</param>
    /// <returns>周任务统计数据</returns>
    [HttpGet("weektasks/{weekId}")]
    public ActionResult<ApiResponse<PagedResponse<StatTaskDto>>> GetWeekTaskStats(int weekId)
    {
        try
        {
            var random = new Random();
            var stats = new List<StatTaskDto>();

            // 计算周的开始和结束日期
            var now = DateTime.Now.Date;
            var startOfWeek = now.AddDays(-(int)now.DayOfWeek); // 本周开始
            if (weekId == 0) // 上周
            {
                startOfWeek = startOfWeek.AddDays(-7);
            }

            // 生成7天的统计数据
            for (int i = 0; i < 7; i++)
            {
                var date = startOfWeek.AddDays(i);
                var taskCount = (uint)random.Next(5, 30);
                var successCount = (uint)random.Next(2, (int)taskCount);

                stats.Add(new StatTaskDto
                {
                    Date = date,
                    TaskCount = taskCount,
                    SuccessCount = successCount,
                    TotalSuccess = (uint)random.Next(50, 500)
                });
            }

            var response = new PagedResponse<StatTaskDto>
            {
                Items = stats,
                Total = stats.Count,
                PageIndex = 1,
                PageSize = 7
            };

            return Ok(ApiResponse<PagedResponse<StatTaskDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<StatTaskDto>>.Error(500, ex.Message));
        }
    }
}
