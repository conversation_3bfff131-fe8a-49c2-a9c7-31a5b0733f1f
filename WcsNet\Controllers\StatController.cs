using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class StatController : ControllerBase
{
    /// <summary>
    /// 获取任务统计
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <returns>任务统计数据</returns>
    [HttpGet("tasks")]
    public ActionResult<ApiResponse<PagedResponse<TaskStatDto>>> GetTaskStats(int page_size = 10)
    {
        try
        {
            // 模拟任务统计数据
            var tasks = new List<TaskStatDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                tasks.Add(new TaskStatDto
                {
                    Id = i,
                    TaskName = $"任务{i:D3}",
                    TaskType = random.Next(1, 4) switch
                    {
                        1 => "入库",
                        2 => "出库",
                        3 => "移库",
                        _ => "盘点"
                    },
                    Status = random.Next(1, 5) switch
                    {
                        1 => "待执行",
                        2 => "执行中",
                        3 => "已完成",
                        _ => "异常"
                    },
                    Priority = random.Next(1, 4),
                    CreateTime = DateTime.Now.AddHours(-random.Next(1, 24)),
                    UpdateTime = DateTime.Now.AddMinutes(-random.Next(1, 60)),
                    Progress = random.Next(0, 101)
                });
            }

            var response = new PagedResponse<TaskStatDto>
            {
                Items = tasks,
                Total = 100, // 模拟总数
                PageIndex = 1,
                PageSize = page_size
            };

            return Ok(ApiResponse<PagedResponse<TaskStatDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<TaskStatDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取周任务统计
    /// </summary>
    /// <param name="weekId">周ID</param>
    /// <returns>周任务统计数据</returns>
    [HttpGet("weektasks/{weekId}")]
    public ActionResult<ApiResponse<WeekTaskStatDto>> GetWeekTaskStats(int weekId)
    {
        try
        {
            var random = new Random();
            var weekStat = new WeekTaskStatDto
            {
                WeekId = weekId,
                WeekStart = DateTime.Now.AddDays(-7),
                WeekEnd = DateTime.Now,
                TotalTasks = random.Next(50, 200),
                CompletedTasks = random.Next(30, 150),
                PendingTasks = random.Next(5, 30),
                FailedTasks = random.Next(0, 10),
                DailyStats = new List<DailyTaskStatDto>()
            };

            // 生成每日统计
            for (int i = 0; i < 7; i++)
            {
                weekStat.DailyStats.Add(new DailyTaskStatDto
                {
                    Date = DateTime.Now.AddDays(-6 + i),
                    TaskCount = random.Next(5, 30),
                    CompletedCount = random.Next(3, 25),
                    FailedCount = random.Next(0, 3)
                });
            }

            return Ok(ApiResponse<WeekTaskStatDto>.Success(weekStat));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<WeekTaskStatDto>.Error(500, ex.Message));
        }
    }
}
