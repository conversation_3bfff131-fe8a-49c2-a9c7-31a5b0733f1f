using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_work")]
public class Work : BaseModel
{
    public sbyte TaskType { get; set; } = 0; // 任务类型
    public uint LaneId { get; set; } = 0; // 所属巷道id

    [StringLength(128)]
    public string ItemCode { get; set; } = string.Empty; // 箱子编码

    [Column(TypeName = "text")]
    public string Parameters { get; set; } = string.Empty; // 任务参数

    public sbyte Retries { get; set; } = 0; // 重试次数
    public sbyte MaxRetries { get; set; } = 3; // 最大重试次数

    [Column(TypeName = "mediumtext")]
    public string? ErrMsg { get; set; } // 错误消息

    public sbyte Status { get; set; } = 1; // 状态 1未执行 2执行中 3执行失败 4执行成功
}
