using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_work")]
public class Work : BaseModel
{
    [Column("task_type")]
    public sbyte TaskType { get; set; } = 0; // 任务类型

    [Column("lane_id")]
    public ulong LaneId { get; set; } = 0; // 所属巷道id

    [StringLength(128)]
    [Column("item_code")]
    public string ItemCode { get; set; } = string.Empty; // 箱子编码

    [Column(TypeName = "text")]
    public string Parameters { get; set; } = string.Empty; // 任务参数

    public sbyte Retries { get; set; } = 0; // 重试次数

    [Column("max_retries")]
    public sbyte MaxRetries { get; set; } = 3; // 最大重试次数

    [Column("err_msg", TypeName = "mediumtext")]
    public string? ErrMsg { get; set; } // 错误消息

    public sbyte Status { get; set; } = 1; // 状态 1未执行 2执行中 3执行失败 4执行成功

    // 添加时间戳字段
    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
