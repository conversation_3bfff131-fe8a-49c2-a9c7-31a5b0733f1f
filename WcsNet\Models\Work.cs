﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_work")]
public class Work : BaseModel
{
    [Column("task_type")]
    public sbyte TaskType { get; set; } = 0;

    [Column("lane_id")]
    public ulong LaneId { get; set; } = 0;

    [StringLength(128)]
    [Column("item_code")]
    public string? ItemCode { get; set; }

    [Column(TypeName = "text")]
    public string? Parameters { get; set; }

    public sbyte Retries { get; set; } = 0;

    [Column("max_retries")]
    public sbyte MaxRetries { get; set; } = 3;

    [Column("err_msg", TypeName = "mediumtext")]
    public string? ErrMsg { get; set; }

    public sbyte Status { get; set; } = 1; // 1未执行 2执行中 3执行失败 4执行成功
}
