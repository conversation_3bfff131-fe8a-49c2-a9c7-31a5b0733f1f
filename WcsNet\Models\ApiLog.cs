using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_api_log")]
public class ApiLog : BaseModel
{
    [Column("work_id")]
    public ulong WorkId { get; set; } = 0;

    [Column("step_id")]
    public ulong StepId { get; set; } = 0;

    [StringLength(255)]
    [Column("api_name")]
    public string ApiName { get; set; } = string.Empty;

    [StringLength(1024)]
    [Column("req_param")]
    public string ReqParam { get; set; } = string.Empty;

    [Column("response")]
    public string Response { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1成功 2失败

    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
