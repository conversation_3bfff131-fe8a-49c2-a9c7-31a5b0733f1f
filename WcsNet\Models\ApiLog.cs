using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_api_log")]
public class ApiLog : BaseModel
{
    [Required]
    [StringLength(10)]
    public string Method { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string Url { get; set; } = string.Empty;

    [Column("request_body")]
    public string RequestBody { get; set; } = string.Empty;

    [Column("response_body")]
    public string ResponseBody { get; set; } = string.Empty;

    [Column("status_code")]
    public int StatusCode { get; set; } = 0;

    [Column("response_time")]
    public long ResponseTime { get; set; } = 0;

    [StringLength(45)]
    [Column("client_ip")]
    public string ClientIp { get; set; } = string.Empty;

    [StringLength(255)]
    [Column("user_agent")]
    public string UserAgent { get; set; } = string.Empty;

    [Column("user_id")]
    public ulong UserId { get; set; } = 0;

    [Column("work_id")]
    public ulong WorkId { get; set; } = 0;

    [StringLength(128)]
    [Column("api_name")]
    public string ApiName { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1;
}
