﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_alarm")]
public class Alarm : BaseModel
{
    [Column("alarm_type")]
    public long AlarmType { get; set; } = 1;

    public int Category { get; set; } = 0;

    public long Code { get; set; } = 0;

    [Column("device_id")]
    public int DeviceId { get; set; } = 0;

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;
}
