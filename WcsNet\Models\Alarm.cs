using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_alarm")]
public class Alarm : BaseModel
{
    [Column("alarm_type")]
    public int AlarmType { get; set; } = 1;

    [Column("category")]
    public int Category { get; set; } = 0;

    [Column("code")]
    public int Code { get; set; } = 0;

    [Column("device_id")]
    public uint DeviceId { get; set; } = 0;

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;

    public DateTime? CreatedAt { get; set; }
}
