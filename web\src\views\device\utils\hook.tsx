import dayjs from "dayjs";
import editForm from "../form/form.vue";
import { message } from "@/utils/message";
import { getDictValueList } from "@/api/system";
import {
  createDevice,
  updateDevice,
  getDeviceList,
  deleteDevice
} from "@/api/device";
import { usePublicHooks } from "../../system/hooks";
import { addDialog } from "@/components/ReDialog";
import { reactive, ref, onMounted, h } from "vue";
import type { FormItemProps } from "../utils/types";
import { deviceDetection } from "@pureadmin/utils";
import type { PaginationProps } from "@pureadmin/table";

export function useDevice() {
  const form = reactive({
    device_name: "",
    device_code: "",
    device_type: null,
    status: null,
    page_size: 10,
    page_no: 1
  });

  const pagination = reactive<PaginationProps>({
    total: 0,
    pageSize: 10,
    currentPage: 1,
    pageSizes: [10, 20, 30, 50],
    background: true
  });

  const formRef = ref();
  const dataList = ref([]);
  //设备类型
  const deviceTypeList = ref([]);
  const deviceTypeMap = new Map();
  //通讯类型
  const commList = ref([]);
  const commMap = new Map();

  const loading = ref(true);
  const { tagStyle } = usePublicHooks();

  const columns: TableColumnList = [
    {
      label: "设备名称",
      prop: "device_name",
      width: 220,
      align: "left"
    },
    {
      label: "设备编号",
      prop: "device_code",
      width: 100,
      align: "left"
    },
    {
      label: "设备类型",
      prop: "device_type",
      width: 100,
      formatter: ({ device_type }) => deviceTypeMap.get(device_type)
    },
    {
      label: "通信类型",
      prop: "comm_type",
      width: 100,
      formatter: ({ comm_type }) => commMap.get(comm_type)
    },
    {
      label: "地址",
      prop: "addr",
      minWidth: 120
    },
    {
      label: "端口号",
      prop: "port",
      minWidth: 70
    },
    {
      label: "在线状态",
      prop: "online",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} style={tagStyle.value(row.status)}>
          {row.online === 1 ? "未知" : row.online == 2 ? "在线" : "离线"}
        </el-tag>
      )
    },
    {
      label: "运行状态",
      prop: "run_status",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} style={tagStyle.value(row.status)}>
          {row.run_status === 1
            ? "未知"
            : row.run_status == 2
              ? "运行"
              : "停止"}
        </el-tag>
      )
    },
    {
      label: "状态",
      prop: "status",
      minWidth: 100,
      cellRenderer: ({ row, props }) => (
        <el-tag size={props.size} style={tagStyle.value(row.status)}>
          {row.status === 1 ? "启用" : "停用"}
        </el-tag>
      )
    },
    {
      label: "创建时间",
      minWidth: 200,
      prop: "createdAt",
      formatter: ({ createdAt }) =>
        dayjs(createdAt).format("YYYY-MM-DD HH:mm:ss")
    },
    {
      label: "操作",
      fixed: "right",
      width: 210,
      slot: "operation"
    }
  ];

  function handleSizeChange(val: number) {
    form.page_size = val;
    form.page_no = 1;
    onSearch();
  }

  function handleCurrentChange(val: number) {
    form.page_no = val;
    onSearch();
    // console.log(`current page: ${val}`);
  }

  function handleSelectionChange(val) {
    console.log("handleSelectionChange", val);
  }

  function resetForm(formEl) {
    if (!formEl) return;
    formEl.resetFields();
    form.page_no = 1;
    onSearch();
  }

  async function onSearch() {
    loading.value = true;
    const response = await getDeviceList(form);
    console.log("Device API Response:", response);
    const { code, data } = response;
    console.log("Destructured - code:", code, "data:", data);
    if (code == 0) {
      console.log("data.list:", data.list);
      console.log("data.total:", data.total);
      dataList.value = data.list;
      pagination.total = data.total;
    }
    loading.value = false;
  }

  function openDialog(title = "新增", row?: FormItemProps) {
    addDialog({
      title: `${title}设备`,
      props: {
        formInline: {
          deviceTypeOptions: deviceTypeList.value ?? [],
          commOptions: commList.value ?? [],
          device_name: row?.device_name ?? "",
          device_code: row?.device_code ?? "",
          device_type: row?.device_type ?? "",
          comm_type: row?.comm_type ?? "",
          addr: row?.addr ?? "",
          port: row?.port ?? 0,
          status: row?.status ?? 1
        }
      },
      width: "40%",
      draggable: true,
      fullscreen: deviceDetection(),
      fullscreenIcon: true,
      closeOnClickModal: false,
      contentRenderer: () => h(editForm, { ref: formRef, formInline: null }),
      beforeSure: (done, { options }) => {
        const FormRef = formRef.value.getRef();
        const curData = options.props.formInline as FormItemProps;
        function chores() {
          message(`您${title}设备名称为${curData.device_name}的这条数据`, {
            type: "success"
          });
          done(); // 关闭弹框
          onSearch(); // 刷新表格数据
        }
        FormRef.validate(valid => {
          if (valid) {
            console.log("curData", curData);
            const data = {
              device_name: curData.device_name,
              device_code: curData.device_code,
              device_type: curData.device_type,
              comm_type: curData.comm_type,
              addr: curData.addr,
              port: curData.port - 0,
              status: curData.status
            };
            // 表单规则校验通过
            if (title === "新增") {
              createDevice(data).then(res => {
                if (res.code == 0) {
                  chores();
                }
              });
            } else {
              updateDevice(row!.id, data).then(res => {
                if (res.code == 0) {
                  chores();
                }
              });
            }
          }
        });
      }
    });
  }

  function handleDelete(row) {
    deleteDevice(row.id).then(res => {
      if (res.code == 0) {
        message(`您删除了设备名称为${row.device_name}的这条数据`, {
          type: "success"
        });
        onSearch();
      }
    });
  }

  onMounted(async () => {
    let { code, data } = await getDictValueList(3);
    if (code == 0) {
      data.list.forEach(item => {
        deviceTypeMap.set(item.value - 0, item.label);
        deviceTypeList.value.push({
          label: item.label,
          value: item.value - 0
        });
      });
    }
    //通信协议
    let rtn = await getDictValueList(2);
    if (rtn.code == 0) {
      rtn.data.list.forEach(item => {
        commMap.set(item.value - 0, item.label);
        commList.value.push({
          label: item.label,
          value: item.value - 0
        });
      });
    }
    onSearch();
  });

  return {
    form,
    loading,
    columns,
    dataList,
    deviceTypeList,
    pagination,
    commList,
    /** 搜索 */
    onSearch,
    /** 重置 */
    resetForm,
    /** 新增、修改设备 */
    openDialog,
    /** 删除设备 */
    handleDelete,
    handleSizeChange,
    handleCurrentChange,
    handleSelectionChange
  };
}
