﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_stock")]
public class Stock : BaseModel
{
    [Required]
    [StringLength(128)]
    [Column("stock_name")]
    public string StockName { get; set; } = string.Empty;

    [Required]
    [StringLength(24)]
    [Column("stock_code")]
    public string StockCode { get; set; } = string.Empty;

    [Required]
    [StringLength(24)]
    [Column("lane_code")]
    public string LaneCode { get; set; } = string.Empty;

    [StringLength(24)]
    public string Shelves { get; set; } = string.Empty;

    [StringLength(16)]
    public string X { get; set; } = string.Empty;

    [StringLength(16)]
    public string Y { get; set; } = string.Empty;

    [StringLength(16)]
    public string Z { get; set; } = string.Empty;

    public sbyte Layer { get; set; } = 0;

    public sbyte Deep { get; set; } = 1;

    public sbyte Side { get; set; } = 1;

    [StringLength(8)]
    [Column("abc_type")]
    public string AbcType { get; set; } = string.Empty;

    [StringLength(128)]
    [Column("goods_code")]
    public string GoodsCode { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1无任务 2入库中 3出库中 4禁用
}
