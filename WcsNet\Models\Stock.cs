using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_stock")]
public class Stock : BaseModel
{
    [Required]
    [StringLength(128)]
    [Column("stock_name")]
    public string StockName { get; set; } = string.Empty;

    [Required]
    [StringLength(24)]
    [Column("stock_code")]
    public string StockCode { get; set; } = string.Empty;

    [StringLength(24)]
    [Column("lane_code")]
    public string LaneCode { get; set; } = string.Empty;

    [StringLength(24)]
    public string Shelves { get; set; } = string.Empty; // 货架编码

    [StringLength(16)]
    public string X { get; set; } = string.Empty; // x轴坐标

    [StringLength(16)]
    public string Y { get; set; } = string.Empty; // y轴坐标

    [StringLength(16)]
    public string Z { get; set; } = string.Empty; // z轴坐标

    public sbyte Layer { get; set; } = 0; // 所在层数
    public sbyte Deep { get; set; } = 1; // 货位深度 1或2
    public sbyte Side { get; set; } = 1; // 所处巷道位置 1左侧 2右侧

    [StringLength(8)]
    [Column("abc_type")]
    public string AbcType { get; set; } = string.Empty; // abc类型

    [StringLength(128)]
    [Column("goods_code")]
    public string GoodsCode { get; set; } = string.Empty; // 货物编码

    public sbyte Status { get; set; } = 1; // 1无任务 2入库中 3出库中 4禁用

    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    // 是否有货
    public bool HasGoods => !string.IsNullOrEmpty(GoodsCode);
}
