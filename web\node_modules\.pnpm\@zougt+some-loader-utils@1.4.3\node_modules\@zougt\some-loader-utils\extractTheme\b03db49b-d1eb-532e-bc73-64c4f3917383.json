{"cssRules": {"layout-theme-light": [".layout-theme-light .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: rgba(0, 0, 0, 0.8509803922);\n}"], "layout-theme-default": [".layout-theme-default .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-saucePurple": [".layout-theme-saucePurple .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-pink": [".layout-theme-pink .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-dusk": [".layout-theme-dusk .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-volcano": [".layout-theme-volcano .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-mingQing": [".layout-theme-mingQing .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"], "layout-theme-auroraGreen": [".layout-theme-auroraGreen .sidebar-logo-container .sidebar-logo-link .sidebar-title {\n  color: #fff;\n}"]}, "ruleValues": ["#fff"], "resourcePath": "E:/SourceCode/Test/TempTest/Ai/wcs/web/src/layout/components/lay-sidebar/components/SidebarLogo.vue"}