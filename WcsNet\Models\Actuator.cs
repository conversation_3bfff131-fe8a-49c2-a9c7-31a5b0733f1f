using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_actuator")]
public class Actuator : BaseModel
{
    [Required]
    [StringLength(128)]
    [Column("act_name")]
    public string ActName { get; set; } = string.Empty;

    [Column("group_id")]
    public ulong GroupId { get; set; } = 0;

    [Required]
    [StringLength(255)]
    [Column("class_name")]
    public string ClassName { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    [Column("method_name")]
    public string MethodName { get; set; } = string.Empty;

    [Column("act_type")]
    public sbyte ActType { get; set; } = 0; // 1成功回调 2执行函数 3成功条件 4执行条件

    public sbyte Level { get; set; } = 1; // 优先级

    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;
}
