# API接口覆盖率测试脚本
param(
    [string]$BaseUrl = "http://localhost:8666",
    [string]$FrontendUrl = "http://localhost:8851"
)

Write-Host "=== WCS API接口覆盖率测试 ===" -ForegroundColor Green
Write-Host "后端地址: $BaseUrl" -ForegroundColor Yellow
Write-Host "前端地址: $FrontendUrl" -ForegroundColor Yellow
Write-Host ""

# 定义需要测试的API接口列表
$apiEndpoints = @(
    # 认证相关
    @{ Method = "POST"; Path = "/api/auth/login"; Description = "用户登录"; TestData = @{ account = "admin"; password = "123456" } },
    @{ Method = "GET"; Path = "/api/auth/userinfo"; Description = "获取用户信息" },
    @{ Method = "POST"; Path = "/api/auth/logout"; Description = "用户登出" },

    # 系统管理
    @{ Method = "GET"; Path = "/api/sys/rolemenus"; Description = "获取角色菜单" },
    @{ Method = "GET"; Path = "/api/sys/users"; Description = "获取用户列表" },
    @{ Method = "GET"; Path = "/api/sys/roles"; Description = "获取角色列表" },
    @{ Method = "GET"; Path = "/api/sys/roles/all"; Description = "获取所有角色" },
    @{ Method = "GET"; Path = "/api/sys/menus"; Description = "获取菜单列表" },
    @{ Method = "GET"; Path = "/api/sys/menulist"; Description = "获取菜单列表(简化)" },
    @{ Method = "GET"; Path = "/api/sys/depts"; Description = "获取部门列表" },
    @{ Method = "GET"; Path = "/api/sys/deptlist"; Description = "获取部门列表(简化)" },
    @{ Method = "GET"; Path = "/api/sys/dicts"; Description = "获取字典列表" },
    @{ Method = "GET"; Path = "/api/sys/dictvalues/1"; Description = "获取字典值列表" },
    @{ Method = "GET"; Path = "/api/sys/dictvalues/2"; Description = "获取字典值列表" },
    @{ Method = "GET"; Path = "/api/sys/dictvalues/3"; Description = "获取字典值列表" },

    # 数据库初始化
    @{ Method = "GET"; Path = "/api/init/status"; Description = "获取数据库状态" },
    @{ Method = "POST"; Path = "/api/init/database"; Description = "初始化数据库" },

    # 统计数据
    @{ Method = "GET"; Path = "/api/stat/tasks"; Description = "获取任务统计" },
    @{ Method = "GET"; Path = "/api/stat/weektasks/0"; Description = "获取周任务统计" },
    @{ Method = "GET"; Path = "/api/stat/weektasks/1"; Description = "获取周任务统计" },

    # 告警管理
    @{ Method = "GET"; Path = "/api/alarms"; Description = "获取告警列表" },

    # 设备管理
    @{ Method = "GET"; Path = "/api/devices"; Description = "获取设备列表" },

    # 任务管理
    @{ Method = "GET"; Path = "/api/works"; Description = "获取任务列表" },

    # 库存管理
    @{ Method = "GET"; Path = "/api/stocks"; Description = "获取库存列表" },

    # 测试接口
    @{ Method = "GET"; Path = "/api/test/endpoints"; Description = "获取API接口列表" },
    @{ Method = "GET"; Path = "/api/test/status"; Description = "获取系统状态" }
)

# 测试结果统计
$totalTests = $apiEndpoints.Count
$passedTests = 0
$failedTests = 0
$results = @()

Write-Host "开始测试 $totalTests 个API接口..." -ForegroundColor Cyan
Write-Host ""

foreach ($endpoint in $apiEndpoints) {
    $url = "$BaseUrl$($endpoint.Path)"
    $method = $endpoint.Method
    $description = $endpoint.Description
    
    Write-Host "测试: $method $($endpoint.Path) - $description" -NoNewline
    
    try {
        $startTime = Get-Date
        
        if ($method -eq "GET") {
            $response = Invoke-RestMethod -Uri $url -Method $method -TimeoutSec 10
        } elseif ($method -eq "POST" -and $endpoint.TestData) {
            $body = $endpoint.TestData | ConvertTo-Json
            $response = Invoke-RestMethod -Uri $url -Method $method -Body $body -ContentType "application/json" -TimeoutSec 10
        } else {
            $response = Invoke-RestMethod -Uri $url -Method $method -TimeoutSec 10
        }
        
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        if ($response.code -eq 0 -or $response.code -eq $null) {
            Write-Host " ✓ PASS ($([math]::Round($duration, 2))ms)" -ForegroundColor Green
            $passedTests++
            $status = "PASS"
        } else {
            Write-Host " ✗ FAIL (Code: $($response.code), Msg: $($response.msg))" -ForegroundColor Red
            $failedTests++
            $status = "FAIL"
        }
        
        $results += @{
            Method = $method
            Path = $endpoint.Path
            Description = $description
            Status = $status
            Duration = [math]::Round($duration, 2)
            Response = if ($response.data) { "Has Data" } else { "No Data" }
        }
        
    } catch {
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalMilliseconds
        
        Write-Host " ✗ ERROR ($($_.Exception.Message))" -ForegroundColor Red
        $failedTests++
        
        $results += @{
            Method = $method
            Path = $endpoint.Path
            Description = $description
            Status = "ERROR"
            Duration = [math]::Round($duration, 2)
            Response = $_.Exception.Message
        }
    }
}

Write-Host ""
Write-Host "=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host "总计: $totalTests 个接口" -ForegroundColor White
Write-Host "通过: $passedTests 个接口" -ForegroundColor Green
Write-Host "失败: $failedTests 个接口" -ForegroundColor Red
Write-Host "成功率: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Yellow

if ($failedTests -gt 0) {
    Write-Host ""
    Write-Host "=== 失败的接口 ===" -ForegroundColor Red
    $results | Where-Object { $_.Status -ne "PASS" } | ForEach-Object {
        Write-Host "$($_.Method) $($_.Path) - $($_.Description)" -ForegroundColor Red
        Write-Host "  错误: $($_.Response)" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "=== 性能统计 ===" -ForegroundColor Green
$avgDuration = ($results | Measure-Object -Property Duration -Average).Average
$maxDuration = ($results | Measure-Object -Property Duration -Maximum).Maximum
$minDuration = ($results | Measure-Object -Property Duration -Minimum).Minimum

Write-Host "平均响应时间: $([math]::Round($avgDuration, 2))ms" -ForegroundColor White
Write-Host "最大响应时间: $([math]::Round($maxDuration, 2))ms" -ForegroundColor White
Write-Host "最小响应时间: $([math]::Round($minDuration, 2))ms" -ForegroundColor White

# 检查前端页面是否可访问
Write-Host ""
Write-Host "=== 前端页面检查 ===" -ForegroundColor Green
try {
    $frontendResponse = Invoke-WebRequest -Uri $FrontendUrl -TimeoutSec 10
    if ($frontendResponse.StatusCode -eq 200) {
        Write-Host "前端页面: ✓ 可访问" -ForegroundColor Green
    } else {
        Write-Host "前端页面: ✗ 状态码 $($frontendResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "前端页面: ✗ 无法访问 ($($_.Exception.Message))" -ForegroundColor Red
}

Write-Host ""
Write-Host "测试完成！" -ForegroundColor Green
