{"version": 3, "sources": ["../../.pnpm/mint-filter@4.0.3/node_modules/mint-filter/dist/module/node.js", "../../.pnpm/mint-filter@4.0.3/node_modules/mint-filter/dist/module/index.js"], "sourcesContent": ["export default class Node {\n    depth = 0;\n    key;\n    word = false;\n    children = {};\n    fail;\n    count = 0;\n    constructor(key, depth = 0) {\n        this.key = key;\n        this.depth = depth;\n    }\n}\n", "import Node from './node';\nexport class Mint {\n    root = new Node('root');\n    customCharacter;\n    constructor(keys, ops) {\n        const len = keys.length;\n        this.customCharacter = ops?.customCharacter || '*';\n        for (let idx = 0; idx < len; idx++) {\n            this.add(keys[idx], false);\n        }\n        this.build();\n    }\n    build() {\n        const queue = [];\n        queue.push(this.root);\n        let idx = 0;\n        while (queue.length > idx) {\n            const beginNode = queue[idx];\n            const map = beginNode.children;\n            for (const key in beginNode.children) {\n                const node = map[key];\n                let failNode = beginNode.fail;\n                while (failNode && !failNode.children[key]) {\n                    failNode = failNode.fail;\n                }\n                node.fail = failNode?.children[key] || this.root;\n                queue.push(node);\n            }\n            idx++;\n        }\n    }\n    search(text, options = {\n        replace: true,\n    }) {\n        let node = this.root;\n        const fText = [];\n        const oText = [];\n        const words = [];\n        const { replace = true, verify = false } = options;\n        const textLen = text.length;\n        for (let i = 0; i < textLen; i++) {\n            const oKey = text[i];\n            const key = oKey.toLowerCase();\n            while (node && !node?.children[key]) {\n                node = node?.fail;\n            }\n            node = node?.children[key] || this.root;\n            fText.push(oKey);\n            oText.push(oKey);\n            if (node.word) {\n                let idx = i + 1 - node.depth;\n                let word = '';\n                while (idx <= i) {\n                    const v = oText[idx];\n                    word += v;\n                    if (replace) {\n                        fText[idx] = this.customCharacter;\n                    }\n                    idx++;\n                }\n                words.push(word);\n                if (verify) {\n                    break;\n                }\n            }\n        }\n        return {\n            words,\n            text: fText.join(''),\n        };\n    }\n    filter(text, options) {\n        return this.search(text, options);\n    }\n    verify(text) {\n        const { words } = this.search(text, { verify: true });\n        return !words.length;\n    }\n    delete(key) {\n        const type = this.pop(key.toLowerCase(), key.length, this.root);\n        this.build();\n        return type;\n    }\n    pop(key, len, node, carry = 'delete', idx = 0) {\n        if (!node) {\n            return 'delete';\n        }\n        if (idx === len) {\n            node.word = false;\n            node.count--;\n            let isDel = true;\n            for (const k in node.children) {\n                if (k) {\n                    isDel = false;\n                    break;\n                }\n            }\n            return isDel ? carry : 'update';\n        }\n        else {\n            const val = key[idx];\n            const next = node.children[val];\n            const type = this.pop(key, len, next, node.word ? 'update' : carry, idx + 1);\n            node.count--;\n            if (type === 'delete' && next?.count === 0) {\n                delete node.children[val];\n            }\n            return type;\n        }\n    }\n    add(key, build = true) {\n        const lowKey = key.toLowerCase();\n        const len = lowKey.length;\n        this.put(lowKey, len);\n        if (build) {\n            this.build();\n        }\n        return true;\n    }\n    put(key, len) {\n        let node = this.root;\n        const lastIdx = len - 1;\n        node.count++;\n        for (let idx = 0; idx < len; idx++) {\n            const val = key[idx];\n            const nextNode = node.children[val];\n            if (nextNode) {\n                nextNode.count++;\n                node = nextNode;\n            }\n            else {\n                const newNode = new Node(val, idx + 1);\n                newNode.count = 1;\n                node.children[val] = newNode;\n                node = newNode;\n            }\n            if (lastIdx === idx && node.depth) {\n                node.word = true;\n            }\n        }\n    }\n}\nexport default Mint;\n"], "mappings": ";;;;;AAAA,IAAqB,OAArB,MAA0B;AAAA,EAOtB,YAAY,KAAK,QAAQ,GAAG;AAN5B,iCAAQ;AACR;AACA,gCAAO;AACP,oCAAW,CAAC;AACZ;AACA,iCAAQ;AAEJ,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACjB;AACJ;;;ACVO,IAAM,OAAN,MAAW;AAAA,EAGd,YAAY,MAAM,KAAK;AAFvB,gCAAO,IAAI,KAAK,MAAM;AACtB;AAEI,UAAM,MAAM,KAAK;AACjB,SAAK,mBAAkB,2BAAK,oBAAmB;AAC/C,aAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAChC,WAAK,IAAI,KAAK,GAAG,GAAG,KAAK;AAAA,IAC7B;AACA,SAAK,MAAM;AAAA,EACf;AAAA,EACA,QAAQ;AACJ,UAAM,QAAQ,CAAC;AACf,UAAM,KAAK,KAAK,IAAI;AACpB,QAAI,MAAM;AACV,WAAO,MAAM,SAAS,KAAK;AACvB,YAAM,YAAY,MAAM,GAAG;AAC3B,YAAM,MAAM,UAAU;AACtB,iBAAW,OAAO,UAAU,UAAU;AAClC,cAAM,OAAO,IAAI,GAAG;AACpB,YAAI,WAAW,UAAU;AACzB,eAAO,YAAY,CAAC,SAAS,SAAS,GAAG,GAAG;AACxC,qBAAW,SAAS;AAAA,QACxB;AACA,aAAK,QAAO,qCAAU,SAAS,SAAQ,KAAK;AAC5C,cAAM,KAAK,IAAI;AAAA,MACnB;AACA;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,MAAM,UAAU;AAAA,IACnB,SAAS;AAAA,EACb,GAAG;AACC,QAAI,OAAO,KAAK;AAChB,UAAM,QAAQ,CAAC;AACf,UAAM,QAAQ,CAAC;AACf,UAAM,QAAQ,CAAC;AACf,UAAM,EAAE,UAAU,MAAM,SAAS,MAAM,IAAI;AAC3C,UAAM,UAAU,KAAK;AACrB,aAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAC9B,YAAM,OAAO,KAAK,CAAC;AACnB,YAAM,MAAM,KAAK,YAAY;AAC7B,aAAO,QAAQ,EAAC,6BAAM,SAAS,OAAM;AACjC,eAAO,6BAAM;AAAA,MACjB;AACA,cAAO,6BAAM,SAAS,SAAQ,KAAK;AACnC,YAAM,KAAK,IAAI;AACf,YAAM,KAAK,IAAI;AACf,UAAI,KAAK,MAAM;AACX,YAAI,MAAM,IAAI,IAAI,KAAK;AACvB,YAAI,OAAO;AACX,eAAO,OAAO,GAAG;AACb,gBAAM,IAAI,MAAM,GAAG;AACnB,kBAAQ;AACR,cAAI,SAAS;AACT,kBAAM,GAAG,IAAI,KAAK;AAAA,UACtB;AACA;AAAA,QACJ;AACA,cAAM,KAAK,IAAI;AACf,YAAI,QAAQ;AACR;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,MACH;AAAA,MACA,MAAM,MAAM,KAAK,EAAE;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,OAAO,MAAM,SAAS;AAClB,WAAO,KAAK,OAAO,MAAM,OAAO;AAAA,EACpC;AAAA,EACA,OAAO,MAAM;AACT,UAAM,EAAE,MAAM,IAAI,KAAK,OAAO,MAAM,EAAE,QAAQ,KAAK,CAAC;AACpD,WAAO,CAAC,MAAM;AAAA,EAClB;AAAA,EACA,OAAO,KAAK;AACR,UAAM,OAAO,KAAK,IAAI,IAAI,YAAY,GAAG,IAAI,QAAQ,KAAK,IAAI;AAC9D,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,IAAI,KAAK,KAAK,MAAM,QAAQ,UAAU,MAAM,GAAG;AAC3C,QAAI,CAAC,MAAM;AACP,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,KAAK;AACb,WAAK,OAAO;AACZ,WAAK;AACL,UAAI,QAAQ;AACZ,iBAAW,KAAK,KAAK,UAAU;AAC3B,YAAI,GAAG;AACH,kBAAQ;AACR;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,QAAQ,QAAQ;AAAA,IAC3B,OACK;AACD,YAAM,MAAM,IAAI,GAAG;AACnB,YAAM,OAAO,KAAK,SAAS,GAAG;AAC9B,YAAM,OAAO,KAAK,IAAI,KAAK,KAAK,MAAM,KAAK,OAAO,WAAW,OAAO,MAAM,CAAC;AAC3E,WAAK;AACL,UAAI,SAAS,aAAY,6BAAM,WAAU,GAAG;AACxC,eAAO,KAAK,SAAS,GAAG;AAAA,MAC5B;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,IAAI,KAAK,QAAQ,MAAM;AACnB,UAAM,SAAS,IAAI,YAAY;AAC/B,UAAM,MAAM,OAAO;AACnB,SAAK,IAAI,QAAQ,GAAG;AACpB,QAAI,OAAO;AACP,WAAK,MAAM;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,KAAK,KAAK;AACV,QAAI,OAAO,KAAK;AAChB,UAAM,UAAU,MAAM;AACtB,SAAK;AACL,aAAS,MAAM,GAAG,MAAM,KAAK,OAAO;AAChC,YAAM,MAAM,IAAI,GAAG;AACnB,YAAM,WAAW,KAAK,SAAS,GAAG;AAClC,UAAI,UAAU;AACV,iBAAS;AACT,eAAO;AAAA,MACX,OACK;AACD,cAAM,UAAU,IAAI,KAAK,KAAK,MAAM,CAAC;AACrC,gBAAQ,QAAQ;AAChB,aAAK,SAAS,GAAG,IAAI;AACrB,eAAO;AAAA,MACX;AACA,UAAI,YAAY,OAAO,KAAK,OAAO;AAC/B,aAAK,OAAO;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,IAAO,iBAAQ;", "names": []}