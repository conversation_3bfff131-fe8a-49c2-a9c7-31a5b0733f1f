using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Security.Cryptography;
using System.Security.Claims;
using System.Text;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 用户服务
/// </summary>
public class UserService : BaseService<User>
{
    private readonly string _salt;
    private readonly ILogger<UserService> _logger;

    public UserService(WcsDbContext context, string salt, ILogger<UserService> logger) : base(context)
    {
        _salt = salt;
        _logger = logger;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="account">账号</param>
    /// <param name="password">密码</param>
    /// <returns>用户信息，如果登录失败则返回null</returns>
    public async Task<User?> LoginAsync(string account, string password)
    {
        // 前端发送的密码已经是MD5哈希后的，所以这里标记为已MD5
        var hashedPassword = HashPassword(password, _salt, true);
        _logger.LogInformation("登录调试 - 账号: {Account}, 输入密码: {InputPassword}, 盐值: {Salt}, 生成哈希: {HashedPassword}", account, password, _salt, hashedPassword);

        var user = await _dbSet.FirstOrDefaultAsync(u => u.Account == account && u.Status == 1);
        if (user != null)
        {
            _logger.LogInformation("数据库密码: {DbPassword}", user.Password);
        }

        return await _dbSet
            .FirstOrDefaultAsync(u => u.Account == account && u.Password == hashedPassword && u.Status == 1);
    }

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="user">用户对象</param>
    /// <returns>创建的用户</returns>
    public override async Task<User> CreateAsync(User user)
    {
        // 检查账号是否已存在
        var existingUser = await _dbSet.FirstOrDefaultAsync(u => u.Account == user.Account);
        if (existingUser != null)
        {
            throw new InvalidOperationException("账号已存在");
        }

        // 加密密码
        user.Password = HashPassword(user.Password);
        
        return await base.CreateAsync(user);
    }

    /// <summary>
    /// 更新用户密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="newPassword">新密码</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdatePasswordAsync(ulong userId, string newPassword)
    {
        var user = await FindByIdAsync(userId);
        if (user == null) return false;

        user.Password = HashPassword(newPassword);
        
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 根据账号查找用户
    /// </summary>
    /// <param name="account">账号</param>
    /// <returns>用户信息</returns>
    public async Task<User?> FindByAccountAsync(string account)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.Account == account);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="user">用户对象</param>
    /// <param name="password">密码</param>
    /// <returns>是否验证成功</returns>
    public bool VerifyPassword(User user, string password)
    {
        var hashedPassword = HashPassword(password);
        return user.Password == hashedPassword;
    }

    /// <summary>
    /// 获取用户信息（不包含密码）
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>用户信息</returns>
    public async Task<User?> GetUserInfoAsync(ulong userId)
    {
        var user = await FindByIdAsync(userId);
        if (user != null)
        {
            // 清空密码字段
            user.Password = string.Empty;
        }
        return user;
    }

    /// <summary>
    /// 更新用户状态
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="status">状态</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateStatusAsync(ulong userId, sbyte status)
    {
        return await UpdateAsync(userId, new Dictionary<string, object> { { "Status", status } });
    }

    /// <summary>
    /// 根据部门ID获取用户列表
    /// </summary>
    /// <param name="deptId">部门ID</param>
    /// <returns>用户列表</returns>
    public async Task<List<User>> GetUsersByDeptAsync(ulong deptId)
    {
        return await _dbSet
            .Where(u => u.DeptId == deptId && u.Status == 1)
            .ToListAsync();
    }

    /// <summary>
    /// 根据角色ID获取用户列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>用户列表</returns>
    public async Task<List<User>> GetUsersByRoleAsync(ulong roleId)
    {
        return await _dbSet
            .Where(u => u.RoleId == roleId && u.Status == 1)
            .ToListAsync();
    }

    /// <summary>
    /// 搜索用户
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    public async Task<(List<User> Items, long Total)> SearchUsersAsync(string keyword, int pageIndex, int pageSize)
    {
        return await GetPagedAsync(pageIndex, pageSize, query =>
            query.Where(u => u.Account.Contains(keyword) || 
                           u.Realname.Contains(keyword) || 
                           u.Mobile.Contains(keyword) ||
                           u.Email.Contains(keyword))
                 .OrderBy(u => u.Id));
    }

    /// <summary>
    /// 密码哈希（与Go代码保持一致）
    /// </summary>
    /// <param name="password">原始密码或MD5哈希后的密码</param>
    /// <param name="salt">盐值</param>
    /// <param name="isAlreadyMd5">密码是否已经MD5哈希</param>
    /// <returns>哈希后的密码</returns>
    public static string HashPassword(string password, string salt, bool isAlreadyMd5 = false)
    {
        // 如果密码还没有MD5哈希，先进行MD5哈希
        string md5Password = isAlreadyMd5 ? password : ComputeMd5(password);

        // 然后进行HMAC-SHA256
        using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(salt));
        var hashedBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(md5Password));
        var result = Convert.ToHexString(hashedBytes).ToLower();

        // 调试信息 - 使用静态方法，无法直接使用ILogger，所以使用Console.WriteLine
        Console.WriteLine($"哈希调试 - 输入密码: {password}, 是否已MD5: {isAlreadyMd5}, MD5密码: {md5Password}, 盐值: {salt}, 结果: {result}");

        return result;
    }

    /// <summary>
    /// 计算MD5哈希
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <returns>MD5哈希值</returns>
    private static string ComputeMd5(string input)
    {
        using var md5 = MD5.Create();
        var hashedBytes = md5.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hashedBytes).ToLower();
    }

    /// <summary>
    /// 密码哈希（实例方法）
    /// </summary>
    /// <param name="password">原始密码</param>
    /// <returns>哈希后的密码</returns>
    private string HashPassword(string password)
    {
        return HashPassword(password, _salt, false);
    }

    /// <summary>
    /// 从HttpContext获取当前用户ID
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <returns>用户ID</returns>
    public uint GetCurrentUserId(HttpContext httpContext)
    {
        var userIdClaim = httpContext.User.FindFirst(ClaimTypes.NameIdentifier);
        if (userIdClaim != null && uint.TryParse(userIdClaim.Value, out uint userId))
        {
            return userId;
        }
        return 1; // 默认返回管理员用户ID
    }
}
