using Microsoft.AspNetCore.Mvc;
using WcsNet.Models;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/test")]
public class TestController : ControllerBase
{
    /// <summary>
    /// 测试JSON序列化
    /// </summary>
    /// <returns>测试数据</returns>
    [HttpGet("json")]
    public ActionResult<ApiResponse<Department>> TestJson()
    {
        var dept = new Department
        {
            Id = 1,
            DeptName = "测试部门",
            DeptCode = "TEST001",
            ParentId = 0,
            SortNumber = 1,
            Status = 1,
            Brief = "这是一个测试部门"
        };

        return Ok(ApiResponse<Department>.Success(dept));
    }
}
