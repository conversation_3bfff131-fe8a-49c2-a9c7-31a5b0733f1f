import {
  vue_runtime_esm_bundler_exports
} from "./chunk-WS2SEKHB.js";
import "./chunk-4MZFK6UX.js";

// node_modules/.pnpm/responsive-storage@2.2.0/node_modules/responsive-storage/dist/index.mjs
var p = Object.defineProperty;
var d = Object.getOwnPropertyDescriptor;
var u = Object.getOwnPropertyNames;
var j = Object.prototype.hasOwnProperty;
var _ = (o, t, e) => t in o ? p(o, t, { enumerable: true, configurable: true, writable: true, value: e }) : o[t] = e;
var P = (o, t) => {
  for (var e in t) p(o, e, { get: t[e], enumerable: true });
};
var y = (o, t, e, s) => {
  if (t && typeof t == "object" || typeof t == "function") for (let r of u(t)) !j.call(o, r) && r !== e && p(o, r, { get: () => t[r], enumerable: !(s = d(t, r)) || s.enumerable });
  return o;
};
var m = (o, t, e) => (y(o, t, "default"), e && y(e, t, "default"));
var f = (o, t, e) => (_(o, typeof t != "symbol" ? t + "" : t, e), e);
var c = {};
P(c, { Vue: () => vue_runtime_esm_bundler_exports });
m(c, vue_runtime_esm_bundler_exports);
var g = class {
  static install(t, e) {
    let { nameSpace: s = this._nameSpace, memory: r } = e;
    return r && this.clearAll(s, r), new g(t, e);
  }
  static clearAll(t, e) {
    Object.keys(e).forEach((s) => {
      let r = t + s;
      Object.prototype.hasOwnProperty.call(window.localStorage, r) && window.localStorage.removeItem(r);
    });
  }
  static get(t) {
    return JSON.parse(window.localStorage.getItem(t));
  }
  static set(t, e) {
    e = typeof e == "object" ? JSON.stringify(e) : e, window.localStorage.setItem(t, e);
  }
  static getData(t, e) {
    if (Object.prototype.hasOwnProperty.call(window.localStorage, this._getStaticKey(e, t))) return JSON.parse(window.localStorage.getItem(this._getStaticKey(e, t)));
  }
  constructor(t, e) {
    let s = g, { version: r = 3, nameSpace: S = s._nameSpace, memory: w } = e, l = (a) => S + a, i = r === 3 ? (0, c.reactive)(w) : w;
    Object.keys(i).length === 0 && console.warn("key cannot be empty"), Object.keys(i).forEach((a) => {
      let b = i[a];
      s.set(l(a), b), Reflect.defineProperty(i, a, { get: () => s.get(l(a)), set: (h) => s.set(l(a), h), configurable: true }), r === 2 && t.util.defineReactive(i, a, i[a]);
    });
    let O = r === 3 ? t.config.globalProperties : t.prototype;
    Reflect.defineProperty(O, "$storage", { get: () => i });
  }
};
var n = g;
f(n, "_nameSpace", "rs-"), f(n, "_getStaticKey", (t, e) => `${t ?? g._nameSpace}${e}`);
export {
  n as default
};
//# sourceMappingURL=responsive-storage.js.map
