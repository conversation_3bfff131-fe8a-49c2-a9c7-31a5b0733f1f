namespace WcsNet.DTOs
{
    public class OperationLogDto
    {
        public int Id { get; set; }
        public string? OperatorName { get; set; }
        public string? Username { get; set; }
        public string? Module { get; set; }
        public string? Operation { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
        public string? OperationIp { get; set; }
        public string? IpAddress { get; set; }
        public string? OperationSystem { get; set; }
        public string? BrowserType { get; set; }
        public string? UserAgent { get; set; }
        public string? OperationStatus { get; set; }
        public int Status { get; set; }
        public int ExecutionTime { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime OperationTime { get; set; }
    }

    public class LoginLogDto
    {
        public int Id { get; set; }
        public string? Username { get; set; }
        public string? LoginIp { get; set; }
        public string? LoginLocation { get; set; }
        public string? Browser { get; set; }
        public string? Os { get; set; }
        public int Status { get; set; }
        public string? Message { get; set; }
        public DateTime LoginTime { get; set; }
    }

    public class DeptDto
    {
        public int Id { get; set; }
        public string? DeptName { get; set; }
        public string? DeptCode { get; set; }
        public int ParentId { get; set; }
        public int Sort { get; set; }
        public int Status { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class CreateDeptRequest
    {
        public string DeptName { get; set; } = string.Empty;
        public string DeptCode { get; set; } = string.Empty;
        public int ParentId { get; set; }
        public int Sort { get; set; }
    }

    public class UpdateDeptRequest
    {
        public string DeptName { get; set; } = string.Empty;
        public string DeptCode { get; set; } = string.Empty;
        public int ParentId { get; set; }
        public int Sort { get; set; }
        public int Status { get; set; }
    }

    public class SystemMenuDto
    {
        public int Id { get; set; }
        public string? MenuName { get; set; }
        public string? MenuType { get; set; }
        public string? Path { get; set; }
        public string? Component { get; set; }
        public int ParentId { get; set; }
        public int Sort { get; set; }
        public int Status { get; set; }
        public bool Visible { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class CreateMenuRequest
    {
        public string MenuName { get; set; } = string.Empty;
        public string MenuType { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public string Component { get; set; } = string.Empty;
        public int ParentId { get; set; }
        public int Sort { get; set; }
    }

    public class UpdateMenuRequest
    {
        public string MenuName { get; set; } = string.Empty;
        public string MenuType { get; set; } = string.Empty;
        public string Path { get; set; } = string.Empty;
        public string Component { get; set; } = string.Empty;
        public int ParentId { get; set; }
        public int Sort { get; set; }
        public int Status { get; set; }
        public bool Visible { get; set; }
    }

    public class RoleDto
    {
        public int Id { get; set; }
        public string? RoleName { get; set; }
        public string? RoleCode { get; set; }
        public int Status { get; set; }
        public string? Remark { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class CreateRoleRequest
    {
        public string RoleName { get; set; } = string.Empty;
        public string RoleCode { get; set; } = string.Empty;
        public string Remark { get; set; } = string.Empty;
    }

    public class UpdateRoleRequest
    {
        public string RoleName { get; set; } = string.Empty;
        public string RoleCode { get; set; } = string.Empty;
        public string Remark { get; set; } = string.Empty;
        public int Status { get; set; }
    }

    public class DictDto
    {
        public int Id { get; set; }
        public string? DictName { get; set; }
        public string? DictType { get; set; }
        public int Status { get; set; }
        public string? Remark { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class CreateDictRequest
    {
        public string DictName { get; set; } = string.Empty;
        public string DictType { get; set; } = string.Empty;
        public string Remark { get; set; } = string.Empty;
    }

    public class UpdateDictRequest
    {
        public string DictName { get; set; } = string.Empty;
        public string DictType { get; set; } = string.Empty;
        public string Remark { get; set; } = string.Empty;
        public int Status { get; set; }
    }

    public class RoleMenuRequest
    {
        public List<int> MenuIds { get; set; } = new List<int>();
    }

    public class AlarmDto
    {
        public int Id { get; set; }
        public string? InterfaceName { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public string? Status { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class AlarmPagedResponse
    {
        public List<AlarmDto> List { get; set; } = new List<AlarmDto>();
        public long Total { get; set; }
    }

    public class UserDto
    {
        public ulong Id { get; set; }
        public string? Account { get; set; }
        public string? Realname { get; set; }
        public string? Email { get; set; }
        public string? Mobile { get; set; }
        public string? Status { get; set; }
        public string? Gender { get; set; }
        public ulong DeptId { get; set; }
        public DateTime CreateTime { get; set; }
    }

    public class UserPagedResponse
    {
        public List<UserDto> List { get; set; } = new List<UserDto>();
        public long Total { get; set; }
    }

    // 为了兼容现有的PagedResponse，添加一个简单的包装类
    public class SimplePagedResponse<T>
    {
        public List<T> List { get; set; } = new List<T>();
        public long Total { get; set; }
    }
}
