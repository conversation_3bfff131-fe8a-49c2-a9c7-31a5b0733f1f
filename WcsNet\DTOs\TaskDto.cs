namespace WcsNet.DTOs;

public class TaskDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Type { get; set; }
    public int Status { get; set; }
    public int Priority { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class ActGroupDto
{
    public int Id { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public string GroupCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class PathDto
{
    public int Id { get; set; }
    public string PathName { get; set; } = string.Empty;
    public string PathCode { get; set; } = string.Empty;
    public string StartPoint { get; set; } = string.Empty;
    public string EndPoint { get; set; } = string.Empty;
    public int Distance { get; set; }
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class ApiLogDto
{
    public int Id { get; set; }
    public string Method { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string RequestBody { get; set; } = string.Empty;
    public string ResponseBody { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public int Duration { get; set; }
    public string UserAgent { get; set; } = string.Empty;
    public string ClientIp { get; set; } = string.Empty;
    public DateTime CreateTime { get; set; }
}

public class LaneDto
{
    public int Id { get; set; }
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class LoginLogDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string LoginIp { get; set; } = string.Empty;
    public string LoginLocation { get; set; } = string.Empty;
    public string Browser { get; set; } = string.Empty;
    public string Os { get; set; } = string.Empty;
    public int Status { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
}
