namespace WcsNet.DTOs;

public class TaskDto
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public int Type { get; set; }
    public int Status { get; set; }
    public int Priority { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class ActGroupDto
{
    public int Id { get; set; }
    public string GroupName { get; set; } = string.Empty;
    public string GroupCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class PathDto
{
    public int Id { get; set; }
    public string PathName { get; set; } = string.Empty;
    public string PathCode { get; set; } = string.Empty;
    public string StartPoint { get; set; } = string.Empty;
    public string EndPoint { get; set; } = string.Empty;
    public int Distance { get; set; }
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class ApiLogDto
{
    public int Id { get; set; }
    public string Method { get; set; } = string.Empty;
    public string Url { get; set; } = string.Empty;
    public string RequestBody { get; set; } = string.Empty;
    public string ResponseBody { get; set; } = string.Empty;
    public int StatusCode { get; set; }
    public int Duration { get; set; }
    public string UserAgent { get; set; } = string.Empty;
    public string ClientIp { get; set; } = string.Empty;
    public DateTime CreateTime { get; set; }
}

public class LaneDto
{
    public int Id { get; set; }
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class LoginLogDto
{
    public int Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string LoginIp { get; set; } = string.Empty;
    public string LoginLocation { get; set; } = string.Empty;
    public string Browser { get; set; } = string.Empty;
    public string Os { get; set; } = string.Empty;
    public int Status { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime LoginTime { get; set; }
}

public class WorkDto
{
    public int Id { get; set; }
    public string WorkCode { get; set; } = string.Empty;
    public string WorkName { get; set; } = string.Empty;
    public int WorkType { get; set; }
    public int Status { get; set; }
    public int Priority { get; set; }
    public string SourceLocation { get; set; } = string.Empty;
    public string TargetLocation { get; set; } = string.Empty;
    public string GoodsCode { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public DateTime CreateTime { get; set; }
    public DateTime UpdateTime { get; set; }
}

public class WorkStepDto
{
    public int Id { get; set; }
    public int WorkId { get; set; }
    public string StepName { get; set; } = string.Empty;
    public int StepOrder { get; set; }
    public int Status { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public DateTime CreateTime { get; set; }
}

public class TaskStatDto
{
    public int Id { get; set; }
    public DateTime StatDate { get; set; }
    public int TotalTasks { get; set; }
    public int CompletedTasks { get; set; }
    public int FailedTasks { get; set; }
    public int PendingTasks { get; set; }
    public int AvgExecutionTime { get; set; }
    public DateTime CreateTime { get; set; }
}



public class DeviceStatDto
{
    public int TotalDevices { get; set; }
    public int OnlineDevices { get; set; }
    public int OfflineDevices { get; set; }
    public int FaultDevices { get; set; }
    public int MaintenanceDevices { get; set; }
    public List<DeviceTypeStatDto> DeviceTypes { get; set; } = new List<DeviceTypeStatDto>();
}

public class DeviceTypeStatDto
{
    public string TypeName { get; set; } = string.Empty;
    public int Count { get; set; }
}

public class StockStatDto
{
    public int TotalStocks { get; set; }
    public int OccupiedStocks { get; set; }
    public int EmptyStocks { get; set; }
    public int ReservedStocks { get; set; }
    public double UtilizationRate { get; set; }
    public List<LaneStockStatDto> LaneStats { get; set; } = new List<LaneStockStatDto>();
}

public class LaneStockStatDto
{
    public string LaneCode { get; set; } = string.Empty;
    public int TotalStocks { get; set; }
    public int OccupiedStocks { get; set; }
}

public class RealtimeStatDto
{
    public DateTime CurrentTime { get; set; }
    public int ActiveTasks { get; set; }
    public int QueuedTasks { get; set; }
    public int OnlineDevices { get; set; }
    public double SystemLoad { get; set; }
    public double MemoryUsage { get; set; }
    public double CpuUsage { get; set; }
    public int NetworkTraffic { get; set; }
}

public class LaneDeviceDto
{
    public int Id { get; set; }
    public int DeviceId { get; set; }
    public int LaneId { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public string DeviceCode { get; set; } = string.Empty;
    public string DeviceType { get; set; } = string.Empty;
    public int Status { get; set; }
    public DateTime CreateTime { get; set; }
}


