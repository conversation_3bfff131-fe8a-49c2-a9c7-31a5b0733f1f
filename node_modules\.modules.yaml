hoistPattern:
  - '*'
hoistedDependencies:
  cross-spawn@7.0.6:
    cross-spawn: private
  isexe@2.0.0:
    isexe: private
  path-key@3.1.1:
    path-key: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  which@2.0.2:
    which: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.2
pendingBuilds: []
prunedAt: Mon, 23 Jun 2025 07:37:39 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\SourceCode\Test\TempTest\Ai\wcs\node_modules\.pnpm
virtualStoreDirMaxLength: 60
