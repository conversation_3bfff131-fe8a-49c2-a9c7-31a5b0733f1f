E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.csproj.AssemblyReference.cache
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.GeneratedMSBuildEditorConfig.editorconfig
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.AssemblyInfoInputs.cache
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.AssemblyInfo.cs
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.csproj.CoreCompileInputs.cache
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.MvcApplicationPartsAssemblyInfo.cs
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.MvcApplicationPartsAssemblyInfo.cache
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\appsettings.Development.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\appsettings.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.staticwebassets.endpoints.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.exe
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.deps.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.runtimeconfig.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\WcsNet.pdb
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Relational.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Binder.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.DependencyModel.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Microsoft.OpenApi.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\MySqlConnector.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Pomelo.EntityFrameworkCore.MySql.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.AspNetCore.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Extensions.Hosting.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Extensions.Logging.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Formatting.Compact.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Settings.Configuration.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Sinks.Console.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Sinks.Debug.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Serilog.Sinks.File.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\System.IO.Pipelines.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\System.Text.Encodings.Web.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\System.Text.Json.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\scopedcss\bundle\WcsNet.styles.css
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets.build.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets.development.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets.build.endpoints.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets\msbuild.WcsNet.Microsoft.AspNetCore.StaticWebAssets.props
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets\msbuild.WcsNet.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets\msbuild.build.WcsNet.props
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets\msbuild.buildMultiTargeting.WcsNet.props
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets\msbuild.buildTransitive.WcsNet.props
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets.pack.json
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.csproj.Up2Date
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\refint\WcsNet.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.pdb
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\WcsNet.genruntimeconfig.cache
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\ref\WcsNet.dll
E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
