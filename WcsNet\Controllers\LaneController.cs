using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api")]
public class LaneController : ControllerBase
{
    /// <summary>
    /// 获取车道列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>车道列表</returns>
    [HttpGet("lanes")]
    public ActionResult<ApiResponse<PagedResponse<LaneDto>>> GetLanes(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟车道数据
            var lanes = new List<LaneDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                lanes.Add(new LaneDto
                {
                    Id = i,
                    LaneName = $"车道{i}",
                    LaneCode = $"LANE_{i:D3}",
                    Description = $"车道{i}的描述信息",
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                    UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
                });
            }

            var response = new PagedResponse<LaneDto>
            {
                List = lanes,
                Total = 20 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<LaneDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<LaneDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取车道详情
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>车道详情</returns>
    [HttpGet("lanes/{id}")]
    public ActionResult<ApiResponse<LaneDto>> GetLane(int id)
    {
        try
        {
            var random = new Random();
            
            var lane = new LaneDto
            {
                Id = id,
                LaneName = $"车道{id}",
                LaneCode = $"LANE_{id:D3}",
                Description = $"车道{id}的描述信息",
                Status = random.Next(0, 2),
                CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
            };

            return Ok(ApiResponse<LaneDto>.Success(lane));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LaneDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建车道
    /// </summary>
    /// <param name="request">车道创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("lanes")]
    public ActionResult<ApiResponse<LaneDto>> CreateLane([FromBody] CreateLaneRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(1000, 9999);
            
            var lane = new LaneDto
            {
                Id = newId,
                LaneName = request.LaneName,
                LaneCode = request.LaneCode,
                Description = request.Description,
                Status = 1,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };

            return Ok(ApiResponse<LaneDto>.Success(lane));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LaneDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新车道
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <param name="request">车道更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("lanes/{id}")]
    public ActionResult<ApiResponse<object>> UpdateLane(int id, [FromBody] UpdateLaneRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "车道更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除车道
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("lanes/{id}")]
    public ActionResult<ApiResponse<object>> DeleteLane(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "车道删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }
}

public class CreateLaneRequest
{
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class UpdateLaneRequest
{
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
}
