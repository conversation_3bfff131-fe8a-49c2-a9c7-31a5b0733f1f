using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api")]
public class LaneController : ControllerBase
{
    private readonly WcsDbContext _context;

    public LaneController(WcsDbContext context)
    {
        _context = context;
    }
    /// <summary>
    /// 获取车道列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>车道列表</returns>
    [HttpGet("lanes")]
    public async Task<ActionResult<ApiResponse<PagedResponse<LaneDto>>>> GetLanes(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取车道数据
            var total = await _context.Lanes.CountAsync();
            var lanes = await _context.Lanes
                .OrderBy(l => l.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new LaneDto
                {
                    Id = (int)l.Id,
                    LaneName = l.Lane<PERSON>ame,
                    LaneCode = l.LaneCode,
                    Description = l.Description,
                    Status = l.Status,
                    CreateTime = l.CreatedAt.HasValue ? l.CreatedAt.Value : DateTime.Now,
                    UpdateTime = l.UpdatedAt.HasValue ? l.UpdatedAt.Value : DateTime.Now
                })
                .ToListAsync();

            var response = new PagedResponse<LaneDto>
            {
                List = lanes,
                Total = total
            };

            return Ok(ApiResponse<PagedResponse<LaneDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<LaneDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取车道详情
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>车道详情</returns>
    [HttpGet("lanes/{id}")]
    public ActionResult<ApiResponse<LaneDto>> GetLane(int id)
    {
        try
        {
            var random = new Random();
            
            var lane = new LaneDto
            {
                Id = id,
                LaneName = $"车道{id}",
                LaneCode = $"LANE_{id:D3}",
                Description = $"车道{id}的描述信息",
                Status = random.Next(0, 2),
                CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
            };

            return Ok(ApiResponse<LaneDto>.Success(lane));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LaneDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建车道
    /// </summary>
    /// <param name="request">车道创建请求</param>
    /// <returns>创建结果</returns>
    [HttpPost("lanes")]
    public ActionResult<ApiResponse<LaneDto>> CreateLane([FromBody] CreateLaneRequest request)
    {
        try
        {
            var random = new Random();
            var newId = random.Next(1000, 9999);
            
            var lane = new LaneDto
            {
                Id = newId,
                LaneName = request.LaneName,
                LaneCode = request.LaneCode,
                Description = request.Description,
                Status = 1,
                CreateTime = DateTime.Now,
                UpdateTime = DateTime.Now
            };

            return Ok(ApiResponse<LaneDto>.Success(lane));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LaneDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新车道
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <param name="request">车道更新请求</param>
    /// <returns>更新结果</returns>
    [HttpPut("lanes/{id}")]
    public ActionResult<ApiResponse<object>> UpdateLane(int id, [FromBody] UpdateLaneRequest request)
    {
        try
        {
            // 模拟更新操作
            return Ok(ApiResponse<object>.Success(new { message = "车道更新成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除车道
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("lanes/{id}")]
    public ActionResult<ApiResponse<object>> DeleteLane(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "车道删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取车道设备列表
    /// </summary>
    /// <param name="lane_id">车道ID</param>
    /// <returns>车道设备列表</returns>
    [HttpGet("lanes/devices")]
    public ActionResult<ApiResponse<List<LaneDeviceDto>>> GetLaneDevices(int? lane_id = null)
    {
        try
        {
            // 模拟车道设备数据
            var devices = new List<LaneDeviceDto>();
            var random = new Random();
            var deviceTypes = new[] { "AGV", "堆垛机", "输送线", "扫码器" };

            for (int i = 1; i <= 5; i++)
            {
                devices.Add(new LaneDeviceDto
                {
                    Id = i,
                    DeviceId = i,
                    LaneId = lane_id ?? random.Next(1, 5),
                    DeviceName = $"设备{i}",
                    DeviceCode = $"DEV_{i:D3}",
                    DeviceType = deviceTypes[random.Next(deviceTypes.Length)],
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30))
                });
            }

            return Ok(ApiResponse<List<LaneDeviceDto>>.Success(devices));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<LaneDeviceDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取车道可用设备列表
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>可用设备列表</returns>
    [HttpGet("lanes/useable/devices")]
    public ActionResult<ApiResponse<List<LaneDeviceDto>>> GetUseableLaneDevices(int? id = null)
    {
        try
        {
            // 模拟可用设备数据
            var devices = new List<LaneDeviceDto>();
            var random = new Random();
            var deviceTypes = new[] { "AGV", "堆垛机", "输送线", "扫码器" };

            for (int i = 1; i <= 3; i++)
            {
                devices.Add(new LaneDeviceDto
                {
                    Id = i,
                    DeviceId = i,
                    LaneId = id ?? random.Next(1, 5),
                    DeviceName = $"可用设备{i}",
                    DeviceCode = $"AVAIL_DEV_{i:D3}",
                    DeviceType = deviceTypes[random.Next(deviceTypes.Length)],
                    Status = 1, // 可用状态
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30))
                });
            }

            return Ok(ApiResponse<List<LaneDeviceDto>>.Success(devices));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<LaneDeviceDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取车道设备ID列表
    /// </summary>
    /// <param name="id">车道ID</param>
    /// <returns>设备ID列表</returns>
    [HttpGet("lanes/deviceids")]
    public ActionResult<ApiResponse<List<int>>> GetLaneDeviceIds(int id)
    {
        try
        {
            // 模拟设备ID列表
            var deviceIds = new List<int> { 1, 2, 3, 4, 5 };
            return Ok(ApiResponse<List<int>>.Success(deviceIds));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<int>>.Error(500, ex.Message));
        }
    }
}

public class CreateLaneRequest
{
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
}

public class UpdateLaneRequest
{
    public string LaneName { get; set; } = string.Empty;
    public string LaneCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int Status { get; set; }
}
