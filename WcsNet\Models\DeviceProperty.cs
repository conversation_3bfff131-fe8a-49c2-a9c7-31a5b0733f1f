﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_device_property")]
public class DeviceProperty : BaseModel
{
    [Column("device_id")]
    public ulong DeviceId { get; set; }

    [Required]
    [StringLength(64)]
    [Column("prop_code")]
    public string PropCode { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("prop_name")]
    public string PropName { get; set; } = string.Empty;

    [StringLength(64)]
    public string Addr { get; set; } = string.Empty;

    public sbyte Direction { get; set; } = 1;

    [Column("modbus_type")]
    public sbyte ModbusType { get; set; } = 1;

    [Column("plc_type")]
    [StringLength(16)]
    public string PlcType { get; set; } = string.Empty;

    [Column("prop_length")]
    public sbyte PropLength { get; set; } = 1;

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;
}
