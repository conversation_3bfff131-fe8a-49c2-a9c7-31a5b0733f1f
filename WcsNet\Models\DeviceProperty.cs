using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

/// <summary>
/// 设备属性
/// </summary>
[Table("wcs_device_property")]
public class DeviceProperty : BaseModel
{
    /// <summary>
    /// 所属设备ID
    /// </summary>
    [Column("device_id")]
    public ulong DeviceId { get; set; }

    /// <summary>
    /// 属性编码
    /// </summary>
    [Required]
    [StringLength(64)]
    [Column("prop_code")]
    public string PropCode { get; set; } = string.Empty;

    /// <summary>
    /// 属性名称
    /// </summary>
    [Required]
    [StringLength(128)]
    [Column("prop_name")]
    public string PropName { get; set; } = string.Empty;

    /// <summary>
    /// 属性地址
    /// </summary>
    [StringLength(64)]
    public string Addr { get; set; } = string.Empty;

    /// <summary>
    /// 方向
    /// </summary>
    public sbyte Direction { get; set; } = 0;

    /// <summary>
    /// Modbus类型
    /// </summary>
    [Column("modbus_type")]
    public sbyte ModbusType { get; set; } = 0;

    /// <summary>
    /// PLC类型
    /// </summary>
    [StringLength(16)]
    [Column("plc_type")]
    public string PlcType { get; set; } = string.Empty;

    /// <summary>
    /// 属性长度
    /// </summary>
    [Column("prop_length")]
    public sbyte PropLength { get; set; } = 0;

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    /// <summary>
    /// 更新人ID
    /// </summary>
    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 关联的设备
    /// </summary>
    [ForeignKey("DeviceId")]
    public virtual Device? Device { get; set; }
}
