import {
  English
} from "./chunk-LT25EHPK.js";
import {
  ClickOutside,
  ElAutocomplete,
  ElAvatar,
  ElBacktop,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElCard,
  ElCascader,
  ElCheckbox,
  ElCheckboxGroup,
  ElCol,
  ElColorPicker,
  ElContainer,
  ElDatePicker,
  ElDescriptions,
  ElDescriptionsItem,
  ElDialog,
  ElDivider,
  ElDrawer,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElForm,
  ElFormItem,
  ElHeader,
  ElIcon,
  ElImage,
  ElInput,
  ElInputNumber,
  ElLink,
  ElMain,
  ElMenu,
  ElMenuItem,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElPagination,
  ElPopover,
  ElProgress,
  ElRadio,
  ElRadioGroup,
  ElRate,
  ElRow,
  ElScrollbar,
  ElSelect,
  ElSlider,
  ElStep,
  ElSteps,
  ElSubMenu,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElTag,
  ElText,
  ElTimePicker,
  ElTimeSelect,
  ElTooltip,
  ElTransfer,
  ElTreeSelect,
  arrow_down_bold_default,
  arrow_down_default,
  arrow_up_default,
  document_copy_default,
  expand_default,
  fold_default,
  import_dayjs,
  localeContextKey,
  question_filled_default,
  refresh_right_default,
  search_default,
  select_default,
  setting_default,
  useFormDisabled,
  user_default,
  vLoading,
  version
} from "./chunk-IOAK44TL.js";
import "./chunk-3OLK3RAD.js";
import {
  cloneDeep_default,
  get_default,
  orderBy_default,
  set_default
} from "./chunk-SVMG6VQH.js";
import {
  sortable_esm_default
} from "./chunk-I3VAMALH.js";
import "./chunk-HOL3P2P3.js";
import "./chunk-IDBKCDYW.js";
import {
  Fragment,
  TransitionGroup,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  guardReactiveProps,
  h,
  inject,
  isReactive,
  isRef,
  isVNode,
  mergeProps,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onMounted,
  openBlock,
  provide,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDynamicComponent,
  shallowRef,
  toDisplayString,
  toHandlers,
  toRefs,
  unref,
  useAttrs,
  useSlots,
  watch,
  watchEffect,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-WS2SEKHB.js";
import "./chunk-4MZFK6UX.js";

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/make-installer.mjs
var makeInstaller = (components = []) => {
  const install2 = (app) => {
    components.forEach((component) => app.component(component.name, component));
  };
  return {
    install: install2
  };
};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/utils/is.mjs
var objectToString = Object.prototype.toString;
var toTypeString = (value) => objectToString.call(value);
var toRawType = (value) => {
  return toTypeString(value).slice(8, -1);
};
var isArray = Array.isArray;
var isDate = (val) => toTypeString(val) === "[object Date]";
var isFunction = (val) => typeof val === "function";
var isString = (val) => typeof val === "string";
var isBoolean = (val) => typeof val === "boolean";
var isObject = (val) => val !== null && typeof val === "object";
var isPromise = (val) => {
  return isObject(val) && isFunction(val.then) && isFunction(val.catch);
};
var isPlainObject = (val) => toTypeString(val) === "[object Object]";
function isUrl(url) {
  const regex = new RegExp(
    "^(https?:\\/\\/)?((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|((\\d{1,3}\\.){3}\\d{1,3}))(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*(\\?[;&a-z\\d%_.~+=-]*)?(\\#[-a-z\\d_]*)?$",
    "i"
  );
  return regex.test(url);
}
var isSVGElement = (tag) => typeof SVGElement !== "undefined" && tag instanceof SVGElement;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/utils/format.mjs
function formatDate(date, format = "YYYY-MM-DD HH:mm:ss") {
  if (!date)
    return "";
  return (0, import_dayjs.default)(date || /* @__PURE__ */ new Date()).format(format);
}
function formatMoney(val, format = "￥", decimal = 2) {
  if (!val)
    return "";
  return `${format}${Number(val).toFixed(decimal)}`;
}

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/utils/index.mjs
var getTableKey = (item, hasEditable = false) => hasEditable && isBoolean(item.editable) ? item.label + item.prop + item.editable : item.label + item.prop;
var getTooltip = (tooltip) => {
  const tooltipData = unref(tooltip);
  if (isString(tooltipData)) {
    return { content: tooltipData };
  }
  if (isPlainObject(tooltipData)) {
    return tooltipData;
  }
  return { content: "" };
};
var throwError = (data, type) => {
  if (!isPlainObject(data)) {
    throw new Error(`${type} expected Object but got ${toRawType(data)}`);
  }
};
var getCustomProps = async (props, value, row, index, type) => {
  try {
    let data = {};
    const params = { row, index };
    if (!props) {
      data = {};
    } else if (isRef(props)) {
      data = props.value;
    } else if (isPlainObject(props)) {
      data = { ...props };
    } else if (isFunction(props)) {
      data = await props(value, params);
    } else if (isPromise(props)) {
      data = await props;
    } else {
      data = props;
    }
    throwError(data, type);
    return data;
  } catch (error) {
    return Promise.reject(error);
  }
};
var getSlotName = (type, prop) => {
  return prop ? `plus-${type}-${prop}` : `plus-${type}`;
};
var getFieldSlotName = (prop) => {
  return `${getSlotName("field", prop)}`;
};
var getLabelSlotName = (prop) => {
  return `${getSlotName("label", prop)}`;
};
var getExtraSlotName = (prop) => {
  return `${getSlotName("extra", prop)}`;
};
var getTableHeaderSlotName = (prop) => {
  return `${getSlotName("header", prop)}`;
};
var getTableCellSlotName = (prop) => {
  return `${getSlotName("cell", prop)}`;
};
var getDescSlotName = (prop) => {
  return `${getSlotName("desc", prop)}`;
};
var getDescLabelSlotName = (prop) => {
  return `${getSlotName("desc-label", prop)}`;
};
var filterSlots = (slots, name) => {
  const data = {};
  Object.keys(slots || {}).forEach((key) => {
    if (key.startsWith(name)) {
      data[key] = slots[key];
    }
  });
  return data;
};
var getValue = (target, key) => {
  return get_default(target, key);
};
var setValue = (target, key, value) => {
  return set_default(target, key, value);
};
var compareVersion = (version1, version22) => {
  const arr1 = version1.split(".").map((item) => Number(item));
  const arr2 = version22.split(".").map((item) => Number(item));
  const length = Math.max(arr1.length, arr2.length);
  for (let i = 0; i < length; i++) {
    if ((arr1[i] || 0) > (arr2[i] || 0))
      return 1;
    if ((arr1[i] || 0) < (arr2[i] || 0))
      return -1;
  }
  return 0;
};
var versionIsLessThan260 = compareVersion(version, "2.6.0") < 0;
var getLabel = (label) => label ? unref(label) : "";

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/hooks/useGetOptions.mjs
var throwError2 = (data) => {
  if (!isArray(data)) {
    console.error("Uncaught TypeError: ", `options expected Array but got ${toRawType(data)}`);
  }
};
var getOptionsByOptionsMap = (options, props) => {
  const optionsMap = props.optionsMap;
  const valueType = props.valueType;
  if (valueType === "cascader" || !isPlainObject(optionsMap)) {
    return options;
  }
  const data = options.map((item) => {
    const temp = cloneDeep_default(item);
    const label = optionsMap.label || "label";
    const value = optionsMap.value || "value";
    const __origin = {
      [label]: temp[label],
      [value]: temp[value]
    };
    optionsMap.label && Reflect.deleteProperty(temp, label);
    optionsMap.value && Reflect.deleteProperty(temp, value);
    return { ...temp, __origin, label: item[label], value: item[value] };
  });
  return data;
};
var useGetOptions = (props) => {
  const options = ref([]);
  const optionsIsReady = ref(false);
  if (!props.options) {
    options.value = [];
    optionsIsReady.value = true;
  } else if (isRef(props.options) || isReactive(props.options) || isArray(props.options)) {
    watch(
      () => props.options,
      (val) => {
        const value = isRef(val) ? val.value : val;
        options.value = getOptionsByOptionsMap(value, props);
        optionsIsReady.value = true;
      },
      {
        immediate: true,
        deep: true
      }
    );
  } else if (isFunction(props.options)) {
    const getValue2 = props.options;
    const result = getValue2(props);
    if (isPromise(result)) {
      result.then((value) => {
        options.value = getOptionsByOptionsMap(value, props);
        optionsIsReady.value = true;
        throwError2(options.value);
      }).catch((err) => {
        throw err;
      });
    } else {
      options.value = getOptionsByOptionsMap(result, props);
      optionsIsReady.value = true;
    }
  } else if (isPromise(props.options)) {
    const getValue2 = props.options;
    getValue2.then((value) => {
      options.value = getOptionsByOptionsMap(value, props);
      optionsIsReady.value = true;
      throwError2(options.value);
    }).catch((err) => {
      throw err;
    });
  } else {
    optionsIsReady.value = true;
    throwError2(props.options);
  }
  return { customOptions: options, customOptionsIsReady: optionsIsReady };
};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/hooks/useLocale.mjs
var buildTranslator = (locale) => (path, option) => translate(path, option, unref(locale));
var translate = (path, option, locale) => get_default(locale, path, path).replace(
  /\{(\w+)\}/g,
  (_, key) => {
    var _a;
    return `${(_a = option == null ? void 0 : option[key]) != null ? _a : `{${key}}`}`;
  }
);
var buildLocaleContext = (locale) => {
  const lang = computed(() => unref(locale).name);
  const localeRef = isRef(locale) ? locale : ref(locale);
  return {
    lang,
    locale: localeRef,
    t: buildTranslator(locale)
  };
};
var useLocale = (localeOverrides) => {
  const locale = localeOverrides || inject(localeContextKey, ref());
  return buildLocaleContext(computed(() => {
    var _a;
    return ((_a = locale.value) == null ? void 0 : _a.plus) ? locale.value : English;
  }));
};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/constants/page.mjs
var DefaultPageSizeList = [10, 20, 30, 40, 50, 100, 200, 300, 400, 500];
var DefaultPageInfo = {
  page: 1,
  pageSize: 10
};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/constants/form.mjs
var TableFormRefInjectionKey = Symbol("tableFormRefInjectionKey");
var TableFormFieldRefInjectionKey = Symbol("tableFormFieldRefInjectionKey");
var DatePickerValueIsArrayList = ["datetimerange", "daterange", "monthrange"];
var ValueIsNumberList = ["rate", "input-number", "slider"];
var ValueIsBooleanList = ["switch"];
var ValueIsArrayList = [
  "checkbox",
  "cascader",
  "plus-date-picker",
  "plus-input-tag",
  "transfer"
];

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/hooks/useTable.mjs
function useTable(_pageInfo) {
  const defaultPageInfo = unref(_pageInfo) || DefaultPageInfo;
  const tableData = ref([]);
  const pageInfo = ref({ ...defaultPageInfo });
  const total = ref(0);
  const loadingStatus = ref(false);
  const buttons = shallowRef([]);
  return {
    tableData,
    pageInfo,
    total,
    loadingStatus,
    buttons
  };
}

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog/src/index.vue2.mjs
var _hoisted_1 = { class: "plus-dialog-body" };
var _sfc_main = defineComponent({
  ...{
    name: "PlusDialog",
    inheritAttrs: false
  },
  __name: "index",
  props: {
    modelValue: { type: Boolean, default: false },
    confirmText: { default: "" },
    cancelText: { default: "" },
    confirmLoading: { type: Boolean, default: false },
    hasFooter: { type: Boolean, default: true },
    footerAlign: { default: "right" },
    top: { default: "15vh" },
    width: { default: "460px" },
    title: { default: "" }
  },
  emits: ["update:modelValue", "cancel", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const style = computed(() => ({
      justifyContent: props.footerAlign === "left" ? "flex-start" : props.footerAlign === "center" ? "center" : "flex-end"
    }));
    const subVisible = ref(false);
    const { t } = useLocale();
    watchEffect(() => {
      subVisible.value = props.modelValue;
    });
    const handleConfirm = () => {
      emit("confirm");
    };
    const handleCancel = () => {
      emit("update:modelValue", false);
      emit("cancel");
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElDialog), mergeProps({
        modelValue: subVisible.value,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => subVisible.value = $event),
        top: _ctx.top,
        width: _ctx.width,
        title: _ctx.title || unref(t)("plus.dialog.title"),
        "close-on-click-modal": false,
        "close-on-press-escape": false,
        "append-to-body": false,
        "before-close": handleCancel,
        class: "plus-dialog"
      }, _ctx.$attrs), createSlots({
        default: withCtx(() => [
          createBaseVNode("div", _hoisted_1, [
            renderSlot(_ctx.$slots, "default")
          ])
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        _ctx.$slots.header ? {
          name: "header",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "header")
          ]),
          key: "0"
        } : void 0,
        _ctx.hasFooter ? {
          name: "footer",
          fn: withCtx(() => [
            createBaseVNode(
              "div",
              {
                class: "plus-dialog-footer",
                style: normalizeStyle(style.value)
              },
              [
                renderSlot(_ctx.$slots, "footer", {}, () => [
                  createVNode(unref(ElButton), { onClick: handleCancel }, {
                    default: withCtx(() => [
                      createTextVNode(
                        toDisplayString(_ctx.cancelText || unref(t)("plus.dialog.cancelText")),
                        1
                        /* TEXT */
                      )
                    ]),
                    _: 1
                    /* STABLE */
                  }),
                  createVNode(unref(ElButton), {
                    type: "primary",
                    loading: _ctx.confirmLoading,
                    onClick: handleConfirm
                  }, {
                    default: withCtx(() => [
                      createTextVNode(
                        toDisplayString(_ctx.confirmText || unref(t)("plus.dialog.confirmText")),
                        1
                        /* TEXT */
                      )
                    ]),
                    _: 1
                    /* STABLE */
                  }, 8, ["loading"])
                ])
              ],
              4
              /* STYLE */
            )
          ]),
          key: "1"
        } : void 0
      ]), 1040, ["modelValue", "top", "width", "title"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/_virtual/_plugin-vue_export-helper.mjs
var _export_sfc = (sfc, props) => {
  const target = sfc.__vccOpts || sfc;
  for (const [key, val] of props) {
    target[key] = val;
  }
  return target;
};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog/src/index.vue.mjs
var Dialog = _export_sfc(_sfc_main, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog/index.mjs
var PlusDialog = Dialog;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/pagination/src/index.vue2.mjs
var _hoisted_12 = { class: "plus-pagination" };
var _hoisted_2 = createBaseVNode(
  "span",
  null,
  null,
  -1
  /* HOISTED */
);
var _hoisted_3 = createBaseVNode(
  "span",
  null,
  null,
  -1
  /* HOISTED */
);
var _sfc_main2 = defineComponent({
  ...{
    name: "PlusPagination"
  },
  __name: "index",
  props: {
    modelValue: { default: () => ({ ...DefaultPageInfo }) },
    total: { default: 0 },
    pageSizeList: { default: () => [...DefaultPageSizeList] },
    align: { default: "right" }
  },
  emits: ["update:modelValue", "change", "size-change", "current-change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const pageInfo = ref({ ...DefaultPageInfo });
    watchEffect(() => {
      pageInfo.value = { ...props.modelValue };
    });
    const handleEmit = () => {
      emit("update:modelValue", pageInfo.value);
      emit("change", pageInfo.value);
    };
    const handleSizeChange = (pageSize) => {
      pageInfo.value.pageSize = pageSize;
      pageInfo.value.page = 1;
      handleEmit();
      emit("size-change", pageSize);
    };
    const handleCurrentChange = (page) => {
      pageInfo.value.page = page;
      handleEmit();
      emit("current-change", page);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_12, [
        _ctx.align === "right" ? renderSlot(_ctx.$slots, "pagination-left", { key: 0 }, () => [
          _hoisted_2
        ]) : createCommentVNode("v-if", true),
        createVNode(unref(ElPagination), mergeProps({
          layout: "total, sizes, prev, pager, next, jumper",
          background: false,
          "current-page": pageInfo.value.page,
          "page-size": pageInfo.value.pageSize,
          total: _ctx.total,
          "page-sizes": _ctx.pageSizeList
        }, _ctx.$attrs, {
          onSizeChange: handleSizeChange,
          onCurrentChange: handleCurrentChange
        }), null, 16, ["current-page", "page-size", "total", "page-sizes"]),
        _ctx.align === "left" ? renderSlot(_ctx.$slots, "pagination-right", { key: 1 }, () => [
          _hoisted_3
        ]) : createCommentVNode("v-if", true)
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/pagination/src/index.vue.mjs
var Pagination = _export_sfc(_sfc_main2, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/pagination/index.mjs
var PlusPagination = Pagination;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-action-bar.vue2.mjs
var _hoisted_13 = { class: "plus-table-action-bar__dropdown__link" };
var _hoisted_22 = { class: "plus-table-action-bar__more-text" };
var _sfc_main3 = defineComponent({
  ...{
    name: "PlusTableActionBar"
  },
  __name: "table-action-bar",
  props: {
    label: { default: "" },
    fixed: { default: "right" },
    showNumber: { default: 3 },
    type: { default: "link" },
    buttons: { default: () => [] },
    width: { default: 200 },
    actionBarTableColumnProps: { default: () => ({}) }
  },
  emits: ["clickAction", "clickActionConfirmCancel"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const formRefs = inject(TableFormRefInjectionKey);
    const getSubButtons = (row, index) => {
      const data = props.buttons.filter((item) => {
        if (isFunction(item.show)) {
          const tempFunction = item.show;
          const isShow = tempFunction(row, index, item);
          return unref(isShow) !== false;
        }
        return unref(item.show) !== false;
      });
      const preButtons = data.slice(0, props.showNumber);
      const nextButtons = data.slice(props.showNumber);
      const showMore = data.length > props.showNumber;
      return {
        showMore,
        preButtons,
        nextButtons
      };
    };
    const render = (row, buttonRow, index, rest) => {
      const buttonRowProps = isFunction(buttonRow.props) ? buttonRow.props(row, index, buttonRow) : unref(buttonRow.props);
      if (props.type === "icon") {
        return h(
          ElTooltip,
          { placement: "top", content: unref(buttonRow.text), ...buttonRow.tooltipProps },
          () => withDirectives(
            h(
              ElIcon,
              {
                size: 16,
                ...buttonRowProps,
                onClick: (event) => handleClickAction(row, buttonRow, index, event, rest)
              },
              () => buttonRow.icon ? h(buttonRow.icon) : ""
            ),
            buttonRow.directives || []
          )
        );
      } else {
        const Tag = props.type === "button" ? ElButton : ElLink;
        const defaultProps = props.type === "link" ? { href: "javaScript:;" } : {};
        return withDirectives(
          h(
            Tag,
            {
              size: "small",
              ...defaultProps,
              ...buttonRowProps,
              onClick: (event) => handleClickAction(row, buttonRow, index, event, rest)
            },
            () => {
              if (isFunction(buttonRow.text)) {
                const tempFunction = buttonRow.text;
                const text = tempFunction(row, index, buttonRow);
                return unref(text);
              } else {
                return unref(buttonRow.text);
              }
            }
          ),
          buttonRow.directives || []
        );
      }
    };
    const handleClickAction = (row, buttonRow, index, e, rest) => {
      var _a, _b;
      const callbackParams = {
        row,
        buttonRow,
        index,
        rowIndex: index,
        e,
        formRefs: formRefs.value[index],
        ...rest
      };
      if (buttonRow.onClick && isFunction(buttonRow.onClick)) {
        buttonRow.onClick(callbackParams);
      }
      if (buttonRow.confirm) {
        let message = t("plus.table.confirmToPerformThisOperation");
        let title = t("plus.table.prompt");
        let options = void 0;
        let appContext = null;
        if (isPlainObject(buttonRow.confirm) && typeof buttonRow.confirm !== "boolean") {
          const tempTitle = isFunction(buttonRow.confirm.title) ? buttonRow.confirm.title(callbackParams) : buttonRow.confirm.title;
          if (tempTitle) {
            title = tempTitle;
          }
          const tempMessage = isFunction(buttonRow.confirm.message) ? buttonRow.confirm.message(callbackParams) : buttonRow.confirm.message;
          if (tempMessage) {
            message = tempMessage;
          }
          options = (_a = buttonRow.confirm) == null ? void 0 : _a.options;
          appContext = (_b = buttonRow.confirm) == null ? void 0 : _b.appContext;
        }
        ElMessageBox.confirm(message, title, options, appContext).then(() => {
          if (buttonRow.onConfirm && isFunction(buttonRow.onConfirm)) {
            buttonRow.onConfirm(callbackParams);
          }
          emit("clickAction", callbackParams);
        }).catch(() => {
          if (buttonRow.onCancel && isFunction(buttonRow.onCancel)) {
            buttonRow.onCancel(callbackParams);
          }
          emit("clickActionConfirmCancel", callbackParams);
        });
      } else {
        emit("clickAction", callbackParams);
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTableColumn), mergeProps({
        key: "actionBar",
        "class-name": "plus-table-action-bar",
        label: unref(_ctx.label) || unref(t)("plus.table.action"),
        fixed: _ctx.fixed || "right",
        width: _ctx.width || 200
      }, _ctx.actionBarTableColumnProps), {
        default: withCtx(({ row, $index, ...rest }) => [
          createCommentVNode(" 显示出来的按钮 "),
          (openBlock(true), createElementBlock(
            Fragment,
            null,
            renderList(getSubButtons(row, $index).preButtons, (buttonRow) => {
              return openBlock(), createBlock(resolveDynamicComponent(() => render(row, buttonRow, $index, rest)), {
                key: buttonRow.text
              });
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          createCommentVNode(" 隐藏的按钮 "),
          getSubButtons(row, $index).showMore ? (openBlock(), createBlock(
            unref(ElDropdown),
            {
              key: 0,
              trigger: "click",
              class: "plus-table-action-bar__dropdown"
            },
            {
              dropdown: withCtx(() => [
                createVNode(
                  unref(ElDropdownMenu),
                  null,
                  {
                    default: withCtx(() => [
                      (openBlock(true), createElementBlock(
                        Fragment,
                        null,
                        renderList(getSubButtons(row, $index).nextButtons, (buttonRow) => {
                          return openBlock(), createBlock(
                            unref(ElDropdownItem),
                            {
                              key: unref(buttonRow.text)
                            },
                            {
                              default: withCtx(() => [
                                (openBlock(), createBlock(resolveDynamicComponent(() => render(row, buttonRow, $index, rest))))
                              ]),
                              _: 2
                              /* DYNAMIC */
                            },
                            1024
                            /* DYNAMIC_SLOTS */
                          );
                        }),
                        128
                        /* KEYED_FRAGMENT */
                      ))
                    ]),
                    _: 2
                    /* DYNAMIC */
                  },
                  1024
                  /* DYNAMIC_SLOTS */
                )
              ]),
              default: withCtx(() => [
                createBaseVNode("span", _hoisted_13, [
                  createBaseVNode(
                    "span",
                    _hoisted_22,
                    toDisplayString(unref(t)("plus.table.more")),
                    1
                    /* TEXT */
                  ),
                  renderSlot(_ctx.$slots, "action-bar-more-icon", {}, () => [
                    createVNode(unref(ElIcon), null, {
                      default: withCtx(() => [
                        createVNode(unref(arrow_down_bold_default))
                      ]),
                      _: 1
                      /* STABLE */
                    })
                  ])
                ])
              ]),
              _: 2
              /* DYNAMIC */
            },
            1024
            /* DYNAMIC_SLOTS */
          )) : createCommentVNode("v-if", true)
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["label", "fixed", "width"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-action-bar.vue.mjs
var PlusTableActionBar = _export_sfc(_sfc_main3, [["__file", "table-action-bar.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/render/src/index.vue2.mjs
var _sfc_main4 = defineComponent({
  ...{
    name: "PlusRender"
  },
  __name: "index",
  props: {
    renderType: { default: void 0 },
    callbackValue: { default: "" },
    customFieldProps: { default: () => ({}) },
    render: {},
    params: { default: () => ({}) },
    handleChange: {}
  },
  setup(__props) {
    const props = __props;
    const state = ref();
    watch(
      () => props.callbackValue,
      (val) => {
        state.value = val;
      },
      {
        flush: "post",
        immediate: true
      }
    );
    const renderComponent = () => {
      if (!props.render)
        return;
      const params = { ...props.params };
      const dynamicComponent = props.renderType === "form" ? props.render(
        state.value,
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        props.handleChange,
        params
      ) : props.render(state.value, params);
      if (isVNode(dynamicComponent)) {
        const payload = props.renderType === "form" ? {
          modelValue: state.value,
          ...props.customFieldProps,
          ...dynamicComponent.props
        } : {
          ...props.customFieldProps,
          ...dynamicComponent.props
        };
        return {
          ...dynamicComponent,
          props: payload
        };
      } else if (isString(dynamicComponent)) {
        return dynamicComponent;
      }
    };
    return (_ctx, _cache) => {
      return _ctx.renderType === "form" ? (openBlock(), createBlock(resolveDynamicComponent(renderComponent), mergeProps({
        key: 0,
        modelValue: state.value,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.value = $event)
      }, _ctx.customFieldProps), null, 16, ["modelValue"])) : (openBlock(), createBlock(
        resolveDynamicComponent(renderComponent),
        normalizeProps(mergeProps({ key: 1 }, _ctx.customFieldProps)),
        null,
        16
        /* FULL_PROPS */
      ));
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/render/src/index.vue.mjs
var Render = _export_sfc(_sfc_main4, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/render/index.mjs
var PlusRender = Render;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/radio/src/index.vue2.mjs
var _sfc_main5 = defineComponent({
  ...{
    name: "PlusRadio"
  },
  __name: "index",
  props: {
    modelValue: { type: [String, Number, Boolean], default: "" },
    options: { default: () => [] },
    isCancel: { type: Boolean, default: true },
    fieldSlots: { default: void 0 },
    fieldChildrenSlot: { default: void 0 }
  },
  emits: ["change", "update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const radioInstance = ref();
    const radioGroupInstance = ref();
    const state = reactive({ radio: "" });
    watch(
      () => props.modelValue,
      (val) => {
        state.radio = val;
      },
      { immediate: true }
    );
    const attrs = useAttrs();
    const radioClick = (e, val, fieldItemProps) => {
      if (Reflect.has(attrs, "disabled") || (fieldItemProps == null ? void 0 : fieldItemProps.disabled)) {
        return;
      }
      if (!props.isCancel) {
        return;
      } else {
        e.preventDefault();
      }
      state.radio = state.radio === val ? "" : val;
      emit("update:modelValue", state.radio);
      emit("change", state.radio);
    };
    const change = (val) => {
      if (props.isCancel)
        return;
      emit("update:modelValue", val);
      emit("change", val);
    };
    __expose({
      radioInstance,
      radioGroupInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElRadioGroup), mergeProps({
        ref_key: "radioGroupInstance",
        ref: radioGroupInstance,
        modelValue: state.radio,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.radio = $event),
        class: "plus-radio"
      }, _ctx.$attrs), createSlots({
        default: withCtx(() => [
          unref(versionIsLessThan260) ? (openBlock(true), createElementBlock(
            Fragment,
            { key: 0 },
            renderList(_ctx.options, (item) => {
              return openBlock(), createBlock(unref(ElRadio), mergeProps({
                key: `${item.label}${item.value}`,
                ref_for: true,
                ref_key: "radioInstance",
                ref: radioInstance,
                label: item.value
              }, item.fieldItemProps, {
                onClick: ($event) => radioClick($event, item.value, item.fieldItemProps),
                onChange: ($event) => change(item.value)
              }), {
                default: withCtx(() => [
                  unref(isFunction)(item.fieldSlot) ? (openBlock(), createBlock(resolveDynamicComponent(item.fieldSlot), mergeProps({
                    key: 0,
                    "model-value": state.radio,
                    column: props
                  }, item), null, 16, ["model-value"])) : unref(isFunction)(_ctx.fieldChildrenSlot) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.fieldChildrenSlot), mergeProps({
                    key: 1,
                    "model-value": state.radio,
                    column: props
                  }, item), null, 16, ["model-value"])) : (openBlock(), createElementBlock(
                    Fragment,
                    { key: 2 },
                    [
                      createTextVNode(
                        toDisplayString(item == null ? void 0 : item.label),
                        1
                        /* TEXT */
                      )
                    ],
                    64
                    /* STABLE_FRAGMENT */
                  ))
                ]),
                _: 2
                /* DYNAMIC */
              }, 1040, ["label", "onClick", "onChange"]);
            }),
            128
            /* KEYED_FRAGMENT */
          )) : (openBlock(), createElementBlock(
            Fragment,
            { key: 1 },
            [
              createCommentVNode(" element-plus 版本号大于等于2.6.0 "),
              (openBlock(true), createElementBlock(
                Fragment,
                null,
                renderList(_ctx.options, (item) => {
                  return openBlock(), createBlock(unref(ElRadio), mergeProps({
                    key: `${item.label}${item.value}`,
                    ref_for: true,
                    ref_key: "radioInstance",
                    ref: radioInstance,
                    value: item.value
                  }, item.fieldItemProps, {
                    onClick: ($event) => radioClick($event, item.value, item.fieldItemProps),
                    onChange: ($event) => change(item.value)
                  }), {
                    default: withCtx(() => [
                      unref(isFunction)(item.fieldSlot) ? (openBlock(), createBlock(resolveDynamicComponent(item.fieldSlot), mergeProps({
                        key: 0,
                        "model-value": state.radio,
                        column: props
                      }, item), null, 16, ["model-value"])) : unref(isFunction)(_ctx.fieldChildrenSlot) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.fieldChildrenSlot), mergeProps({
                        key: 1,
                        "model-value": state.radio,
                        column: props
                      }, item), null, 16, ["model-value"])) : (openBlock(), createElementBlock(
                        Fragment,
                        { key: 2 },
                        [
                          createTextVNode(
                            toDisplayString(item == null ? void 0 : item.label),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      ))
                    ]),
                    _: 2
                    /* DYNAMIC */
                  }, 1040, ["value", "onClick", "onChange"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ],
            64
            /* STABLE_FRAGMENT */
          ))
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        renderList(_ctx.fieldSlots, (fieldSlot, key) => {
          return {
            name: key,
            fn: withCtx((data) => [
              (openBlock(), createBlock(
                resolveDynamicComponent(fieldSlot),
                normalizeProps(guardReactiveProps(data)),
                null,
                16
                /* FULL_PROPS */
              ))
            ])
          };
        })
      ]), 1040, ["modelValue"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/radio/src/index.vue.mjs
var Radio = _export_sfc(_sfc_main5, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/radio/index.mjs
var PlusRadio = Radio;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/date-picker/src/index.vue2.mjs
var _hoisted_14 = { class: "plus-date-picker__middle" };
var _sfc_main6 = defineComponent({
  ...{
    name: "PlusDatePicker"
  },
  __name: "index",
  props: {
    modelValue: { default: () => [] },
    rangeSeparator: { default: "/" },
    valueFormat: { default: "YYYY-MM-DD HH:mm:ss" },
    type: { default: "datetime" },
    startProps: { default: () => ({}) },
    endProps: { default: () => ({}) },
    disabled: { type: Boolean, default: false },
    startDisabledDate: { type: Function, default: (startTime, endValue) => {
      if (!endValue)
        return false;
      return startTime.getTime() > new Date(endValue).getTime();
    } },
    endDisabledDate: { type: Function, default: (endTime, startValue) => {
      if (!startValue)
        return false;
      return endTime.getTime() < new Date(startValue).getTime();
    } }
  },
  emits: ["change", "focus", "update:modelValue"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const attrs = useAttrs();
    const computedStartProps = computed(() => ({ ...attrs, ...props.startProps }));
    const computedEndProps = computed(() => ({ ...attrs, ...props.endProps }));
    const startPickerInstance = ref();
    const endPickerInstance = ref();
    const state = reactive({
      start: "",
      end: ""
    });
    const formDisabled = useFormDisabled();
    const isFocus = ref(false);
    const handleFocus = (event) => {
      isFocus.value = true;
      emit("focus", event);
    };
    const onClickOutside = () => {
      isFocus.value = false;
    };
    const subStartDisabledDate = (time) => {
      if (props.startDisabledDate && isFunction(props.startDisabledDate)) {
        return props.startDisabledDate(time, state.end);
      }
      return false;
    };
    const subEndDisabledDate = (time) => {
      if (props.endDisabledDate && isFunction(props.endDisabledDate)) {
        return props.endDisabledDate(time, state.start);
      }
      return false;
    };
    watch(
      () => props.modelValue,
      (val) => {
        const [start, end] = val;
        state.start = start;
        state.end = end;
      },
      {
        immediate: true
      }
    );
    const handleChange = () => {
      const res = [state.start, state.end];
      emit("update:modelValue", res);
      emit("change", res);
    };
    __expose({
      startPickerInstance,
      endPickerInstance
    });
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createElementBlock(
        "div",
        {
          class: normalizeClass(["plus-date-picker", {
            "is-focus": isFocus.value,
            "is-disabled": unref(formDisabled)
          }])
        },
        [
          createVNode(unref(ElDatePicker), mergeProps({
            ref_key: "startPickerInstance",
            ref: startPickerInstance,
            modelValue: state.start,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.start = $event),
            type: _ctx.type,
            "value-format": _ctx.valueFormat,
            placeholder: unref(t)("plus.datepicker.startPlaceholder"),
            "disabled-date": subStartDisabledDate,
            class: "plus-date-picker__start",
            clearable: "",
            disabled: unref(formDisabled)
          }, computedStartProps.value, {
            onChange: handleChange,
            onFocus: handleFocus
          }), null, 16, ["modelValue", "type", "value-format", "placeholder", "disabled"]),
          createBaseVNode(
            "span",
            _hoisted_14,
            toDisplayString(_ctx.rangeSeparator),
            1
            /* TEXT */
          ),
          createVNode(unref(ElDatePicker), mergeProps({
            ref_key: "endPickerInstance",
            ref: endPickerInstance,
            modelValue: state.end,
            "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => state.end = $event),
            "value-format": _ctx.valueFormat,
            type: _ctx.type,
            placeholder: unref(t)("plus.datepicker.endPlaceholder"),
            "disabled-date": subEndDisabledDate,
            class: "plus-date-picker__end",
            clearable: "",
            disabled: unref(formDisabled)
          }, computedEndProps.value, {
            onChange: handleChange,
            onFocus: handleFocus
          }), null, 16, ["modelValue", "value-format", "type", "placeholder", "disabled"])
        ],
        2
        /* CLASS */
      )), [
        [unref(ClickOutside), onClickOutside]
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/date-picker/src/index.vue.mjs
var DatePicker = _export_sfc(_sfc_main6, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/date-picker/index.mjs
var PlusDatePicker = DatePicker;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/input-tag/src/index.vue2.mjs
var _sfc_main7 = defineComponent({
  ...{
    name: "PlusInputTag"
  },
  __name: "index",
  props: {
    modelValue: { default: () => [] },
    trigger: { default: () => ["blur", "enter", "space"] },
    inputProps: { default: () => ({}) },
    tagProps: { default: () => ({}) },
    limit: { default: Infinity },
    formatTag: { type: Function, default: void 0 },
    retainInputValue: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "change", "remove", "blur", "enter", "space"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const inputInstance = ref();
    const tagInstance = ref();
    const plusInputTagInstance = ref();
    const state = reactive({
      tags: [],
      inputValue: "",
      isFocus: false
    });
    const formDisabled = useFormDisabled();
    const { t } = useLocale();
    watch(
      () => props.modelValue,
      (val) => {
        state.tags = val.slice(0, props.limit);
      },
      { immediate: true }
    );
    const onClickOutside = () => {
      state.isFocus = false;
    };
    const handleClick = () => {
      var _a;
      state.isFocus = true;
      (_a = inputInstance.value) == null ? void 0 : _a.focus();
    };
    const handleClose = (tag) => {
      if (formDisabled.value)
        return;
      state.tags = state.tags.filter((item) => item !== tag);
      emit("remove", tag);
      emit("update:modelValue", state.tags);
      emit("change", state.tags);
    };
    const handleValue = () => {
      if (state.inputValue.trim() && !state.tags.includes(state.inputValue.trim()) && state.tags.length < props.limit) {
        state.tags.push(state.inputValue.trim());
      }
      if (!props.retainInputValue) {
        state.inputValue = "";
      }
      emit("update:modelValue", state.tags);
      emit("change", state.tags);
    };
    const handle = (event, type) => {
      emit(type, state.inputValue, event);
      const triggerList = isArray(props.trigger) ? props.trigger : isString(props.trigger) ? [props.trigger] : ["blur", "enter", "space"];
      if (triggerList.includes(type)) {
        handleValue();
      }
    };
    __expose({ inputInstance, tagInstance });
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createElementBlock(
        "div",
        {
          ref_key: "plusInputTagInstance",
          ref: plusInputTagInstance,
          class: normalizeClass(["plus-input-tag", {
            "is-focus": state.isFocus,
            "is-disabled": unref(formDisabled)
          }]),
          onClick: handleClick
        },
        [
          (openBlock(true), createElementBlock(
            Fragment,
            null,
            renderList(state.tags, (tag) => {
              return openBlock(), createBlock(unref(ElTag), mergeProps({
                ref_for: true,
                ref_key: "tagInstance",
                ref: tagInstance,
                key: tag,
                class: "plus-input-tag__tag"
              }, _ctx.tagProps, {
                closable: "",
                onClose: ($event) => handleClose(tag)
              }), {
                default: withCtx(() => [
                  createTextVNode(
                    toDisplayString(_ctx.formatTag && unref(isFunction)(_ctx.formatTag) ? _ctx.formatTag(tag) : tag),
                    1
                    /* TEXT */
                  )
                ]),
                _: 2
                /* DYNAMIC */
              }, 1040, ["onClose"]);
            }),
            128
            /* KEYED_FRAGMENT */
          )),
          state.tags.length < _ctx.limit ? (openBlock(), createBlock(unref(ElInput), mergeProps({
            key: 0,
            ref_key: "inputInstance",
            ref: inputInstance,
            modelValue: state.inputValue,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.inputValue = $event),
            class: "plus-input-tag__input",
            placeholder: state.tags.length ? "" : unref(t)("plus.inputTag.placeholder"),
            disabled: unref(formDisabled) || state.tags.length >= _ctx.limit
          }, _ctx.inputProps, {
            clearable: "",
            onBlur: _cache[1] || (_cache[1] = ($event) => handle($event, "blur")),
            onKeyup: [
              _cache[2] || (_cache[2] = withKeys(withModifiers(($event) => handle($event, "enter"), ["exact"]), ["enter"])),
              _cache[3] || (_cache[3] = withKeys(withModifiers(($event) => handle($event, "space"), ["exact"]), ["space"]))
            ]
          }), null, 16, ["modelValue", "placeholder", "disabled"])) : createCommentVNode("v-if", true)
        ],
        2
        /* CLASS */
      )), [
        [unref(ClickOutside), onClickOutside]
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/input-tag/src/index.vue.mjs
var InputTag = _export_sfc(_sfc_main7, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/input-tag/index.mjs
var PlusInputTag = InputTag;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form-item/src/form-item.mjs
var FieldComponentMap = {
  // plus
  "plus-radio": {
    component: PlusRadio,
    hasOptions: true
  },
  "plus-date-picker": {
    component: PlusDatePicker
  },
  "plus-input-tag": {
    component: PlusInputTag
  },
  // el
  autocomplete: {
    component: ElAutocomplete,
    props: { placeholder: "plus.field.pleaseEnter" },
    hasSelectEvent: true
  },
  cascader: {
    component: ElCascader,
    hasOptions: true
  },
  checkbox: {
    component: ElCheckboxGroup,
    children: ElCheckbox,
    hasVersionCompatibility: true
  },
  "color-picker": {
    component: ElColorPicker
  },
  "date-picker": {
    component: ElDatePicker,
    props: {
      startPlaceholder: "plus.datepicker.startPlaceholder",
      endPlaceholder: "plus.datepicker.endPlaceholder"
    }
  },
  "input-number": {
    component: ElInputNumber,
    props: { placeholder: "plus.field.pleaseEnter" }
  },
  radio: {
    component: ElRadioGroup,
    children: ElRadio,
    hasVersionCompatibility: true
  },
  rate: {
    component: ElRate
  },
  select: {
    component: ElSelect,
    children: ElOption
  },
  slider: {
    component: ElSlider
  },
  switch: {
    component: ElSwitch
  },
  "time-picker": {
    component: ElTimePicker
  },
  "time-select": {
    component: ElTimeSelect
  },
  transfer: {
    component: ElTransfer
  },
  input: {
    component: ElInput,
    props: { placeholder: "plus.field.pleaseEnter" }
  },
  textarea: {
    component: ElInput,
    props: { type: "textarea", placeholder: "plus.field.pleaseEnter" }
  },
  "tree-select": {
    component: ElTreeSelect
  }
};
var hasFieldComponent = (valueType) => Object.keys(FieldComponentMap).includes(
  valueType
);
var getFieldComponent = (valueType) => Reflect.get(FieldComponentMap, valueType) || {};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form-item/src/index.vue2.mjs
var _hoisted_15 = { class: "plus-form-item__label" };
var _sfc_main8 = defineComponent({
  ...{
    name: "PlusFormItem"
  },
  __name: "index",
  props: {
    modelValue: { default: "" },
    hasLabel: { default: true },
    label: { default: "" },
    prop: {},
    fieldProps: { default: () => ({}) },
    valueType: { default: void 0 },
    options: { default: () => [] },
    formItemProps: { default: () => ({}) },
    renderField: { default: void 0 },
    renderLabel: { default: void 0 },
    tooltip: { default: "" },
    fieldSlots: { default: () => ({}) },
    fieldChildrenSlot: { default: void 0 },
    optionsMap: { default: void 0 },
    index: { default: 0 }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const ElFormItem$1 = ElFormItem;
    const ElTooltip$1 = ElTooltip;
    const ElIcon$1 = ElIcon;
    const ElInput$1 = ElInput;
    const ElSelect$1 = ElSelect;
    const ElOption$1 = ElOption;
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const { customOptions, customOptionsIsReady } = useGetOptions(props);
    const formItemInstance = ref();
    const fieldInstance = ref();
    const customFormItemProps = ref({});
    const customFieldProps = ref({});
    const state = ref();
    const customFieldPropsIsReady = ref(false);
    const valueIsReady = ref(false);
    const labelValue = computed(() => getLabel(props.label));
    const params = computed(() => ({ ...props, label: labelValue.value }));
    const formFieldRefs = inject(TableFormFieldRefInjectionKey, {});
    const isArrayValue = computed(() => {
      var _a, _b, _c, _d, _e, _f;
      if (props.valueType === "cascader" && ((_b = (_a = customFieldProps.value) == null ? void 0 : _a.props) == null ? void 0 : _b.emitPath) === false) {
        return false;
      }
      if (ValueIsArrayList.includes(props.valueType)) {
        return true;
      }
      if (props.valueType === "select" && ((_c = customFieldProps.value) == null ? void 0 : _c.multiple) === true) {
        return true;
      }
      if (props.valueType === "date-picker" && DatePickerValueIsArrayList.includes((_d = customFieldProps.value) == null ? void 0 : _d.type)) {
        return true;
      }
      if (props.valueType === "time-picker" && ((_e = customFieldProps.value) == null ? void 0 : _e.isRange) === true) {
        return true;
      }
      if (props.valueType === "tree-select" && ((_f = customFieldProps.value) == null ? void 0 : _f.multiple) === true) {
        return true;
      }
      return false;
    });
    const isNumberValue = computed(() => {
      if (ValueIsNumberList.includes(props.valueType)) {
        return true;
      }
      return false;
    });
    const setValue2 = (val) => {
      if (isArrayValue.value) {
        if (isArray(val)) {
          const [start, end] = val;
          if (isDate(start) || isDate(end)) {
            state.value = [String(start), String(end)];
          } else {
            state.value = val;
          }
        } else {
          state.value = [];
        }
      } else if (isNumberValue.value) {
        state.value = val === null || val === void 0 || val === "" ? null : typeof val === "string" ? Number(val) : val;
      } else if (isDate(val)) {
        state.value = String(val);
      } else {
        state.value = val;
      }
      valueIsReady.value = true;
    };
    const commonProps = computed(() => {
      const { hasOptions, hasSelectEvent, props: componentProps } = getFieldComponent(props.valueType);
      return {
        ...hasOptions ? {
          options: customOptions.value
        } : null,
        ...hasSelectEvent ? {
          onSelect: handleSelect
        } : null,
        ...componentProps,
        placeholder: (componentProps == null ? void 0 : componentProps.placeholder) ? t(componentProps == null ? void 0 : componentProps.placeholder) + labelValue.value : t("plus.field.pleaseSelect") + labelValue.value,
        ...props.valueType === "date-picker" ? {
          startPlaceholder: (componentProps == null ? void 0 : componentProps.startPlaceholder) ? t(componentProps == null ? void 0 : componentProps.startPlaceholder) : "",
          endPlaceholder: (componentProps == null ? void 0 : componentProps.startPlaceholder) ? t(componentProps == null ? void 0 : componentProps.endPlaceholder) : ""
        } : null,
        ...customFieldProps.value
      };
    });
    const getChildrenProps = (item) => {
      return {
        ...props.valueType === "select" ? {
          label: item.label,
          value: item.value
        } : versionIsLessThan260 ? {
          label: item.value
        } : {
          label: item.label,
          value: item.value
        },
        ...item.fieldItemProps
      };
    };
    watch(
      () => props.formItemProps,
      (val) => {
        getCustomProps(val, state.value, props, props.index, "formItemProps").then((data) => {
          customFormItemProps.value = data;
        }).catch((err) => {
          throw err;
        });
      },
      {
        immediate: true,
        deep: true
      }
    );
    watch(
      () => props.fieldProps,
      (val) => {
        getCustomProps(val, state.value, props, props.index, "fieldProps").then((data) => {
          customFieldProps.value = data;
          customFieldPropsIsReady.value = true;
        }).catch((err) => {
          throw err;
        });
      },
      {
        immediate: true,
        deep: true
      }
    );
    watch(
      computed(() => [props.modelValue, customFieldPropsIsReady.value, customOptionsIsReady.value]),
      ([val, fieldPropsIsReady, optionsIsReady]) => {
        if (fieldPropsIsReady && optionsIsReady) {
          setValue2(val);
        }
      },
      {
        immediate: true,
        flush: "post"
      }
    );
    const handleChange = (val) => {
      emit("update:modelValue", val);
      emit("change", val);
    };
    const handleSelect = ({ value }) => {
      handleChange(value);
    };
    watch(fieldInstance, () => {
      formFieldRefs.value = {
        fieldInstance: fieldInstance.value,
        valueIsReady
      };
    });
    __expose({
      formItemInstance,
      fieldInstance
    });
    return (_ctx, _cache) => {
      var _a;
      return valueIsReady.value ? (openBlock(), createBlock(unref(ElFormItem$1), mergeProps({
        key: 0,
        ref_key: "formItemInstance",
        ref: formItemInstance,
        label: _ctx.hasLabel ? labelValue.value : "",
        prop: _ctx.prop,
        class: "plus-form-item"
      }, customFormItemProps.value, {
        "label-width": _ctx.hasLabel ? (_a = customFormItemProps.value) == null ? void 0 : _a.labelWidth : "0px"
      }), createSlots({
        default: withCtx(() => [
          _ctx.renderField && unref(isFunction)(_ctx.renderField) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 0 },
            [
              valueIsReady.value ? (openBlock(), createBlock(unref(PlusRender), {
                key: 0,
                render: _ctx.renderField,
                params: params.value,
                "callback-value": state.value,
                "custom-field-props": customFieldProps.value,
                "render-type": "form",
                "handle-change": handleChange
              }, null, 8, ["render", "params", "callback-value", "custom-field-props"])) : createCommentVNode("v-if", true)
            ],
            64
            /* STABLE_FRAGMENT */
          )) : _ctx.$slots[unref(getFieldSlotName)(_ctx.prop)] ? renderSlot(_ctx.$slots, unref(getFieldSlotName)(_ctx.prop), {
            key: 1,
            prop: _ctx.prop,
            label: labelValue.value,
            fieldProps: customFieldProps.value,
            valueType: _ctx.valueType,
            column: props
          }) : _ctx.valueType === "select" && customFieldProps.value.multiple === true ? (openBlock(), createBlock(unref(ElSelect$1), mergeProps({
            key: 2,
            ref_key: "fieldInstance",
            ref: fieldInstance,
            modelValue: state.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.value = $event),
            placeholder: unref(t)("plus.field.pleaseSelect") + labelValue.value,
            class: "plus-form-item-field",
            clearable: ""
          }, customFieldProps.value, { "onUpdate:modelValue": handleChange }), createSlots({
            default: withCtx(() => [
              (openBlock(true), createElementBlock(
                Fragment,
                null,
                renderList(unref(customOptions), (item) => {
                  return openBlock(), createBlock(unref(ElOption$1), mergeProps({
                    key: item.label,
                    label: item.label,
                    value: item.value
                  }, item.fieldItemProps), {
                    default: withCtx(() => [
                      unref(isFunction)(item.fieldSlot) ? (openBlock(), createBlock(
                        resolveDynamicComponent(item.fieldSlot),
                        normalizeProps(mergeProps({ key: 0 }, item)),
                        null,
                        16
                        /* FULL_PROPS */
                      )) : unref(isFunction)(_ctx.fieldChildrenSlot) ? (openBlock(), createBlock(
                        resolveDynamicComponent(_ctx.fieldChildrenSlot),
                        normalizeProps(mergeProps({ key: 1 }, item)),
                        null,
                        16
                        /* FULL_PROPS */
                      )) : (openBlock(), createElementBlock(
                        Fragment,
                        { key: 2 },
                        [
                          createTextVNode(
                            toDisplayString(item.label),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      ))
                    ]),
                    _: 2
                    /* DYNAMIC */
                  }, 1040, ["label", "value"]);
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ]),
            _: 2
            /* DYNAMIC */
          }, [
            renderList(_ctx.fieldSlots, (fieldSlot, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  (openBlock(), createBlock(
                    resolveDynamicComponent(fieldSlot),
                    normalizeProps(guardReactiveProps(data)),
                    null,
                    16
                    /* FULL_PROPS */
                  ))
                ])
              };
            })
          ]), 1040, ["modelValue", "placeholder"])) : unref(hasFieldComponent)(_ctx.valueType) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 3 },
            [
              createCommentVNode(" 统一处理 "),
              createCommentVNode(" has-children  "),
              unref(getFieldComponent)(_ctx.valueType).children ? (openBlock(), createBlock(resolveDynamicComponent(unref(getFieldComponent)(_ctx.valueType).component), mergeProps({
                key: 0,
                ref_key: "fieldInstance",
                ref: fieldInstance,
                modelValue: state.value,
                "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => state.value = $event),
                class: "plus-form-item-field",
                clearable: ""
              }, commonProps.value, { "onUpdate:modelValue": handleChange }), createSlots({
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(
                    Fragment,
                    null,
                    renderList(unref(customOptions), (item) => {
                      return openBlock(), createBlock(
                        resolveDynamicComponent(unref(getFieldComponent)(_ctx.valueType).children),
                        mergeProps({
                          key: item.label
                        }, getChildrenProps(item)),
                        {
                          default: withCtx(() => [
                            unref(isFunction)(item.fieldSlot) ? (openBlock(), createBlock(resolveDynamicComponent(item.fieldSlot), mergeProps({
                              key: 0,
                              "model-value": state.value,
                              column: params.value
                            }, item), null, 16, ["model-value", "column"])) : unref(isFunction)(_ctx.fieldChildrenSlot) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.fieldChildrenSlot), mergeProps({
                              key: 1,
                              "model-value": state.value,
                              column: params.value
                            }, item), null, 16, ["model-value", "column"])) : (openBlock(), createElementBlock(
                              Fragment,
                              { key: 2 },
                              [
                                createTextVNode(
                                  toDisplayString(item.label),
                                  1
                                  /* TEXT */
                                )
                              ],
                              64
                              /* STABLE_FRAGMENT */
                            ))
                          ]),
                          _: 2
                          /* DYNAMIC */
                        },
                        1040
                        /* FULL_PROPS, DYNAMIC_SLOTS */
                      );
                    }),
                    128
                    /* KEYED_FRAGMENT */
                  ))
                ]),
                _: 2
                /* DYNAMIC */
              }, [
                renderList(_ctx.fieldSlots, (fieldSlot, key) => {
                  return {
                    name: key,
                    fn: withCtx((data) => [
                      (openBlock(), createBlock(resolveDynamicComponent(fieldSlot), mergeProps({
                        value: state.value,
                        column: params.value
                      }, data), null, 16, ["value", "column"]))
                    ])
                  };
                })
              ]), 1040, ["modelValue"])) : (openBlock(), createElementBlock(
                Fragment,
                { key: 1 },
                [
                  createCommentVNode(" no-children  "),
                  (openBlock(), createBlock(resolveDynamicComponent(unref(getFieldComponent)(_ctx.valueType).component), mergeProps({
                    ref_key: "fieldInstance",
                    ref: fieldInstance,
                    modelValue: state.value,
                    "onUpdate:modelValue": _cache[2] || (_cache[2] = ($event) => state.value = $event),
                    class: "plus-form-item-field",
                    clearable: "",
                    "field-children-slot": _ctx.fieldChildrenSlot
                  }, commonProps.value, { "onUpdate:modelValue": handleChange }), createSlots({
                    _: 2
                    /* DYNAMIC */
                  }, [
                    renderList(_ctx.fieldSlots, (fieldSlot, key) => {
                      return {
                        name: key,
                        fn: withCtx((data) => [
                          (openBlock(), createBlock(resolveDynamicComponent(fieldSlot), mergeProps({
                            "model-value": state.value,
                            column: params.value
                          }, data), null, 16, ["model-value", "column"]))
                        ])
                      };
                    })
                  ]), 1040, ["modelValue", "field-children-slot"]))
                ],
                2112
                /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
              ))
            ],
            64
            /* STABLE_FRAGMENT */
          )) : _ctx.valueType === "text" ? (openBlock(), createBlock(
            unref(ElText),
            mergeProps({
              key: 4,
              ref_key: "fieldInstance",
              ref: fieldInstance,
              class: "plus-form-item-field"
            }, customFieldProps.value),
            {
              default: withCtx(() => [
                createTextVNode(
                  toDisplayString(state.value),
                  1
                  /* TEXT */
                )
              ]),
              _: 1
              /* STABLE */
            },
            16
            /* FULL_PROPS */
          )) : _ctx.valueType === "divider" ? (openBlock(), createBlock(
            unref(ElDivider),
            mergeProps({
              key: 5,
              ref_key: "fieldInstance",
              ref: fieldInstance,
              class: "plus-form-item-field"
            }, customFieldProps.value),
            {
              default: withCtx(() => [
                createTextVNode(
                  toDisplayString(state.value),
                  1
                  /* TEXT */
                )
              ]),
              _: 1
              /* STABLE */
            },
            16
            /* FULL_PROPS */
          )) : (openBlock(), createBlock(unref(ElInput$1), mergeProps({
            key: 6,
            ref_key: "fieldInstance",
            ref: fieldInstance,
            modelValue: state.value,
            "onUpdate:modelValue": _cache[3] || (_cache[3] = ($event) => state.value = $event),
            class: "plus-form-item-field",
            placeholder: unref(t)("plus.field.pleaseEnter") + labelValue.value,
            autocomplete: "off",
            clearable: ""
          }, customFieldProps.value, { "onUpdate:modelValue": handleChange }), createSlots({
            _: 2
            /* DYNAMIC */
          }, [
            renderList(_ctx.fieldSlots, (fieldSlot, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  (openBlock(), createBlock(resolveDynamicComponent(fieldSlot), mergeProps({
                    "model-value": state.value,
                    column: params.value
                  }, data), null, 16, ["model-value", "column"]))
                ])
              };
            })
          ]), 1040, ["modelValue", "placeholder"]))
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        _ctx.hasLabel ? {
          name: "label",
          fn: withCtx(({ label: currentLabel }) => [
            createBaseVNode("span", _hoisted_15, [
              _ctx.renderLabel && unref(isFunction)(_ctx.renderLabel) ? (openBlock(), createElementBlock(
                Fragment,
                { key: 0 },
                [
                  valueIsReady.value ? (openBlock(), createBlock(unref(PlusRender), {
                    key: 0,
                    render: _ctx.renderLabel,
                    params: params.value,
                    "callback-value": currentLabel,
                    "custom-field-props": customFieldProps.value
                  }, null, 8, ["render", "params", "callback-value", "custom-field-props"])) : createCommentVNode("v-if", true)
                ],
                64
                /* STABLE_FRAGMENT */
              )) : renderSlot(_ctx.$slots, unref(getLabelSlotName)(_ctx.prop), {
                key: 1,
                prop: _ctx.prop,
                label: labelValue.value,
                fieldProps: customFieldProps.value,
                valueType: _ctx.valueType,
                column: params.value
              }, () => [
                createTextVNode(
                  toDisplayString(currentLabel),
                  1
                  /* TEXT */
                )
              ]),
              _ctx.tooltip ? (openBlock(), createBlock(
                unref(ElTooltip$1),
                mergeProps({
                  key: 2,
                  placement: "top"
                }, unref(getTooltip)(_ctx.tooltip)),
                {
                  default: withCtx(() => [
                    renderSlot(_ctx.$slots, "tooltip-icon", {}, () => [
                      createVNode(unref(ElIcon$1), {
                        class: "plus-table-column__label__icon",
                        size: 16
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(question_filled_default))
                        ]),
                        _: 1
                        /* STABLE */
                      })
                    ])
                  ]),
                  _: 3
                  /* FORWARDED */
                },
                16
                /* FULL_PROPS */
              )) : createCommentVNode("v-if", true)
            ])
          ]),
          key: "0"
        } : void 0
      ]), 1040, ["label", "prop", "label-width"])) : createCommentVNode("v-if", true);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form-item/src/index.vue.mjs
var FormItem = _export_sfc(_sfc_main8, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form-item/index.mjs
var PlusFormItem = FormItem;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/collapse-transition.vue2.mjs
var _sfc_main9 = defineComponent({
  ...{
    name: "PlusCollapseTransition"
  },
  __name: "collapse-transition",
  props: {
    collapseDuration: { default: 300 },
    collapseTransition: { type: Boolean, default: true }
  },
  setup(__props) {
    const props = __props;
    const on = {
      beforeEnter(el) {
        el.style.opacity = 0;
      },
      enter(el, done) {
        requestAnimationFrame(() => {
          el.style.transition = `opacity ${props.collapseDuration}ms linear`;
          el.style.opacity = 1;
          done();
        });
      },
      leave(el, done) {
        el.style.opacity = 0;
        setTimeout(() => {
          done();
        }, props.collapseDuration / 3 * 2);
      }
    };
    return (_ctx, _cache) => {
      return _ctx.collapseTransition ? (openBlock(), createBlock(
        TransitionGroup,
        mergeProps({
          key: 0,
          name: "plus-collapse-transition",
          css: false
        }, toHandlers(on)),
        {
          default: withCtx(() => [
            renderSlot(_ctx.$slots, "default")
          ]),
          _: 3
          /* FORWARDED */
        },
        16
        /* FULL_PROPS */
      )) : renderSlot(_ctx.$slots, "default", { key: 1 });
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/collapse-transition.vue.mjs
var PlusCollapseTransition = _export_sfc(_sfc_main9, [["__file", "collapse-transition.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/form-content.vue2.mjs
var _hoisted_16 = {
  key: 0,
  class: "plus-form-item-extra"
};
var _sfc_main10 = defineComponent({
  ...{
    name: "PlusFormContent"
  },
  __name: "form-content",
  props: {
    modelValue: { default: () => ({}) },
    hasLabel: { type: Boolean, default: true },
    columns: { default: () => [] },
    rowProps: { default: () => ({}) },
    colProps: { default: () => ({}) },
    collapseDuration: { default: void 0 },
    collapseTransition: { type: Boolean, default: void 0 }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const values = ref({});
    const getHasLabel = (hasLabel) => {
      const has = unref(hasLabel);
      if (isBoolean(has)) {
        return has;
      }
      return props.hasLabel;
    };
    watch(
      () => props.modelValue,
      (val) => {
        values.value = val;
      },
      {
        immediate: true
      }
    );
    const getModelValue = (prop) => getValue(values.value, prop);
    const handleChange = (value, column) => {
      setValue(values.value, column.prop, value);
      emit("update:modelValue", values.value);
      emit("change", values.value, column);
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(
        unref(ElRow),
        mergeProps(_ctx.rowProps, { class: "plus-form__row" }),
        {
          default: withCtx(() => [
            createVNode(PlusCollapseTransition, {
              "collapse-duration": _ctx.collapseDuration,
              "collapse-transition": _ctx.collapseTransition
            }, {
              default: withCtx(() => [
                (openBlock(true), createElementBlock(
                  Fragment,
                  null,
                  renderList(_ctx.columns, (item) => {
                    return openBlock(), createBlock(
                      unref(ElCol),
                      mergeProps({
                        key: item.prop
                      }, item.colProps || _ctx.colProps),
                      {
                        default: withCtx(() => [
                          createVNode(unref(PlusFormItem), mergeProps({
                            "model-value": getModelValue(item.prop)
                          }, item, {
                            "has-label": getHasLabel(item.hasLabel),
                            onChange: (value) => handleChange(value, item)
                          }), createSlots({
                            _: 2
                            /* DYNAMIC */
                          }, [
                            _ctx.$slots[unref(getLabelSlotName)(item.prop)] ? {
                              name: unref(getLabelSlotName)(item.prop),
                              fn: withCtx((data) => [
                                renderSlot(_ctx.$slots, unref(getLabelSlotName)(item.prop), normalizeProps(guardReactiveProps(data)))
                              ]),
                              key: "0"
                            } : void 0,
                            _ctx.$slots[unref(getFieldSlotName)(item.prop)] ? {
                              name: unref(getFieldSlotName)(item.prop),
                              fn: withCtx((data) => [
                                renderSlot(_ctx.$slots, unref(getFieldSlotName)(item.prop), normalizeProps(guardReactiveProps(data)))
                              ]),
                              key: "1"
                            } : void 0,
                            _ctx.$slots["tooltip-icon"] ? {
                              name: "tooltip-icon",
                              fn: withCtx(() => [
                                renderSlot(_ctx.$slots, "tooltip-icon")
                              ]),
                              key: "2"
                            } : void 0
                          ]), 1040, ["model-value", "has-label", "onChange"]),
                          createCommentVNode(" el-form-item 下一行额外的内容 "),
                          item.renderExtra || _ctx.$slots[unref(getExtraSlotName)(item.prop)] ? (openBlock(), createElementBlock("div", _hoisted_16, [
                            item.renderExtra && unref(isFunction)(item.renderExtra) ? (openBlock(), createBlock(
                              resolveDynamicComponent(item.renderExtra),
                              normalizeProps(mergeProps({ key: 0 }, item)),
                              null,
                              16
                              /* FULL_PROPS */
                            )) : _ctx.$slots[unref(getExtraSlotName)(item.prop)] ? renderSlot(_ctx.$slots, unref(getExtraSlotName)(item.prop), normalizeProps(mergeProps({ key: 1 }, item))) : createCommentVNode("v-if", true)
                          ])) : createCommentVNode("v-if", true)
                        ]),
                        _: 2
                        /* DYNAMIC */
                      },
                      1040
                      /* FULL_PROPS, DYNAMIC_SLOTS */
                    );
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ]),
              _: 3
              /* FORWARDED */
            }, 8, ["collapse-duration", "collapse-transition"]),
            createCommentVNode(" 搜索的footer插槽  "),
            renderSlot(_ctx.$slots, "search-footer")
          ]),
          _: 3
          /* FORWARDED */
        },
        16
        /* FULL_PROPS */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/form-content.vue.mjs
var PlusFormContent = _export_sfc(_sfc_main10, [["__file", "form-content.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/index.vue2.mjs
var _hoisted_17 = { class: "plus-form__group__item__icon" };
var _sfc_main11 = defineComponent({
  ...{
    name: "PlusForm",
    inheritAttrs: false
  },
  __name: "index",
  props: {
    modelValue: { default: () => ({}) },
    defaultValues: { default: () => ({}) },
    columns: { default: () => [] },
    labelWidth: { default: "80px" },
    labelPosition: { default: "left" },
    rowProps: { default: () => ({}) },
    colProps: { default: () => ({}) },
    labelSuffix: { default: ":" },
    hasErrorTip: { type: Boolean, default: true },
    hasFooter: { type: Boolean, default: true },
    hasReset: { type: Boolean, default: true },
    hasLabel: { type: Boolean, default: true },
    submitText: { default: "" },
    resetText: { default: "" },
    submitLoading: { type: Boolean, default: false },
    footerAlign: { default: "left" },
    rules: { default: () => ({}) },
    group: { type: [Boolean, Array], default: false },
    cardProps: { default: () => ({}) },
    prevent: { type: Boolean, default: false },
    collapseDuration: { default: void 0 },
    collapseTransition: { type: Boolean, default: void 0 }
  },
  emits: ["update:modelValue", "submit", "change", "reset", "submitError", "validate"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const formInstance = ref(null);
    const values = ref({});
    const filterHide = (columns) => {
      return columns.filter((item) => unref(item.hideInForm) !== true);
    };
    const model = computed(() => values.value);
    const style = computed(() => ({
      justifyContent: props.footerAlign === "left" ? "flex-start" : props.footerAlign === "center" ? "center" : "flex-end"
    }));
    const subColumns = computed(() => filterHide(props.columns));
    const subGroup = computed(
      () => {
        var _a;
        return isArray(props.group) ? (_a = props.group) == null ? void 0 : _a.filter((item) => unref(item.hideInGroup) !== true) : props.group;
      }
    );
    const originAttrs = useAttrs();
    const attrs = computed(() => ({
      ...originAttrs,
      ...props.prevent ? {
        onSubmit: withModifiers(
          (...arg) => {
            if ((originAttrs == null ? void 0 : originAttrs.onSubmit) && isFunction(originAttrs == null ? void 0 : originAttrs.onSubmit)) {
              ;
              originAttrs.onSubmit(...arg);
            }
          },
          ["prevent"]
        )
      } : {}
    }));
    const slots = useSlots();
    const labelSlots = filterSlots(slots, getLabelSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const extraSlots = filterSlots(slots, getExtraSlotName());
    watch(
      () => props.modelValue,
      (val) => {
        values.value = val;
      },
      {
        immediate: true
      }
    );
    const handleChange = (_, column) => {
      emit("update:modelValue", values.value);
      emit("change", values.value, column);
    };
    const clearValidate = () => {
      var _a;
      (_a = formInstance.value) == null ? void 0 : _a.clearValidate();
    };
    const handleSubmit = async () => {
      var _a, _b, _c;
      try {
        const valid = await ((_a = formInstance.value) == null ? void 0 : _a.validate());
        if (valid) {
          emit("submit", values.value);
          return true;
        }
      } catch (errors) {
        if (props.hasErrorTip) {
          ElMessage.closeAll();
          const values2 = isPlainObject(errors) && Object.values(errors);
          const message = values2 ? (_c = (_b = values2[0]) == null ? void 0 : _b[0]) == null ? void 0 : _c.message : void 0;
          ElMessage.warning(message || t("plus.form.errorTip"));
        }
        emit("submitError", errors);
      }
      return false;
    };
    const handleReset = () => {
      clearValidate();
      values.value = { ...props.defaultValues };
      emit("update:modelValue", values.value);
      emit("reset", values.value);
    };
    const handleValidate = (...args) => {
      emit("validate", ...args);
    };
    __expose({
      formInstance,
      handleSubmit,
      handleReset
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElForm), mergeProps({
        ref_key: "formInstance",
        ref: formInstance,
        rules: _ctx.rules,
        "label-width": _ctx.hasLabel ? _ctx.labelWidth : 0,
        class: ["plus-form", _ctx.hasLabel ? "" : "no-has-label"],
        "label-position": _ctx.labelPosition,
        "validate-on-rule-change": false,
        "label-suffix": _ctx.hasLabel ? _ctx.labelSuffix : ""
      }, attrs.value, {
        model: model.value,
        onValidate: handleValidate
      }), {
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default", {}, () => [
            createCommentVNode(" 分组表单 "),
            subGroup.value ? (openBlock(true), createElementBlock(
              Fragment,
              { key: 0 },
              renderList(subGroup.value, (groupItem) => {
                return openBlock(), createBlock(
                  unref(ElCard),
                  mergeProps({
                    key: groupItem.title
                  }, groupItem.cardProps || _ctx.cardProps, { class: "plus-form__group__item" }),
                  {
                    header: withCtx(() => [
                      renderSlot(_ctx.$slots, "group-header", {
                        title: groupItem.title,
                        columns: groupItem.columns,
                        icon: groupItem.icon
                      }, () => [
                        createBaseVNode("div", _hoisted_17, [
                          groupItem.icon ? (openBlock(), createBlock(
                            unref(ElIcon),
                            { key: 0 },
                            {
                              default: withCtx(() => [
                                (openBlock(), createBlock(resolveDynamicComponent(groupItem.icon)))
                              ]),
                              _: 2
                              /* DYNAMIC */
                            },
                            1024
                            /* DYNAMIC_SLOTS */
                          )) : createCommentVNode("v-if", true),
                          createTextVNode(
                            " " + toDisplayString(groupItem.title),
                            1
                            /* TEXT */
                          )
                        ])
                      ])
                    ]),
                    default: withCtx(() => [
                      createVNode(PlusFormContent, {
                        modelValue: values.value,
                        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => values.value = $event),
                        "row-props": _ctx.rowProps,
                        "col-props": _ctx.colProps,
                        columns: filterHide(groupItem.columns),
                        "has-label": _ctx.hasLabel,
                        "collapse-transition": _ctx.collapseTransition,
                        "collapse-duration": _ctx.collapseDuration,
                        onChange: handleChange
                      }, createSlots({
                        _: 2
                        /* DYNAMIC */
                      }, [
                        renderList(unref(labelSlots), (_, key) => {
                          return {
                            name: key,
                            fn: withCtx((data) => [
                              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                            ])
                          };
                        }),
                        renderList(unref(fieldSlots), (_, key) => {
                          return {
                            name: key,
                            fn: withCtx((data) => [
                              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                            ])
                          };
                        }),
                        renderList(unref(extraSlots), (_, key) => {
                          return {
                            name: key,
                            fn: withCtx((data) => [
                              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                            ])
                          };
                        }),
                        _ctx.$slots["tooltip-icon"] ? {
                          name: "tooltip-icon",
                          fn: withCtx(() => [
                            renderSlot(_ctx.$slots, "tooltip-icon")
                          ]),
                          key: "0"
                        } : void 0
                      ]), 1032, ["modelValue", "row-props", "col-props", "columns", "has-label", "collapse-transition", "collapse-duration"])
                    ]),
                    _: 2
                    /* DYNAMIC */
                  },
                  1040
                  /* FULL_PROPS, DYNAMIC_SLOTS */
                );
              }),
              128
              /* KEYED_FRAGMENT */
            )) : (openBlock(), createElementBlock(
              Fragment,
              { key: 1 },
              [
                createCommentVNode(" 普通表单 "),
                createVNode(PlusFormContent, {
                  modelValue: values.value,
                  "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => values.value = $event),
                  "row-props": _ctx.rowProps,
                  "col-props": _ctx.colProps,
                  columns: subColumns.value,
                  "has-label": _ctx.hasLabel,
                  "collapse-transition": _ctx.collapseTransition,
                  "collapse-duration": _ctx.collapseDuration,
                  onChange: handleChange
                }, createSlots({
                  _: 2
                  /* DYNAMIC */
                }, [
                  renderList(unref(labelSlots), (_, key) => {
                    return {
                      name: key,
                      fn: withCtx((data) => [
                        renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                      ])
                    };
                  }),
                  renderList(unref(fieldSlots), (_, key) => {
                    return {
                      name: key,
                      fn: withCtx((data) => [
                        renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                      ])
                    };
                  }),
                  renderList(unref(extraSlots), (_, key) => {
                    return {
                      name: key,
                      fn: withCtx((data) => [
                        renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                      ])
                    };
                  }),
                  _ctx.$slots["search-footer"] ? {
                    name: "search-footer",
                    fn: withCtx(() => [
                      renderSlot(_ctx.$slots, "search-footer")
                    ]),
                    key: "0"
                  } : void 0,
                  _ctx.$slots["tooltip-icon"] ? {
                    name: "tooltip-icon",
                    fn: withCtx(() => [
                      renderSlot(_ctx.$slots, "tooltip-icon")
                    ]),
                    key: "1"
                  } : void 0
                ]), 1032, ["modelValue", "row-props", "col-props", "columns", "has-label", "collapse-transition", "collapse-duration"])
              ],
              64
              /* STABLE_FRAGMENT */
            ))
          ]),
          _ctx.hasFooter ? (openBlock(), createElementBlock(
            "div",
            {
              key: 0,
              class: "plus-form__footer",
              style: normalizeStyle(style.value)
            },
            [
              renderSlot(_ctx.$slots, "footer", normalizeProps(guardReactiveProps({ handleReset, handleSubmit })), () => [
                _ctx.hasReset ? (openBlock(), createBlock(unref(ElButton), {
                  key: 0,
                  onClick: handleReset
                }, {
                  default: withCtx(() => [
                    createCommentVNode(" 重置 "),
                    createTextVNode(
                      " " + toDisplayString(_ctx.resetText || unref(t)("plus.form.resetText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                })) : createCommentVNode("v-if", true),
                createVNode(unref(ElButton), {
                  type: "primary",
                  loading: _ctx.submitLoading,
                  onClick: handleSubmit
                }, {
                  default: withCtx(() => [
                    createCommentVNode(" 提交 "),
                    createTextVNode(
                      " " + toDisplayString(_ctx.submitText || unref(t)("plus.form.submitText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                }, 8, ["loading"])
              ])
            ],
            4
            /* STYLE */
          )) : createCommentVNode("v-if", true)
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["rules", "label-width", "class", "label-position", "label-suffix", "model"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/src/index.vue.mjs
var Form = _export_sfc(_sfc_main11, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/form/index.mjs
var PlusForm = Form;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/display-item/src/display-item.mjs
var DisplayComponentMap = {
  img: {
    component: ElImage,
    class: "plus-display-item__image",
    hasSlots: true
  },
  link: {
    component: ElLink,
    class: "plus-display-item__link",
    hasSlots: true
  },
  tag: {
    component: ElTag,
    hasSlots: true
  },
  progress: {
    component: ElProgress,
    hasSlots: true
  },
  avatar: {
    component: ElAvatar,
    hasSlots: true
  },
  "date-picker": {
    component: "span",
    format: formatDate
  },
  money: {
    component: "span",
    format: formatMoney
  },
  code: {
    component: "span",
    class: "plus-display-item__pre"
  }
};
var hasDisplayComponent = (valueType) => Object.keys(DisplayComponentMap).includes(valueType);
var getDisplayComponent = (valueType) => Reflect.get(DisplayComponentMap, valueType) || {};

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/display-item/src/index.vue2.mjs
var _hoisted_18 = ["innerHTML"];
var _hoisted_23 = { class: "plus-display-item" };
var _hoisted_32 = createBaseVNode(
  "svg",
  {
    fill: "none",
    viewBox: "0 0 24 24",
    width: "1em",
    height: "1em",
    class: "t-icon t-icon-edit-1",
    "pointer-events": "none"
  },
  [
    createBaseVNode("path", {
      fill: "currentColor",
      d: "M16.83 1.42l5.75 5.75L7.75 22H2v-5.75L16.83 1.42zm0 8.68l2.92-2.93-2.92-2.93-2.93 2.93 2.93 2.93zm-4.34-1.51L4 17.07V20h2.93l8.48-8.49L12.5 8.6z"
    })
  ],
  -1
  /* HOISTED */
);
var _sfc_main12 = defineComponent({
  ...{
    name: "PlusDisplayItem"
  },
  __name: "index",
  props: {
    column: { default: () => ({ prop: "", label: "" }) },
    row: { default: () => ({}) },
    index: { default: 0 },
    editable: { type: [Boolean, String], default: false },
    rest: { default: () => ({}) }
  },
  emits: ["change"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const customFieldProps = ref({});
    const formInstance = ref();
    const { customOptions: options } = useGetOptions(props.column);
    const columns = ref([]);
    const subRow = ref(cloneDeep_default(props.row));
    const customFieldPropsIsReady = ref(false);
    const isEdit = ref(false);
    const falseArray = [false, "click", "dblclick"];
    const statusValueTypes = ["select", "radio", "checkbox"];
    watch(
      () => props.row,
      (val) => {
        subRow.value = cloneDeep_default(val);
      },
      {
        deep: true
      }
    );
    watch(
      () => [props.editable, props.column.editable],
      () => {
        if (props.column.editable === true) {
          isEdit.value = true;
          return;
        }
        if (props.column.editable === false) {
          isEdit.value = false;
          return;
        }
        if (props.editable === true) {
          isEdit.value = true;
          return;
        }
        if (falseArray.includes(props.editable)) {
          isEdit.value = false;
          return;
        }
      },
      {
        immediate: true
      }
    );
    const hasEditIcon = computed(
      () => (props.editable === "click" || props.editable === "dblclick") && props.column.editable !== false
    );
    const displayValue = computed({
      get() {
        return getValue(subRow.value, props.column.prop);
      },
      set(value) {
        setValue(subRow.value, props.column.prop, value);
      }
    });
    const formatterValue = computed(() => {
      const value = props.column.valueType === "link" ? props.column.linkText || displayValue.value : displayValue.value;
      if (!statusValueTypes.includes(props.column.valueType) && !isEdit.value) {
        if (props.column.formatter && isFunction(props.column.formatter)) {
          return props.column.formatter(value, renderParams.value);
        }
        if (displayComponent.value.format && isFunction(displayComponent.value.format)) {
          return displayComponent.value.format(
            value,
            customFieldProps.value.format || customFieldProps.value.valueFormat
          );
        }
      }
      return value;
    });
    const modelValues = computed({
      get() {
        return { [props.column.prop]: displayValue.value };
      },
      set(values) {
        displayValue.value = values[props.column.prop];
      }
    });
    const isTagAndNoValue = computed(
      () => props.column.valueType === "tag" && (displayValue.value === void 0 || displayValue.value === null || displayValue.value === "")
    );
    const renderParams = computed(() => ({
      prop: props.column.prop,
      valueType: props.column.valueType,
      row: subRow.value,
      index: props.index,
      rowIndex: props.index,
      fieldProps: customFieldProps.value,
      ...props.rest,
      column: { ...props.rest.column, ...props.column }
    }));
    const imageUrl = computed(() => {
      const option = formatterValue.value;
      if (option && isString(option)) {
        return { options: [option], url: option };
      }
      if (isArray(option)) {
        return { options: option, url: option[0] };
      }
      return { options: [], url: "" };
    });
    const getStatus = computed(() => {
      var _a, _b, _c, _d, _e;
      if (((_a = props.column) == null ? void 0 : _a.customGetStatus) && isFunction((_b = props.column) == null ? void 0 : _b.customGetStatus)) {
        const option2 = (_c = props.column) == null ? void 0 : _c.customGetStatus({
          options: options.value,
          value: displayValue.value,
          row: subRow.value
        });
        return option2 || { label: "", value: "" };
      }
      if (
        // select 多选
        props.column.valueType === "select" && customFieldProps.value.multiple === true || // checkbox
        props.column.valueType === "checkbox"
      ) {
        const option2 = ((_d = options.value) == null ? void 0 : _d.filter((i) => {
          var _a2;
          return (_a2 = displayValue.value) == null ? void 0 : _a2.includes(i.value);
        })) || [];
        return option2;
      }
      const option = ((_e = options.value) == null ? void 0 : _e.find(
        (i) => i.value === displayValue.value
      )) || { label: "", value: "" };
      return option;
    });
    const displayComponent = computed(() => getDisplayComponent(props.column.valueType));
    const displayComponentProps = computed(() => {
      return {
        // img
        ...props.column.valueType === "img" ? {
          fit: "cover",
          previewTeleported: true,
          src: imageUrl.value.url,
          previewSrcList: props.column.preview !== false ? imageUrl.value.options : []
        } : null,
        // progress
        ...props.column.valueType === "progress" ? {
          percentage: formatterValue.value
        } : null,
        // link
        ...props.column.valueType === "link" ? {
          type: "primary"
        } : null,
        // avatar
        ...props.column.valueType === "avatar" ? {
          src: formatterValue.value
        } : null,
        ...customFieldProps.value
      };
    });
    watch(
      () => props.column,
      (val) => {
        if (val) {
          columns.value = [val];
        }
      },
      {
        immediate: true,
        deep: true
      }
    );
    watch(
      () => props.column.fieldProps,
      (val) => {
        getCustomProps(val, displayValue.value, subRow.value, props.index, "fieldProps").then((data) => {
          customFieldProps.value = data;
          customFieldPropsIsReady.value = true;
        }).catch((err) => {
          throw err;
        });
      },
      {
        immediate: true,
        deep: true
      }
    );
    watch(
      () => props.row,
      (val) => {
        subRow.value = { ...val };
      },
      {
        deep: true
      }
    );
    const copy = (data) => {
      const url = data;
      const textarea = document.createElement("textarea");
      textarea.readOnly = true;
      textarea.style.position = "absolute";
      textarea.style.left = "-9999px";
      textarea.value = url;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("Copy");
      textarea.remove();
    };
    const handelClickCopy = (item, row) => {
      copy(row[item.prop]);
      row.isCopy = true;
      setTimeout(() => {
        row.isCopy = false;
      }, 3e3);
    };
    const handleChange = (values) => {
      emit("change", {
        value: values[props.column.prop],
        prop: props.column.prop,
        // 兼容 value 代码
        row: { value: subRow.value, ...subRow.value }
      });
    };
    const startCellEdit = () => {
      if (props.column.editable === false) {
        isEdit.value = false;
        return;
      }
      isEdit.value = true;
    };
    const stopCellEdit = () => {
      if (props.column.editable === true) {
        isEdit.value = true;
        return;
      }
      isEdit.value = false;
    };
    const getDisplayItemInstance = () => {
      return {
        isEdit,
        index: props.index,
        prop: props.column.prop,
        formInstance: computed(() => {
          var _a;
          return (_a = formInstance.value) == null ? void 0 : _a.formInstance;
        })
      };
    };
    __expose({
      startCellEdit,
      stopCellEdit,
      getDisplayItemInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        Fragment,
        null,
        [
          createCommentVNode(" 表单第一优先级 "),
          isEdit.value ? (openBlock(), createBlock(unref(PlusForm), mergeProps({
            key: 0,
            ref_key: "formInstance",
            ref: formInstance,
            modelValue: modelValues.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => modelValues.value = $event),
            model: modelValues.value,
            columns: columns.value,
            "has-footer": false,
            "has-label": false
          }, _ctx.column.formProps, {
            class: "plus-display-item__form",
            onChange: handleChange
          }), createSlots({
            _: 2
            /* DYNAMIC */
          }, [
            _ctx.$slots[unref(getFieldSlotName)(_ctx.column.prop)] ? {
              name: unref(getFieldSlotName)(_ctx.column.prop),
              fn: withCtx((data) => [
                renderSlot(_ctx.$slots, unref(getFieldSlotName)(_ctx.column.prop), mergeProps(data, { row: subRow.value }))
              ]),
              key: "0"
            } : void 0,
            _ctx.$slots[unref(getExtraSlotName)(_ctx.column.prop)] ? {
              name: unref(getExtraSlotName)(_ctx.column.prop),
              fn: withCtx((data) => [
                renderSlot(_ctx.$slots, unref(getExtraSlotName)(_ctx.column.prop), mergeProps(data, { row: subRow.value }))
              ]),
              key: "1"
            } : void 0
          ]), 1040, ["modelValue", "model", "columns"])) : _ctx.column.render && unref(isFunction)(_ctx.column.render) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 1 },
            [
              createCommentVNode(" 自定义显示 "),
              customFieldPropsIsReady.value ? (openBlock(), createBlock(unref(PlusRender), {
                key: 0,
                render: _ctx.column.render,
                params: renderParams.value,
                "callback-value": displayValue.value,
                "custom-field-props": customFieldProps.value
              }, null, 8, ["render", "params", "callback-value", "custom-field-props"])) : createCommentVNode("v-if", true)
            ],
            64
            /* STABLE_FRAGMENT */
          )) : _ctx.$slots[unref(getTableCellSlotName)(_ctx.column.prop)] ? (openBlock(), createElementBlock(
            Fragment,
            { key: 2 },
            [
              createCommentVNode(" 插槽 "),
              renderSlot(_ctx.$slots, unref(getTableCellSlotName)(_ctx.column.prop), mergeProps({ value: displayValue.value }, renderParams.value))
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : _ctx.column.renderHTML && unref(isFunction)(_ctx.column.renderHTML) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 3 },
            [
              createCommentVNode("显示HTML "),
              createBaseVNode("span", {
                class: "plus-display-item",
                innerHTML: _ctx.column.renderHTML(displayValue.value, renderParams.value)
              }, null, 8, _hoisted_18)
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : statusValueTypes.includes(_ctx.column.valueType) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 4 },
            [
              createCommentVNode(" 状态显示 `select`, `radio`, `checkbox`"),
              createBaseVNode(
                "span",
                mergeProps({ class: "plus-display-item plus-display-item__badge" }, customFieldProps.value, {
                  class: { "is-list": unref(isArray)(getStatus.value) }
                }),
                [
                  createCommentVNode(" 多选 "),
                  unref(isArray)(getStatus.value) ? (openBlock(), createElementBlock(
                    Fragment,
                    { key: 0 },
                    [
                      unref(isFunction)(_ctx.column.formatter) ? (openBlock(), createElementBlock(
                        Fragment,
                        { key: 0 },
                        [
                          createTextVNode(
                            toDisplayString(_ctx.column.formatter(displayValue.value, renderParams.value)),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      )) : (openBlock(true), createElementBlock(
                        Fragment,
                        { key: 1 },
                        renderList(getStatus.value, (item) => {
                          return openBlock(), createElementBlock("span", {
                            key: String(item.value),
                            class: "plus-display-item__badge__item"
                          }, [
                            createBaseVNode(
                              "i",
                              {
                                class: normalizeClass([
                                  "plus-display-item__badge__dot",
                                  item.type && !item.color ? "plus-display-item__badge__dot--" + item.type : ""
                                ]),
                                style: normalizeStyle({ backgroundColor: item.color })
                              },
                              null,
                              6
                              /* CLASS, STYLE */
                            ),
                            createTextVNode(
                              " " + toDisplayString(item.label),
                              1
                              /* TEXT */
                            )
                          ]);
                        }),
                        128
                        /* KEYED_FRAGMENT */
                      ))
                    ],
                    64
                    /* STABLE_FRAGMENT */
                  )) : (openBlock(), createElementBlock(
                    Fragment,
                    { key: 1 },
                    [
                      createCommentVNode(" 单选 "),
                      getStatus.value.color || getStatus.value.type ? (openBlock(), createElementBlock(
                        "i",
                        {
                          key: 0,
                          class: normalizeClass([
                            "plus-display-item__badge__dot",
                            getStatus.value.type && !getStatus.value.color ? "plus-display-item__badge__dot--" + getStatus.value.type : ""
                          ]),
                          style: normalizeStyle({ backgroundColor: getStatus.value.color })
                        },
                        null,
                        6
                        /* CLASS, STYLE */
                      )) : createCommentVNode("v-if", true),
                      createTextVNode(
                        " " + toDisplayString(unref(isFunction)(_ctx.column.formatter) ? _ctx.column.formatter(displayValue.value, renderParams.value) : getStatus.value.label),
                        1
                        /* TEXT */
                      )
                    ],
                    64
                    /* STABLE_FRAGMENT */
                  ))
                ],
                16
                /* FULL_PROPS */
              )
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : _ctx.column.valueType === "copy" ? (openBlock(), createElementBlock(
            Fragment,
            { key: 5 },
            [
              createCommentVNode(" 复制 "),
              createBaseVNode("span", _hoisted_23, [
                createTextVNode(
                  toDisplayString(formatterValue.value) + " ",
                  1
                  /* TEXT */
                ),
                displayValue.value ? (openBlock(), createBlock(
                  unref(ElIcon),
                  mergeProps({
                    key: 0,
                    size: "16",
                    class: "plus-display-item__icon__copy"
                  }, customFieldProps.value, {
                    onClick: _cache[1] || (_cache[1] = ($event) => handelClickCopy(_ctx.column, subRow.value))
                  }),
                  {
                    default: withCtx(() => [
                      !subRow.value.isCopy ? (openBlock(), createBlock(unref(document_copy_default), { key: 0 })) : (openBlock(), createBlock(unref(select_default), { key: 1 }))
                    ]),
                    _: 1
                    /* STABLE */
                  },
                  16
                  /* FULL_PROPS */
                )) : createCommentVNode("v-if", true)
              ])
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : unref(hasDisplayComponent)(_ctx.column.valueType) ? (openBlock(), createElementBlock(
            Fragment,
            { key: 6 },
            [
              createCommentVNode(" 统一处理 "),
              createCommentVNode("has slots  "),
              displayComponent.value.hasSlots ? (openBlock(), createBlock(resolveDynamicComponent(isTagAndNoValue.value ? "span" : displayComponent.value.component), mergeProps({
                key: 0,
                class: ["plus-display-item", displayComponent.value.class]
              }, { ...renderParams.value, ...displayComponentProps.value }), createSlots({
                default: withCtx(() => [
                  createTextVNode(
                    " " + toDisplayString(formatterValue.value),
                    1
                    /* TEXT */
                  )
                ]),
                _: 2
                /* DYNAMIC */
              }, [
                renderList(_ctx.column.fieldSlots, (fieldSlot, key) => {
                  return {
                    name: key,
                    fn: withCtx((data) => [
                      (openBlock(), createBlock(resolveDynamicComponent(fieldSlot), mergeProps({ value: displayValue.value }, { ...renderParams.value, ...data }), null, 16, ["value"]))
                    ])
                  };
                })
              ]), 1040, ["class"])) : (openBlock(), createElementBlock(
                Fragment,
                { key: 1 },
                [
                  createCommentVNode("no slots  "),
                  (openBlock(), createBlock(resolveDynamicComponent(displayComponent.value.component), mergeProps({
                    class: ["plus-display-item", displayComponent.value.class]
                  }, { ...renderParams.value, ...displayComponentProps.value }), {
                    default: withCtx(() => [
                      createTextVNode(
                        toDisplayString(formatterValue.value),
                        1
                        /* TEXT */
                      )
                    ]),
                    _: 1
                    /* STABLE */
                  }, 16, ["class"]))
                ],
                2112
                /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
              ))
            ],
            64
            /* STABLE_FRAGMENT */
          )) : _ctx.column.valueType === "divider" ? (openBlock(), createBlock(
            unref(ElDivider),
            mergeProps({
              key: 7,
              ref: "fieldInstance",
              class: "plus-form-item-field"
            }, customFieldProps.value),
            {
              default: withCtx(() => [
                createTextVNode(
                  toDisplayString(formatterValue.value),
                  1
                  /* TEXT */
                )
              ]),
              _: 1
              /* STABLE */
            },
            16
            /* FULL_PROPS */
          )) : (openBlock(), createElementBlock(
            Fragment,
            { key: 8 },
            [
              createCommentVNode(" 没有format "),
              createBaseVNode(
                "span",
                mergeProps({ class: "plus-display-item" }, customFieldProps.value),
                toDisplayString(formatterValue.value),
                17
                /* TEXT, FULL_PROPS */
              )
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )),
          renderSlot(_ctx.$slots, "edit-icon", {}, () => [
            hasEditIcon.value && !isEdit.value ? (openBlock(), createBlock(unref(ElIcon), {
              key: 0,
              size: 16,
              class: "plus-display-item__edit-icon",
              "pointer-events": "none"
            }, {
              default: withCtx(() => [
                _hoisted_32
              ]),
              _: 1
              /* STABLE */
            })) : createCommentVNode("v-if", true)
          ])
        ],
        64
        /* STABLE_FRAGMENT */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/display-item/src/index.vue.mjs
var DisplayItem = _export_sfc(_sfc_main12, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/display-item/index.mjs
var PlusDisplayItem = DisplayItem;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column.vue2.mjs
var _hoisted_19 = { class: "plus-table-column__header" };
var _sfc_main13 = defineComponent({
  ...{
    name: "PlusTableTableColumn"
  },
  __name: "table-column",
  props: {
    columns: { default: () => [] },
    editable: { type: [Boolean, String], default: false }
  },
  emits: ["formChange"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const plusDisplayItemInstance = ref();
    const formRef = inject(TableFormRefInjectionKey);
    const setFormRef = () => {
      var _a, _b;
      if (!((_a = plusDisplayItemInstance.value) == null ? void 0 : _a.length))
        return;
      const data = {};
      const list = ((_b = plusDisplayItemInstance.value) == null ? void 0 : _b.map((item) => ({ ...item, ...item == null ? void 0 : item.getDisplayItemInstance() }))) || [];
      list.forEach((item) => {
        if (!data[item.index]) {
          data[item.index] = [];
        }
        data[item.index].push(item);
      });
      formRef.value = data;
    };
    watch(
      plusDisplayItemInstance,
      () => {
        setFormRef();
      },
      {
        deep: true
      }
    );
    const hasPropsEditIcon = computed(() => props.editable === "click" || props.editable === "dblclick");
    const getKey = (item) => getTableKey(item, true);
    const handleChange = (data, index, column, item, rest) => {
      const formChangeCallBackParams = {
        ...data,
        index,
        column: { ...column, ...item },
        rowIndex: index,
        ...rest
      };
      emit("formChange", formChangeCallBackParams);
    };
    __expose({
      plusDisplayItemInstance
    });
    return (_ctx, _cache) => {
      return openBlock(true), createElementBlock(
        Fragment,
        null,
        renderList(_ctx.columns, (item, index) => {
          return openBlock(), createBlock(unref(ElTableColumn), mergeProps({
            key: getKey(item),
            "class-name": "plus-table-column " + (hasPropsEditIcon.value ? "plus-table-column__edit" : "")
          }, item.tableColumnProps, {
            prop: item.prop,
            width: item.width,
            "min-width": item.minWidth,
            index
          }), {
            header: withCtx((scoped) => [
              createBaseVNode("span", _hoisted_19, [
                item.renderHeader && unref(isFunction)(item.renderHeader) ? (openBlock(), createBlock(unref(PlusRender), {
                  key: 0,
                  render: item.renderHeader,
                  params: { ...scoped, ...item, cellIndex: index },
                  "callback-value": unref(getLabel)(item.label)
                }, null, 8, ["render", "params", "callback-value"])) : (openBlock(), createElementBlock(
                  Fragment,
                  { key: 1 },
                  [
                    createCommentVNode("表格单元格Header的插槽 "),
                    renderSlot(_ctx.$slots, unref(getTableHeaderSlotName)(item.prop), mergeProps({
                      prop: item.prop,
                      label: unref(getLabel)(item.label),
                      fieldProps: item.fieldProps,
                      valueType: item.valueType,
                      cellIndex: index
                    }, scoped, {
                      column: { ...scoped, ...item }
                    }), () => [
                      createTextVNode(
                        toDisplayString(unref(getLabel)(item.label)),
                        1
                        /* TEXT */
                      )
                    ])
                  ],
                  2112
                  /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                )),
                item.tooltip ? (openBlock(), createBlock(
                  unref(ElTooltip),
                  mergeProps({
                    key: 2,
                    placement: "top"
                  }, unref(getTooltip)(item.tooltip)),
                  {
                    default: withCtx(() => [
                      renderSlot(_ctx.$slots, "tooltip-icon", {}, () => [
                        createVNode(unref(ElIcon), {
                          class: "plus-table-column__header__icon",
                          size: 16
                        }, {
                          default: withCtx(() => [
                            createVNode(unref(question_filled_default))
                          ]),
                          _: 1
                          /* STABLE */
                        })
                      ])
                    ]),
                    _: 2
                    /* DYNAMIC */
                  },
                  1040
                  /* FULL_PROPS, DYNAMIC_SLOTS */
                )) : createCommentVNode("v-if", true)
              ])
            ]),
            default: withCtx(({ row, column, $index, ...rest }) => [
              createVNode(unref(PlusDisplayItem), {
                ref_for: true,
                ref_key: "plusDisplayItemInstance",
                ref: plusDisplayItemInstance,
                column: item,
                row,
                index: $index,
                editable: _ctx.editable,
                rest: { column, ...rest },
                onChange: (data) => handleChange(data, $index, column, item, rest)
              }, createSlots({
                _: 2
                /* DYNAMIC */
              }, [
                _ctx.$slots[unref(getFieldSlotName)(item.prop)] ? {
                  name: unref(getFieldSlotName)(item.prop),
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, unref(getFieldSlotName)(item.prop), normalizeProps(guardReactiveProps(data)))
                  ]),
                  key: "0"
                } : void 0,
                _ctx.$slots[unref(getExtraSlotName)(item.prop)] ? {
                  name: unref(getExtraSlotName)(item.prop),
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, unref(getExtraSlotName)(item.prop), normalizeProps(guardReactiveProps(data)))
                  ]),
                  key: "1"
                } : void 0,
                _ctx.$slots[unref(getTableCellSlotName)(item.prop)] ? {
                  name: unref(getTableCellSlotName)(item.prop),
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, unref(getTableCellSlotName)(item.prop), normalizeProps(guardReactiveProps(data)))
                  ]),
                  key: "2"
                } : void 0,
                _ctx.$slots["edit-icon"] ? {
                  name: "edit-icon",
                  fn: withCtx(() => [
                    renderSlot(_ctx.$slots, "edit-icon")
                  ]),
                  key: "3"
                } : void 0
              ]), 1032, ["column", "row", "index", "editable", "rest", "onChange"])
            ]),
            _: 2
            /* DYNAMIC */
          }, 1040, ["class-name", "prop", "width", "min-width", "index"]);
        }),
        128
        /* KEYED_FRAGMENT */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column.vue.mjs
var PlusTableColumn = _export_sfc(_sfc_main13, [["__file", "table-column.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column-index.vue2.mjs
var _sfc_main14 = defineComponent({
  ...{
    name: "PlusTableTableColumnIndex"
  },
  __name: "table-column-index",
  props: {
    pageInfo: { default: () => ({ ...DefaultPageInfo }) },
    indexTableColumnProps: { default: () => ({}) },
    max: { default: 999 },
    indexContentStyle: { type: [Object, Function], default: () => ({}) }
  },
  setup(__props) {
    const props = __props;
    const getTableIndex = (index) => {
      var _a, _b;
      const i = ((((_a = props.pageInfo) == null ? void 0 : _a.page) || DefaultPageInfo.page) - 1) * (((_b = props.pageInfo) == null ? void 0 : _b.pageSize) || DefaultPageInfo.page) + index + 1;
      return +i;
    };
    const indexContentStyle = (row, index) => {
      if (isFunction(props.indexContentStyle)) {
        return props.indexContentStyle(
          row,
          index
        );
      } else if (isPlainObject(props.indexContentStyle)) {
        return props.indexContentStyle;
      } else {
        return {};
      }
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(
        unref(ElTableColumn),
        mergeProps({
          key: "index",
          label: "#",
          fixed: "left",
          type: "index",
          "class-name": "plus-table-column-index",
          width: "60",
          align: "center",
          index: getTableIndex
        }, _ctx.indexTableColumnProps),
        {
          default: withCtx(({ row, $index }) => [
            getTableIndex($index) > _ctx.max ? (openBlock(), createBlock(unref(ElTooltip), {
              key: 0,
              content: String(getTableIndex($index)),
              placement: "top-start"
            }, {
              default: withCtx(() => [
                createBaseVNode(
                  "div",
                  {
                    class: "plus-table-column-index__content",
                    style: normalizeStyle(indexContentStyle(row, $index))
                  },
                  toDisplayString(getTableIndex($index)),
                  5
                  /* TEXT, STYLE */
                )
              ]),
              _: 2
              /* DYNAMIC */
            }, 1032, ["content"])) : (openBlock(), createElementBlock(
              "div",
              {
                key: 1,
                class: "plus-table-column-index__content",
                style: normalizeStyle(indexContentStyle(row, $index))
              },
              toDisplayString(getTableIndex($index)),
              5
              /* TEXT, STYLE */
            ))
          ]),
          _: 1
          /* STABLE */
        },
        16
        /* FULL_PROPS */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column-index.vue.mjs
var PlusTableTableColumnIndex = _export_sfc(_sfc_main14, [["__file", "table-column-index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column-drag-sort.vue2.mjs
var _hoisted_110 = { class: "plus-table-column-drag-icon" };
var _sfc_main15 = defineComponent({
  ...{
    name: "PlusTableColumnDragSort"
  },
  __name: "table-column-drag-sort",
  props: {
    sortable: { type: Boolean, default: true },
    tableInstance: { default: null },
    dragSortableTableColumnProps: { default: () => ({}) }
  },
  emits: ["dragSortEnd"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    watch(
      () => props.tableInstance,
      (val) => {
        if (val && props.sortable) {
          rowDrop();
        }
      }
    );
    const rowDrop = () => {
      var _a, _b;
      const tbody = (_b = (_a = props.tableInstance) == null ? void 0 : _a.$el) == null ? void 0 : _b.querySelector(".el-table__body-wrapper tbody");
      if (!tbody)
        return;
      let config = {
        handle: ".plus-table-column-drag-icon",
        animation: 150,
        group: "box",
        easing: "cubic-bezier(1, 0, 0, 1)",
        chosenClass: "sortable-chosen",
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          emit("dragSortEnd", newIndex, oldIndex);
        }
      };
      if (isPlainObject(props.sortable)) {
        config = { ...config, ...props.sortable };
      }
      sortable_esm_default.create(tbody, config);
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElTableColumn), mergeProps({
        key: "dragSort",
        label: unref(t)("plus.table.sort"),
        width: "60",
        "class-name": "plus-table-column-drag-sort"
      }, _ctx.dragSortableTableColumnProps), {
        default: withCtx(() => [
          createBaseVNode("span", _hoisted_110, [
            renderSlot(_ctx.$slots, "drag-sort-icon", {}, () => [
              createTextVNode("☷")
            ])
          ])
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["label"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-column-drag-sort.vue.mjs
var PlusTableColumnDragSort = _export_sfc(_sfc_main15, [["__file", "table-column-drag-sort.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/popover/src/index.vue2.mjs
var _hoisted_111 = {
  key: 0,
  style: { "padding-top": "12px" }
};
var _sfc_main16 = defineComponent({
  ...{
    name: "PlusPopover"
  },
  __name: "index",
  props: {
    hasShowBottomButton: { type: Boolean, default: false },
    confirmLoading: { type: Boolean, default: false },
    cancelText: { default: "" },
    confirmText: { default: "" }
  },
  emits: ["cancel", "confirm"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const subVisible = ref(false);
    const { t } = useLocale();
    watch(
      () => props.visible,
      (val) => {
        subVisible.value = val;
      },
      {
        immediate: true
      }
    );
    const handleCancelPopover = () => {
      subVisible.value = false;
      emit("cancel");
    };
    const handleConfirmPopover = () => {
      subVisible.value = false;
      emit("confirm");
    };
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElPopover), mergeProps({
        visible: subVisible.value,
        "onUpdate:visible": _cache[0] || (_cache[0] = ($event) => subVisible.value = $event)
      }, _ctx.$attrs), {
        reference: withCtx(() => [
          createBaseVNode("span", null, [
            renderSlot(_ctx.$slots, "reference")
          ])
        ]),
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default"),
          _ctx.hasShowBottomButton ? (openBlock(), createElementBlock("div", _hoisted_111, [
            createVNode(unref(ElButton), {
              size: "small",
              plain: "",
              onClick: handleCancelPopover
            }, {
              default: withCtx(() => [
                createTextVNode(
                  toDisplayString(_ctx.cancelText || unref(t)("plus.popover.cancelText")),
                  1
                  /* TEXT */
                )
              ]),
              _: 1
              /* STABLE */
            }),
            createVNode(unref(ElButton), {
              size: "small",
              type: "primary",
              loading: _ctx.confirmLoading,
              onClick: handleConfirmPopover
            }, {
              default: withCtx(() => [
                createTextVNode(
                  toDisplayString(_ctx.confirmText || unref(t)("plus.popover.confirmText")),
                  1
                  /* TEXT */
                )
              ]),
              _: 1
              /* STABLE */
            }, 8, ["loading"])
          ])) : createCommentVNode("v-if", true)
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["visible"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/popover/src/index.vue.mjs
var Popover = _export_sfc(_sfc_main16, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/popover/index.mjs
var PlusPopover = Popover;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-title-bar.vue2.mjs
var _hoisted_112 = { class: "plus-table-title-bar" };
var _hoisted_24 = { class: "plus-table-title-bar__title" };
var _hoisted_33 = { class: "plus-table-title-bar__toolbar" };
var _hoisted_4 = { class: "plus-table-title-bar__toolbar__density" };
var _hoisted_5 = createBaseVNode(
  "svg",
  {
    viewBox: "0 0 1024 1024",
    focusable: "false",
    "data-icon": "column-height",
    fill: "currentColor",
    "aria-hidden": "true"
  },
  [
    createBaseVNode("path", { d: "M840 836H184c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h656c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zm0-724H184c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h656c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8zM610.8 378c6 0 9.4-7 5.7-11.7L515.7 238.7a7.14 7.14 0 00-11.3 0L403.6 366.3a7.23 7.23 0 005.7 11.7H476v268h-62.8c-6 0-9.4 7-5.7 11.7l100.8 127.5c2.9 3.7 8.5 3.7 11.3 0l100.8-127.5c3.7-4.7.4-11.7-5.7-11.7H548V378h62.8z" })
  ],
  -1
  /* HOISTED */
);
var _hoisted_6 = {
  key: 0,
  class: "plus-table-checkbox-handle"
};
var _hoisted_7 = { key: 1 };
var _hoisted_8 = { key: 1 };
var _sfc_main17 = defineComponent({
  ...{
    name: "PlusTableToolbar"
  },
  __name: "table-title-bar",
  props: {
    columns: { default: () => [] },
    titleBar: { type: [Boolean, Object], default: true },
    filterTableHeaderOverflowLabelLength: { default: 6 },
    defaultSize: { default: "default" },
    columnsIsChange: { type: Boolean, default: false }
  },
  emits: ["filterTable", "clickDensity", "refresh"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const checkboxGroupInstance = ref(null);
    const titleBarConfig = computed(() => props.titleBar);
    const iconSize = computed(() => {
      var _a, _b;
      return ((_b = (_a = titleBarConfig.value) == null ? void 0 : _a.icon) == null ? void 0 : _b.size) || 18;
    });
    const iconColor = computed(() => {
      var _a, _b;
      return ((_b = (_a = titleBarConfig.value) == null ? void 0 : _a.icon) == null ? void 0 : _b.color) || "";
    });
    const columnSetting = computed(() => {
      var _a;
      return (_a = titleBarConfig.value) == null ? void 0 : _a.columnSetting;
    });
    const sortable = ref(null);
    const buttonNameDensity = [
      {
        size: "default",
        text: computed(() => t("plus.table.default"))
      },
      {
        size: "large",
        text: computed(() => t("plus.table.loose"))
      },
      {
        size: "small",
        text: computed(() => t("plus.table.compact"))
      }
    ];
    const getCheckList = (hasDisabled = false) => {
      if (hasDisabled) {
        return props.columns.filter((item) => item.disabledHeaderFilter === true).map((item) => getTableKey(item));
      }
      return props.columns.map((item) => getTableKey(item));
    };
    const state = reactive({
      checkAll: true,
      isIndeterminate: false,
      bigImageVisible: false,
      srcList: [],
      checkList: []
    });
    const setCheckAllState = (value) => {
      const checkedCount = value.length;
      state.checkAll = checkedCount === props.columns.length;
      state.isIndeterminate = checkedCount > 0 && checkedCount < props.columns.length;
    };
    watch(
      () => props.columnsIsChange,
      () => {
        state.checkList = getCheckList();
        setCheckAllState(state.checkList);
      },
      {
        immediate: true
      }
    );
    const handleCheckAllChange = (val) => {
      state.checkList = val ? getCheckList() : getCheckList(true);
      setCheckAllState(state.checkList);
      handleFilterTableConfirm();
    };
    const handleFilterTableConfirm = () => {
      const filterColumns = props.columns.map((item) => {
        if (state.checkList.includes(getTableKey(item))) {
          return { ...item, __selfHideInTable: false };
        }
        return { ...item, __selfHideInTable: true };
      });
      emit("filterTable", filterColumns);
    };
    const handleCheckGroupChange = (value) => {
      setCheckAllState(value);
      handleFilterTableConfirm();
    };
    const handleClickDensity = (size) => {
      emit("clickDensity", size);
    };
    const handleRefresh = () => {
      emit("refresh");
    };
    const getLabelValue = (label) => {
      const tempLabel = getLabel(label);
      if (tempLabel && (tempLabel == null ? void 0 : tempLabel.length) <= props.filterTableHeaderOverflowLabelLength) {
        return tempLabel;
      }
      return (tempLabel == null ? void 0 : tempLabel.slice(0, props.filterTableHeaderOverflowLabelLength)) + "...";
    };
    const handleDrop = () => {
      var _a;
      if (!checkboxGroupInstance.value)
        return;
      let config = {
        onEnd: handleDragEnd,
        ghostClass: "plus-table-ghost-class"
      };
      const dragSort = (_a = columnSetting.value) == null ? void 0 : _a.dragSort;
      if (isPlainObject(dragSort)) {
        config = { ...config, ...dragSort, handle: ".plus-table-checkbox-handle" };
      }
      sortable.value = new sortable_esm_default(checkboxGroupInstance.value, config);
    };
    const handleDragEnd = (event) => {
      const subDragCheckboxList = cloneDeep_default(props.columns);
      const draggedCheckbox = props.columns[event.oldIndex];
      subDragCheckboxList.splice(event.oldIndex, 1);
      subDragCheckboxList.splice(event.newIndex, 0, draggedCheckbox);
      const list = subDragCheckboxList.filter((item) => item);
      emit("filterTable", list);
    };
    onMounted(() => {
      var _a;
      const dragSort = (_a = columnSetting.value) == null ? void 0 : _a.dragSort;
      if (dragSort !== false) {
        if (checkboxGroupInstance.value) {
          handleDrop();
        }
      }
    });
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return openBlock(), createElementBlock("div", _hoisted_112, [
        createBaseVNode("div", _hoisted_24, [
          renderSlot(_ctx.$slots, "title", {}, () => [
            createTextVNode(
              toDisplayString(titleBarConfig.value.title),
              1
              /* TEXT */
            )
          ])
        ]),
        createBaseVNode("div", _hoisted_33, [
          renderSlot(_ctx.$slots, "toolbar"),
          ((_a = titleBarConfig.value) == null ? void 0 : _a.refresh) === true ? (openBlock(), createElementBlock("span", {
            key: 0,
            class: "plus-table-title-bar__toolbar__refresh",
            onClick: handleRefresh
          }, [
            createVNode(unref(ElTooltip), {
              effect: "dark",
              content: unref(t)("plus.table.refresh"),
              placement: "top"
            }, {
              default: withCtx(() => [
                renderSlot(_ctx.$slots, "refresh-icon", {}, () => [
                  createVNode(unref(ElIcon), {
                    size: iconSize.value,
                    color: iconColor.value,
                    class: "plus-table-title-bar__toolbar__icon"
                  }, {
                    default: withCtx(() => [
                      createVNode(unref(refresh_right_default))
                    ]),
                    _: 1
                    /* STABLE */
                  }, 8, ["size", "color"])
                ])
              ]),
              _: 3
              /* FORWARDED */
            }, 8, ["content"])
          ])) : createCommentVNode("v-if", true),
          createCommentVNode(" 表格密度 "),
          ((_b = titleBarConfig.value) == null ? void 0 : _b.density) !== false ? (openBlock(), createBlock(unref(PlusPopover), {
            key: 1,
            placement: "bottom",
            width: 150,
            trigger: "click",
            title: unref(t)("plus.table.density")
          }, {
            reference: withCtx(() => [
              createVNode(unref(ElTooltip), {
                effect: "dark",
                content: unref(t)("plus.table.density"),
                placement: "top"
              }, {
                default: withCtx(() => [
                  renderSlot(_ctx.$slots, "density-icon", {}, () => [
                    createVNode(unref(ElIcon), {
                      size: iconSize.value,
                      color: iconColor.value,
                      class: "plus-table-title-bar__toolbar__icon"
                    }, {
                      default: withCtx(() => [
                        _hoisted_5
                      ]),
                      _: 1
                      /* STABLE */
                    }, 8, ["size", "color"])
                  ])
                ]),
                _: 3
                /* FORWARDED */
              }, 8, ["content"])
            ]),
            default: withCtx(() => [
              createBaseVNode("div", _hoisted_4, [
                (openBlock(), createElementBlock(
                  Fragment,
                  null,
                  renderList(buttonNameDensity, (item) => {
                    return createVNode(unref(ElButton), {
                      key: item.size,
                      plain: _ctx.defaultSize !== item.size,
                      type: "primary",
                      size: "small",
                      onClick: ($event) => handleClickDensity(item.size)
                    }, {
                      default: withCtx(() => [
                        createTextVNode(
                          toDisplayString(unref(item.text)),
                          1
                          /* TEXT */
                        )
                      ]),
                      _: 2
                      /* DYNAMIC */
                    }, 1032, ["plain", "onClick"]);
                  }),
                  64
                  /* STABLE_FRAGMENT */
                ))
              ])
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["title"])) : createCommentVNode("v-if", true),
          createCommentVNode(" 列设置 "),
          ((_c = titleBarConfig.value) == null ? void 0 : _c.columnSetting) !== false ? (openBlock(), createBlock(unref(PlusPopover), {
            key: 2,
            placement: "bottom",
            width: 100,
            trigger: "click",
            title: unref(t)("plus.table.columnSettings")
          }, {
            reference: withCtx(() => [
              createVNode(unref(ElTooltip), {
                effect: "dark",
                content: unref(t)("plus.table.columnSettings"),
                placement: "top"
              }, {
                default: withCtx(() => [
                  renderSlot(_ctx.$slots, "column-settings-icon", {}, () => [
                    createVNode(unref(ElIcon), {
                      size: iconSize.value,
                      color: iconColor.value,
                      class: "plus-table-title-bar__toolbar__icon"
                    }, {
                      default: withCtx(() => [
                        createVNode(unref(setting_default))
                      ]),
                      _: 1
                      /* STABLE */
                    }, 8, ["size", "color"])
                  ])
                ]),
                _: 3
                /* FORWARDED */
              }, 8, ["content"])
            ]),
            default: withCtx(() => [
              createVNode(unref(ElCheckbox), {
                modelValue: state.checkAll,
                "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.checkAll = $event),
                indeterminate: state.isIndeterminate,
                onChange: handleCheckAllChange
              }, {
                default: withCtx(() => [
                  createTextVNode(
                    toDisplayString(unref(t)("plus.table.selectAll")),
                    1
                    /* TEXT */
                  )
                ]),
                _: 1
                /* STABLE */
              }, 8, ["modelValue", "indeterminate"]),
              createVNode(unref(ElCheckboxGroup), {
                modelValue: state.checkList,
                "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => state.checkList = $event),
                onChange: handleCheckGroupChange
              }, {
                default: withCtx(() => [
                  createBaseVNode(
                    "div",
                    {
                      ref_key: "checkboxGroupInstance",
                      ref: checkboxGroupInstance,
                      class: "plus-table-checkbox-sortable-list"
                    },
                    [
                      (openBlock(true), createElementBlock(
                        Fragment,
                        null,
                        renderList(_ctx.columns, (item) => {
                          var _a2;
                          return openBlock(), createElementBlock("div", {
                            key: item.prop,
                            class: "plus-table-checkbox-item"
                          }, [
                            ((_a2 = columnSetting.value) == null ? void 0 : _a2.dragSort) !== false ? (openBlock(), createElementBlock("div", _hoisted_6, [
                              renderSlot(_ctx.$slots, "drag-sort-icon", {}, () => [
                                createTextVNode("☷")
                              ])
                            ])) : createCommentVNode("v-if", true),
                            createCommentVNode(" element-plus 版本号小于2.6.0 "),
                            unref(versionIsLessThan260) ? (openBlock(), createBlock(unref(ElCheckbox), {
                              key: 1,
                              label: unref(getTableKey)(item),
                              disabled: item.disabledHeaderFilter,
                              class: "plus-table-title-bar__toolbar__checkbox__item"
                            }, {
                              default: withCtx(() => [
                                unref(getLabel)(item.label).length > _ctx.filterTableHeaderOverflowLabelLength ? (openBlock(), createBlock(unref(ElTooltip), {
                                  key: 0,
                                  content: unref(getLabel)(item.label),
                                  placement: "right-start"
                                }, {
                                  default: withCtx(() => [
                                    createTextVNode(
                                      toDisplayString(getLabelValue(item.label)),
                                      1
                                      /* TEXT */
                                    )
                                  ]),
                                  _: 2
                                  /* DYNAMIC */
                                }, 1032, ["content"])) : (openBlock(), createElementBlock(
                                  "span",
                                  _hoisted_7,
                                  toDisplayString(item.label ? getLabelValue(item.label) : ""),
                                  1
                                  /* TEXT */
                                ))
                              ]),
                              _: 2
                              /* DYNAMIC */
                            }, 1032, ["label", "disabled"])) : (openBlock(), createElementBlock(
                              Fragment,
                              { key: 2 },
                              [
                                createCommentVNode(" element-plus 版本号大于等于2.6.0 "),
                                createVNode(unref(ElCheckbox), {
                                  value: unref(getTableKey)(item),
                                  disabled: item.disabledHeaderFilter,
                                  class: "plus-table-title-bar__toolbar__checkbox__item"
                                }, {
                                  default: withCtx(() => [
                                    unref(getLabel)(item.label).length > _ctx.filterTableHeaderOverflowLabelLength ? (openBlock(), createBlock(unref(ElTooltip), {
                                      key: 0,
                                      content: unref(getLabel)(item.label),
                                      placement: "right-start"
                                    }, {
                                      default: withCtx(() => [
                                        createTextVNode(
                                          toDisplayString(getLabelValue(item.label)),
                                          1
                                          /* TEXT */
                                        )
                                      ]),
                                      _: 2
                                      /* DYNAMIC */
                                    }, 1032, ["content"])) : (openBlock(), createElementBlock(
                                      "span",
                                      _hoisted_8,
                                      toDisplayString(item.label ? getLabelValue(item.label) : ""),
                                      1
                                      /* TEXT */
                                    ))
                                  ]),
                                  _: 2
                                  /* DYNAMIC */
                                }, 1032, ["value", "disabled"])
                              ],
                              64
                              /* STABLE_FRAGMENT */
                            ))
                          ]);
                        }),
                        128
                        /* KEYED_FRAGMENT */
                      ))
                    ],
                    512
                    /* NEED_PATCH */
                  )
                ]),
                _: 3
                /* FORWARDED */
              }, 8, ["modelValue"])
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["title"])) : createCommentVNode("v-if", true)
        ])
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/table-title-bar.vue.mjs
var PlusTableTitleBar = _export_sfc(_sfc_main17, [["__file", "table-title-bar.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/index.vue2.mjs
var _hoisted_113 = { class: "plus-table-expand-col" };
var _sfc_main18 = defineComponent({
  ...{
    name: "PlusTable",
    inheritAttrs: false
  },
  __name: "index",
  props: {
    tableData: { default: () => [] },
    data: { default: () => [] },
    columns: { default: () => [] },
    defaultSize: { default: "default" },
    pagination: { type: [Boolean, Object], default: false },
    actionBar: { type: [Boolean, Object], default: false },
    hasIndexColumn: { type: Boolean, default: false },
    titleBar: { type: [Boolean, Object], default: true },
    isSelection: { type: Boolean, default: false },
    hasExpand: { type: Boolean, default: false },
    loadingStatus: { type: Boolean, default: false },
    height: {},
    headerCellStyle: { default: () => ({
      "background-color": "var(--el-fill-color-light)"
    }) },
    rowKey: { type: [String, Function], default: "id" },
    dragSortable: { type: [Boolean, Object], default: false },
    dragSortableTableColumnProps: { default: () => ({}) },
    indexTableColumnProps: { default: () => ({}) },
    selectionTableColumnProps: { default: () => ({
      width: 40
    }) },
    expandTableColumnProps: { default: () => ({}) },
    indexContentStyle: { type: [Object, Function], default: () => ({}) },
    editable: { type: [Boolean, String], default: false }
  },
  emits: ["paginationChange", "clickAction", "clickActionConfirmCancel", "dragSortEnd", "formChange", "refresh", "edited", "cell-click", "cell-dblclick"],
  setup(__props, { expose: __expose, emit: __emit }) {
    var _a;
    const props = __props;
    const emit = __emit;
    const subColumns = ref([]);
    const columnsIsChange = ref(false);
    const filterColumns = ref([]);
    const tableInstance = shallowRef(null);
    const tableWrapperInstance = ref(null);
    const state = reactive({
      subPageInfo: {
        ...((_a = props.pagination) == null ? void 0 : _a.modelValue) || DefaultPageInfo
      },
      size: props.defaultSize
    });
    const __tableData = computed(
      () => {
        var _a2;
        return ((_a2 = props.tableData) == null ? void 0 : _a2.length) ? props.tableData : props.data;
      }
    );
    const slots = useSlots();
    const cellSlots = filterSlots(slots, getTableCellSlotName());
    const headerSlots = filterSlots(slots, getTableHeaderSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const extraSlots = filterSlots(slots, getExtraSlotName());
    const formRefs = shallowRef({});
    provide(TableFormRefInjectionKey, formRefs);
    const formFieldRefs = shallowRef({});
    provide(TableFormFieldRefInjectionKey, formFieldRefs);
    watch(
      () => props.columns,
      (val) => {
        subColumns.value = val.filter((item) => unref(item.hideInTable) !== true);
        filterColumns.value = cloneDeep_default(subColumns.value);
        columnsIsChange.value = !columnsIsChange.value;
      },
      {
        deep: true,
        immediate: true
      }
    );
    const handlePaginationChange = () => {
      emit("paginationChange", { ...state.subPageInfo });
    };
    const handleAction = (callbackParams) => {
      emit("clickAction", callbackParams);
    };
    const handleClickActionConfirmCancel = (callbackParams) => {
      emit("clickActionConfirmCancel", callbackParams);
    };
    const handleFilterTableConfirm = (_columns) => {
      filterColumns.value = _columns;
      subColumns.value = _columns.filter(
        (item) => unref(item.hideInTable) !== true && item.__selfHideInTable !== true
      );
    };
    const handleClickDensity = (size2) => {
      state.size = size2;
    };
    const handleDragSortEnd = (newIndex, oldIndex) => {
      emit("dragSortEnd", newIndex, oldIndex);
    };
    const handleRefresh = () => {
      emit("refresh");
    };
    const handleFormChange = (data) => {
      emit("formChange", data);
    };
    const currentForm = ref();
    const handleCellEdit = (row, column, type) => {
      var _a2;
      const rowIndex = __tableData.value.indexOf(row);
      const columnIndex = column.index;
      const columnConfig = subColumns.value[column.index];
      if (!columnConfig)
        return;
      if (props.editable === type) {
        document.addEventListener("click", handleStopEditClick);
        const currentCellForm = formRefs.value[rowIndex][columnIndex];
        if (currentForm.value) {
          (_a2 = currentForm.value) == null ? void 0 : _a2.stopCellEdit();
        }
        currentForm.value = currentCellForm;
        currentCellForm.startCellEdit();
        const unwatch = watch(
          () => formFieldRefs.value.valueIsReady,
          (val) => {
            var _a3, _b;
            if ((val == null ? void 0 : val.value) && ((_b = (_a3 = formFieldRefs.value) == null ? void 0 : _a3.fieldInstance) == null ? void 0 : _b.focus) && (props.editable === "click" || props.editable === "dblclick")) {
              formFieldRefs.value.fieldInstance.focus();
              unwatch();
            }
          }
        );
      }
    };
    const handleClickCell = (row, column, cell, event) => {
      handleCellEdit(row, column, "click");
      emit("cell-click", row, column, cell, event);
    };
    const handleDoubleClickCell = (row, column, cell, event) => {
      handleCellEdit(row, column, "dblclick");
      emit("cell-dblclick", row, column, cell, event);
    };
    const handleStopEditClick = (e) => {
      var _a2;
      if (tableWrapperInstance.value && currentForm.value) {
        const wrapperClass = ".el-table__body-wrapper";
        const tbody = tableWrapperInstance.value.querySelector(wrapperClass);
        const target = e == null ? void 0 : e.target;
        const cls = Array.from(target.classList).join(".");
        const tempCls = cls ? `.${cls}` : "";
        const contains = tempCls && tbody.querySelector(tempCls);
        if (!contains && !isSVGElement(target)) {
          (_a2 = currentForm.value) == null ? void 0 : _a2.stopCellEdit();
          emit("edited");
          document.removeEventListener("click", handleStopEditClick);
        }
      }
    };
    const { subPageInfo, size } = toRefs(state);
    __expose({
      formRefs,
      tableInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        "div",
        {
          ref_key: "tableWrapperInstance",
          ref: tableWrapperInstance,
          class: "plus-table"
        },
        [
          _ctx.titleBar ? (openBlock(), createBlock(PlusTableTitleBar, {
            key: 0,
            columns: filterColumns.value,
            "default-size": unref(size),
            "columns-is-change": columnsIsChange.value,
            "title-bar": _ctx.titleBar,
            onClickDensity: handleClickDensity,
            onFilterTable: handleFilterTableConfirm,
            onRefresh: handleRefresh
          }, createSlots({
            title: withCtx(() => [
              renderSlot(_ctx.$slots, "title")
            ]),
            toolbar: withCtx(() => [
              renderSlot(_ctx.$slots, "toolbar")
            ]),
            _: 2
            /* DYNAMIC */
          }, [
            _ctx.$slots["drag-sort-icon"] ? {
              name: "drag-sort-icon",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "drag-sort-icon")
              ]),
              key: "0"
            } : void 0,
            _ctx.$slots["column-settings-icon"] ? {
              name: "column-settings-icon",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "column-settings-icon")
              ]),
              key: "1"
            } : void 0,
            _ctx.$slots["density-icon"] ? {
              name: "density-icon",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "density-icon")
              ]),
              key: "2"
            } : void 0
          ]), 1032, ["columns", "default-size", "columns-is-change", "title-bar"])) : createCommentVNode("v-if", true),
          withDirectives((openBlock(), createBlock(unref(ElTable), mergeProps({
            ref_key: "tableInstance",
            ref: tableInstance,
            "reserve-selection": true,
            data: __tableData.value,
            border: true,
            height: _ctx.height,
            "header-cell-style": _ctx.headerCellStyle,
            size: unref(size),
            "row-key": _ctx.rowKey,
            "highlight-current-row": "",
            "scrollbar-always-on": ""
          }, _ctx.$attrs, {
            onCellClick: handleClickCell,
            onCellDblclick: handleDoubleClickCell
          }), {
            default: withCtx(() => [
              renderSlot(_ctx.$slots, "default", {}, () => {
                var _a2;
                return [
                  createCommentVNode(" 选择栏 "),
                  _ctx.isSelection ? (openBlock(), createBlock(
                    unref(ElTableColumn),
                    mergeProps({
                      key: "selection",
                      type: "selection"
                    }, _ctx.selectionTableColumnProps),
                    null,
                    16
                    /* FULL_PROPS */
                  )) : createCommentVNode("v-if", true),
                  createCommentVNode(" 序号栏 "),
                  _ctx.hasIndexColumn ? (openBlock(), createBlock(PlusTableTableColumnIndex, {
                    key: 1,
                    "index-content-style": _ctx.indexContentStyle,
                    "index-table-column-props": _ctx.indexTableColumnProps,
                    "page-info": (_a2 = _ctx.pagination) == null ? void 0 : _a2.modelValue
                  }, null, 8, ["index-content-style", "index-table-column-props", "page-info"])) : createCommentVNode("v-if", true),
                  createCommentVNode(" 拖拽行 "),
                  _ctx.dragSortable ? (openBlock(), createBlock(PlusTableColumnDragSort, {
                    key: 2,
                    sortable: _ctx.dragSortable,
                    "drag-sortable-table-column-props": _ctx.dragSortableTableColumnProps,
                    "table-instance": tableInstance.value,
                    onDragSortEnd: handleDragSortEnd
                  }, createSlots({
                    _: 2
                    /* DYNAMIC */
                  }, [
                    _ctx.$slots["drag-sort-icon"] ? {
                      name: "drag-sort-icon",
                      fn: withCtx(() => [
                        renderSlot(_ctx.$slots, "drag-sort-icon")
                      ]),
                      key: "0"
                    } : void 0
                  ]), 1032, ["sortable", "drag-sortable-table-column-props", "table-instance"])) : createCommentVNode("v-if", true),
                  createCommentVNode(" 展开行 "),
                  _ctx.hasExpand ? (openBlock(), createBlock(
                    unref(ElTableColumn),
                    mergeProps({
                      key: 3,
                      type: "expand"
                    }, _ctx.expandTableColumnProps),
                    {
                      default: withCtx((scoped) => [
                        createBaseVNode("div", _hoisted_113, [
                          renderSlot(_ctx.$slots, "expand", mergeProps({
                            index: scoped.$index
                          }, scoped))
                        ])
                      ]),
                      _: 3
                      /* FORWARDED */
                    },
                    16
                    /* FULL_PROPS */
                  )) : createCommentVNode("v-if", true),
                  createCommentVNode("配置渲染栏  "),
                  createVNode(PlusTableColumn, {
                    columns: subColumns.value,
                    editable: _ctx.editable,
                    onFormChange: handleFormChange
                  }, createSlots({
                    _: 2
                    /* DYNAMIC */
                  }, [
                    renderList(unref(headerSlots), (_, key) => {
                      return {
                        name: key,
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                        ])
                      };
                    }),
                    renderList(unref(cellSlots), (_, key) => {
                      return {
                        name: key,
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                        ])
                      };
                    }),
                    renderList(unref(fieldSlots), (_, key) => {
                      return {
                        name: key,
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                        ])
                      };
                    }),
                    renderList(unref(extraSlots), (_, key) => {
                      return {
                        name: key,
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                        ])
                      };
                    }),
                    _ctx.$slots["tooltip-icon"] ? {
                      name: "tooltip-icon",
                      fn: withCtx(() => [
                        renderSlot(_ctx.$slots, "tooltip-icon")
                      ]),
                      key: "0"
                    } : void 0,
                    _ctx.$slots["edit-icon"] ? {
                      name: "edit-icon",
                      fn: withCtx(() => [
                        renderSlot(_ctx.$slots, "edit-icon")
                      ]),
                      key: "1"
                    } : void 0
                  ]), 1032, ["columns", "editable"]),
                  createCommentVNode(" 操作栏 "),
                  _ctx.actionBar ? (openBlock(), createBlock(
                    PlusTableActionBar,
                    mergeProps({ key: 4 }, _ctx.actionBar, {
                      onClickAction: handleAction,
                      onClickActionConfirmCancel: handleClickActionConfirmCancel
                    }),
                    createSlots({
                      _: 2
                      /* DYNAMIC */
                    }, [
                      _ctx.$slots["action-bar-more-icon"] ? {
                        name: "action-bar-more-icon",
                        fn: withCtx(() => [
                          renderSlot(_ctx.$slots, "action-bar-more-icon")
                        ]),
                        key: "0"
                      } : void 0
                    ]),
                    1040
                    /* FULL_PROPS, DYNAMIC_SLOTS */
                  )) : createCommentVNode("v-if", true)
                ];
              })
            ]),
            append: withCtx(() => [
              renderSlot(_ctx.$slots, "append")
            ]),
            empty: withCtx(() => [
              renderSlot(_ctx.$slots, "empty")
            ]),
            _: 3
            /* FORWARDED */
          }, 16, ["data", "height", "header-cell-style", "size", "row-key"])), [
            [unref(vLoading), _ctx.loadingStatus]
          ]),
          createCommentVNode(" 分页 "),
          _ctx.pagination ? (openBlock(), createBlock(unref(PlusPagination), mergeProps({
            key: 1,
            modelValue: unref(subPageInfo),
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => isRef(subPageInfo) ? subPageInfo.value = $event : null)
          }, _ctx.pagination, { onChange: handlePaginationChange }), createSlots({
            _: 2
            /* DYNAMIC */
          }, [
            _ctx.$slots["pagination-left"] ? {
              name: "pagination-left",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "pagination-left")
              ]),
              key: "0"
            } : void 0,
            _ctx.$slots["pagination-right"] ? {
              name: "pagination-right",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "pagination-right")
              ]),
              key: "1"
            } : void 0
          ]), 1040, ["modelValue"])) : createCommentVNode("v-if", true)
        ],
        512
        /* NEED_PATCH */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/src/index.vue.mjs
var _Table = _export_sfc(_sfc_main18, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/table/index.mjs
var PlusTable = _Table;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/descriptions/src/index.vue2.mjs
var _sfc_main19 = defineComponent({
  ...{
    name: "PlusDescriptions"
  },
  __name: "index",
  props: {
    data: { default: () => ({}) },
    columns: { default: () => [] },
    column: { default: 3 },
    title: { default: "" }
  },
  setup(__props) {
    const props = __props;
    const subColumns = computed(
      () => props.columns.filter((item) => unref(item.hideInDescriptions) !== true)
    );
    const getDisplayValue = (prop) => getValue(props.data, prop);
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElDescriptions), mergeProps({
        title: _ctx.title,
        column: _ctx.column,
        class: "plus-description",
        border: ""
      }, _ctx.$attrs), {
        title: withCtx(() => [
          renderSlot(_ctx.$slots, "title")
        ]),
        extra: withCtx(() => [
          renderSlot(_ctx.$slots, "extra")
        ]),
        default: withCtx(() => [
          renderSlot(_ctx.$slots, "default", {}, () => [
            (openBlock(true), createElementBlock(
              Fragment,
              null,
              renderList(subColumns.value, (item) => {
                var _a, _b;
                return openBlock(), createBlock(unref(ElDescriptionsItem), mergeProps({
                  key: item.prop,
                  label: unref(getLabel)(item.label),
                  "class-name": (((_a = item.descriptionsItemProps) == null ? void 0 : _a.className) || "") + " plus-description__name",
                  "label-class-name": (((_b = item.descriptionsItemProps) == null ? void 0 : _b.labelClassName) || "") + " plus-description__label"
                }, item.descriptionsItemProps), {
                  label: withCtx(() => [
                    item.renderDescriptionsLabel && unref(isFunction)(item.renderDescriptionsLabel) ? (openBlock(), createBlock(resolveDynamicComponent(item.renderDescriptionsLabel), {
                      key: 0,
                      label: unref(getLabel)(item.label),
                      column: item,
                      row: _ctx.data
                    }, null, 8, ["label", "column", "row"])) : _ctx.$slots[unref(getDescLabelSlotName)(item.prop)] ? (openBlock(), createElementBlock(
                      Fragment,
                      { key: 1 },
                      [
                        createCommentVNode(" plus-desc-label-* "),
                        renderSlot(_ctx.$slots, unref(getDescLabelSlotName)(item.prop), {
                          column: item,
                          row: _ctx.data,
                          label: unref(getLabel)(item.label)
                        })
                      ],
                      64
                      /* STABLE_FRAGMENT */
                    )) : (openBlock(), createElementBlock(
                      Fragment,
                      { key: 2 },
                      [
                        createCommentVNode(" normal "),
                        createTextVNode(
                          toDisplayString(unref(getLabel)(item.label)),
                          1
                          /* TEXT */
                        )
                      ],
                      64
                      /* STABLE_FRAGMENT */
                    ))
                  ]),
                  default: withCtx(() => [
                    item.renderDescriptionsItem && unref(isFunction)(item.renderDescriptionsItem) ? (openBlock(), createBlock(resolveDynamicComponent(item.renderDescriptionsItem), {
                      key: 0,
                      value: getDisplayValue(item.prop),
                      column: item,
                      row: _ctx.data
                    }, null, 8, ["value", "column", "row"])) : _ctx.$slots[unref(getDescSlotName)(item.prop)] ? (openBlock(), createElementBlock(
                      Fragment,
                      { key: 1 },
                      [
                        createCommentVNode(" plus-desc-* "),
                        renderSlot(_ctx.$slots, unref(getDescSlotName)(item.prop), {
                          column: item,
                          row: _ctx.data,
                          value: getDisplayValue(item.prop)
                        })
                      ],
                      64
                      /* STABLE_FRAGMENT */
                    )) : (openBlock(), createElementBlock(
                      Fragment,
                      { key: 2 },
                      [
                        createCommentVNode(" normal "),
                        createVNode(unref(PlusDisplayItem), {
                          column: item,
                          row: _ctx.data
                        }, null, 8, ["column", "row"])
                      ],
                      64
                      /* STABLE_FRAGMENT */
                    ))
                  ]),
                  _: 2
                  /* DYNAMIC */
                }, 1040, ["label", "class-name", "label-class-name"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ])
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["title", "column"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/descriptions/src/index.vue.mjs
var Descriptions = _export_sfc(_sfc_main19, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/descriptions/index.mjs
var PlusDescriptions = Descriptions;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/search/src/index.vue2.mjs
var _sfc_main20 = defineComponent({
  ...{
    name: "PlusSearch"
  },
  __name: "index",
  props: {
    modelValue: { default: () => ({}) },
    defaultValues: { default: () => ({}) },
    columns: { default: () => [] },
    hasFooter: { type: Boolean, default: true },
    hasReset: { type: Boolean, default: true },
    hasUnfold: { type: Boolean, default: true },
    searchText: { default: "" },
    resetText: { default: "" },
    retractText: { default: "" },
    expandText: { default: "" },
    searchLoading: { type: Boolean, default: false },
    inline: { type: Boolean, default: true },
    showNumber: { default: 2 },
    labelPosition: { default: void 0 },
    rowProps: { default: () => ({
      gutter: 20
    }) },
    colProps: { default: () => ({
      xs: 24,
      sm: 12,
      md: 8,
      lg: 8,
      xl: 6
    }) },
    needValidate: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "search", "change", "reset", "collapse"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const plusFormInstance = ref();
    const isShowUnfold = ref(false);
    const values = ref({});
    const slots = useSlots();
    const labelSlots = filterSlots(slots, getLabelSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const extraSlots = filterSlots(slots, getExtraSlotName());
    const originData = computed(() => {
      const filterData = props.columns.filter((item) => unref(item.hideInSearch) !== true).map((item) => ({ ...item, hideInForm: false })).map((item) => ({ ...item, order: (item == null ? void 0 : item.order) ? unref(item.order) : 0 }));
      return orderBy_default(filterData, ["order"], ["desc"]);
    });
    const subColumns = computed(() => {
      if (props.hasUnfold && !isShowUnfold.value) {
        return originData.value.slice(0, props.showNumber);
      } else {
        return originData.value;
      }
    });
    watch(
      () => props.modelValue,
      (val) => {
        values.value = val;
      },
      {
        immediate: true
      }
    );
    const handleChange = async (values2, column) => {
      emit("update:modelValue", values2);
      emit("change", values2, column);
    };
    const handleSearchDefault = () => {
      emit("search", values.value);
    };
    const handleSearchValidate = async () => {
      var _a;
      const isValid = await ((_a = plusFormInstance.value) == null ? void 0 : _a.handleSubmit());
      if (isValid) {
        emit("search", values.value);
      }
    };
    const handleSearch = computed(
      () => props.needValidate ? handleSearchValidate : handleSearchDefault
    );
    const handleReset = () => {
      values.value = { ...props.defaultValues };
      emit("update:modelValue", values.value);
      emit("reset", values.value);
    };
    const handleUnfold = (e) => {
      e.preventDefault();
      isShowUnfold.value = !isShowUnfold.value;
      emit("collapse", isShowUnfold.value);
    };
    __expose({
      plusFormInstance,
      handleReset,
      handleSearch: handleSearch.value,
      handleUnfold
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(PlusForm), mergeProps({
        ref_key: "plusFormInstance",
        ref: plusFormInstance
      }, _ctx.$attrs, {
        modelValue: values.value,
        "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => values.value = $event),
        inline: _ctx.inline,
        "label-position": _ctx.labelPosition,
        "row-props": _ctx.rowProps,
        "col-props": _ctx.colProps,
        columns: subColumns.value,
        class: "plus-search",
        "has-footer": false,
        onChange: handleChange
      }), createSlots({
        "search-footer": withCtx(() => [
          _ctx.hasFooter ? (openBlock(), createBlock(unref(ElFormItem), {
            key: 0,
            class: "plus-search__button__wrapper",
            label: _ctx.labelPosition === "top" ? "placeholder" : ""
          }, {
            default: withCtx(() => [
              renderSlot(_ctx.$slots, "footer", {
                isShowUnfold: isShowUnfold.value,
                handleReset,
                handleSearch: handleSearch.value,
                handleUnfold
              }, () => [
                _ctx.hasReset ? (openBlock(), createBlock(unref(ElButton), {
                  key: 0,
                  icon: unref(refresh_right_default),
                  onClick: handleReset
                }, {
                  default: withCtx(() => [
                    createTextVNode(
                      toDisplayString(_ctx.resetText || unref(t)("plus.search.resetText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                }, 8, ["icon"])) : createCommentVNode("v-if", true),
                createVNode(unref(ElButton), {
                  type: "primary",
                  loading: _ctx.searchLoading,
                  icon: unref(search_default),
                  onClick: handleSearch.value
                }, {
                  default: withCtx(() => [
                    createTextVNode(
                      toDisplayString(_ctx.searchText || unref(t)("plus.search.searchText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                }, 8, ["loading", "icon", "onClick"]),
                _ctx.hasUnfold && originData.value.length > _ctx.showNumber ? (openBlock(), createBlock(unref(ElLink), {
                  key: 1,
                  class: "plus-search__unfold",
                  type: "primary",
                  underline: false,
                  href: "javaScript:;",
                  onClick: handleUnfold
                }, {
                  default: withCtx(() => [
                    createTextVNode(
                      toDisplayString(isShowUnfold.value ? _ctx.retractText || unref(t)("plus.search.retract") : _ctx.expandText || unref(t)("plus.search.expand")) + " ",
                      1
                      /* TEXT */
                    ),
                    createVNode(unref(ElIcon), null, {
                      default: withCtx(() => [
                        isShowUnfold.value ? (openBlock(), createBlock(unref(arrow_up_default), { key: 0 })) : (openBlock(), createBlock(unref(arrow_down_default), { key: 1 }))
                      ]),
                      _: 1
                      /* STABLE */
                    })
                  ]),
                  _: 1
                  /* STABLE */
                })) : createCommentVNode("v-if", true)
              ])
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["label"])) : createCommentVNode("v-if", true)
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        renderList(unref(labelSlots), (_, key) => {
          return {
            name: key,
            fn: withCtx((data) => [
              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
            ])
          };
        }),
        renderList(unref(fieldSlots), (_, key) => {
          return {
            name: key,
            fn: withCtx((data) => [
              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
            ])
          };
        }),
        renderList(unref(extraSlots), (_, key) => {
          return {
            name: key,
            fn: withCtx((data) => [
              renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
            ])
          };
        }),
        _ctx.$slots["tooltip-icon"] ? {
          name: "tooltip-icon",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "tooltip-icon")
          ]),
          key: "0"
        } : void 0
      ]), 1040, ["modelValue", "inline", "label-position", "row-props", "col-props", "columns"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/search/src/index.vue.mjs
var Search = _export_sfc(_sfc_main20, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/search/index.mjs
var PlusSearch = Search;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog-form/src/index.vue2.mjs
var _sfc_main21 = defineComponent({
  ...{
    name: "PlusDialogForm"
  },
  __name: "index",
  props: {
    modelValue: { default: () => ({}) },
    visible: { type: Boolean, default: false },
    dialog: { default: () => ({}) },
    form: { default: () => ({}) },
    hasErrorTip: { type: Boolean, default: true }
  },
  emits: ["update:modelValue", "update:visible", "confirm", "change", "cancel", "confirmError"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const formInstance = ref();
    const computedFormInstance = computed(() => {
      var _a;
      return (_a = formInstance.value) == null ? void 0 : _a.formInstance;
    });
    const state = ref({});
    const subVisible = ref(false);
    const slots = useSlots();
    const labelSlots = filterSlots(slots, getLabelSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const extraSlots = filterSlots(slots, getExtraSlotName());
    watch(
      () => props.visible,
      (val) => {
        subVisible.value = val;
      },
      {
        immediate: true
      }
    );
    watch(
      () => props.modelValue,
      (val) => {
        state.value = val;
      },
      {
        immediate: true
      }
    );
    const handleChange = (values, column) => {
      emit("update:modelValue", values);
      emit("change", values, column);
    };
    const handleConfirm = async () => {
      var _a, _b, _c;
      try {
        const valid = await ((_a = computedFormInstance.value) == null ? void 0 : _a.validate());
        if (valid) {
          emit("confirm", state.value);
        }
      } catch (errors) {
        if (props.hasErrorTip) {
          ElMessage.closeAll();
          const values = isPlainObject(errors) && Object.values(errors);
          const message = values ? (_c = (_b = values[0]) == null ? void 0 : _b[0]) == null ? void 0 : _c.message : void 0;
          ElMessage.warning(message || t("plus.form.errorTip"));
        }
        emit("confirmError", errors);
      }
    };
    const handleCancel = () => {
      subVisible.value = false;
      emit("update:visible", subVisible.value);
      emit("cancel");
    };
    __expose({
      handleConfirm,
      handleCancel,
      formInstance: computedFormInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(PlusDialog), mergeProps({
        modelValue: subVisible.value,
        "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => subVisible.value = $event),
        width: "800px",
        top: "10vh",
        title: unref(t)("plus.dialogForm.title")
      }, _ctx.dialog, {
        onCancel: handleCancel,
        onConfirm: handleConfirm
      }), createSlots({
        default: withCtx(() => [
          createVNode(unref(PlusForm), mergeProps({
            ref_key: "formInstance",
            ref: formInstance,
            modelValue: state.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.value = $event),
            "has-footer": false,
            "footer-align": "right"
          }, _ctx.form, { onChange: handleChange }), createSlots({
            _: 2
            /* DYNAMIC */
          }, [
            _ctx.$slots["form-footer"] ? {
              name: "footer",
              fn: withCtx((data) => [
                renderSlot(_ctx.$slots, "form-footer", normalizeProps(guardReactiveProps(data)))
              ]),
              key: "0"
            } : void 0,
            _ctx.$slots["form-group-header"] ? {
              name: "group-header",
              fn: withCtx((data) => [
                renderSlot(_ctx.$slots, "form-group-header", normalizeProps(guardReactiveProps(data)))
              ]),
              key: "1"
            } : void 0,
            renderList(unref(labelSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            renderList(unref(fieldSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            renderList(unref(extraSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            _ctx.$slots["tooltip-icon"] ? {
              name: "tooltip-icon",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "tooltip-icon")
              ]),
              key: "2"
            } : void 0
          ]), 1040, ["modelValue"])
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        _ctx.$slots["dialog-header"] ? {
          name: "header",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "dialog-header")
          ]),
          key: "0"
        } : void 0,
        _ctx.$slots["dialog-footer"] ? {
          name: "footer",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "dialog-footer", normalizeProps(guardReactiveProps({ handleConfirm, handleCancel })))
          ]),
          key: "1"
        } : void 0
      ]), 1040, ["modelValue", "title"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog-form/src/index.vue.mjs
var DialogForm = _export_sfc(_sfc_main21, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/dialog-form/index.mjs
var PlusDialogForm = DialogForm;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/drawer-form/src/index.vue2.mjs
var _hoisted_114 = { class: "plus-drawer-form__footer" };
var _sfc_main22 = defineComponent({
  ...{
    name: "PlusDrawerForm"
  },
  __name: "index",
  props: {
    modelValue: { default: () => ({}) },
    visible: { type: Boolean, default: false },
    drawer: { default: () => ({}) },
    size: { default: "540px" },
    form: { default: () => ({}) },
    hasFooter: { type: Boolean, default: true },
    cancelText: { default: "" },
    confirmText: { default: "" },
    confirmLoading: { type: Boolean, default: false },
    hasErrorTip: { type: Boolean, default: true }
  },
  emits: ["update:modelValue", "update:visible", "confirm", "change", "cancel", "confirmError"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const formInstance = ref(null);
    const computedFormInstance = computed(() => {
      var _a;
      return (_a = formInstance.value) == null ? void 0 : _a.formInstance;
    });
    const drawerInstance = ref();
    const state = ref({});
    const subVisible = ref(false);
    const slots = useSlots();
    const labelSlots = filterSlots(slots, getLabelSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const extraSlots = filterSlots(slots, getExtraSlotName());
    watch(
      () => props.visible,
      (val) => {
        subVisible.value = val;
      },
      {
        immediate: true
      }
    );
    watch(
      () => props.modelValue,
      (val) => {
        state.value = val;
      },
      {
        immediate: true
      }
    );
    const handleChange = (values, column) => {
      emit("update:modelValue", values);
      emit("change", values, column);
    };
    const handleConfirm = async () => {
      var _a, _b, _c;
      try {
        const valid = await ((_a = computedFormInstance.value) == null ? void 0 : _a.validate());
        if (valid) {
          emit("confirm", state.value);
        }
      } catch (errors) {
        if (props.hasErrorTip) {
          ElMessage.closeAll();
          const values = isPlainObject(errors) && Object.values(errors);
          const message = values ? (_c = (_b = values[0]) == null ? void 0 : _b[0]) == null ? void 0 : _c.message : void 0;
          ElMessage.warning(message || t("plus.form.errorTip"));
        }
        emit("confirmError", errors);
      }
    };
    const handleCancel = () => {
      subVisible.value = false;
      emit("update:visible", subVisible.value);
      emit("cancel");
    };
    __expose({
      drawerInstance,
      formInstance: computedFormInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElDrawer), mergeProps({
        ref_key: "drawerInstance",
        ref: drawerInstance,
        modelValue: subVisible.value,
        "onUpdate:modelValue": _cache[1] || (_cache[1] = ($event) => subVisible.value = $event),
        class: "plus-drawer-form",
        size: _ctx.size || "540px",
        title: unref(t)("plus.drawerForm.title"),
        "close-on-click-modal": false,
        "close-on-press-escape": false
      }, _ctx.$attrs, { onClose: handleCancel }), createSlots({
        default: withCtx(() => [
          createVNode(unref(PlusForm), mergeProps({
            ref_key: "formInstance",
            ref: formInstance,
            modelValue: state.value,
            "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => state.value = $event),
            "has-footer": false
          }, _ctx.form, { onChange: handleChange }), createSlots({
            _: 2
            /* DYNAMIC */
          }, [
            _ctx.$slots["form-footer"] ? {
              name: "footer",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "form-footer")
              ]),
              key: "0"
            } : void 0,
            _ctx.$slots["form-group-header"] ? {
              name: "group-header",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "form-group-header")
              ]),
              key: "1"
            } : void 0,
            renderList(unref(labelSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            renderList(unref(fieldSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            renderList(unref(extraSlots), (_, key) => {
              return {
                name: key,
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                ])
              };
            }),
            _ctx.$slots["tooltip-icon"] ? {
              name: "tooltip-icon",
              fn: withCtx(() => [
                renderSlot(_ctx.$slots, "tooltip-icon")
              ]),
              key: "2"
            } : void 0
          ]), 1040, ["modelValue"])
        ]),
        _: 2
        /* DYNAMIC */
      }, [
        _ctx.$slots["drawer-header"] ? {
          name: "header",
          fn: withCtx(() => [
            renderSlot(_ctx.$slots, "drawer-header")
          ]),
          key: "0"
        } : void 0,
        _ctx.hasFooter ? {
          name: "footer",
          fn: withCtx(() => [
            createBaseVNode("div", _hoisted_114, [
              renderSlot(_ctx.$slots, "drawer-footer", normalizeProps(guardReactiveProps({ handleConfirm, handleCancel })), () => [
                createVNode(unref(ElButton), { onClick: handleCancel }, {
                  default: withCtx(() => [
                    createTextVNode(
                      toDisplayString(_ctx.cancelText || unref(t)("plus.drawerForm.cancelText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                }),
                createVNode(unref(ElButton), {
                  type: "primary",
                  loading: _ctx.confirmLoading,
                  onClick: handleConfirm
                }, {
                  default: withCtx(() => [
                    createTextVNode(
                      toDisplayString(_ctx.confirmText || unref(t)("plus.drawerForm.confirmText")),
                      1
                      /* TEXT */
                    )
                  ]),
                  _: 1
                  /* STABLE */
                }, 8, ["loading"])
              ])
            ])
          ]),
          key: "1"
        } : void 0
      ]), 1040, ["modelValue", "size", "title"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/drawer-form/src/index.vue.mjs
var DrawerForm = _export_sfc(_sfc_main22, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/drawer-form/index.mjs
var PlusDrawerForm = DrawerForm;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/page/src/index.vue2.mjs
var _hoisted_115 = { class: "plus-page" };
var _sfc_main23 = defineComponent({
  ...{
    name: "PlusPage"
  },
  __name: "index",
  props: {
    columns: { default: () => [] },
    request: {},
    search: { type: [Boolean, Object], default: () => ({}) },
    table: { default: () => ({}) },
    params: { default: () => ({}) },
    postData: { type: Function, default: void 0 },
    beforeSearchSubmit: { type: Function, default: void 0 },
    isCard: { type: Boolean, default: true },
    searchCardProps: { default: () => ({}) },
    tableCardProps: { default: () => ({}) },
    defaultPageInfo: { default: () => ({ ...DefaultPageInfo }) },
    defaultPageSizeList: { default: () => DefaultPageSizeList },
    pagination: { type: [Boolean, Object], default: () => ({}) },
    immediate: { type: Boolean, default: true },
    dividerProps: { type: [Boolean, Object], default: false },
    pageInfoMap: { default: () => ({
      page: "page",
      pageSize: "pageSize"
    }) }
  },
  emits: ["search", "reset", "paginationChange", "requestError", "requestComplete"],
  setup(__props, { expose: __expose, emit: __emit }) {
    var _a;
    const props = __props;
    const emit = __emit;
    const slots = useSlots();
    const computedDefaultPageInfo = computed(() => props.defaultPageInfo);
    const computedDefaultPageSizeList = computed(() => props.defaultPageSizeList);
    const { tableData, pageInfo, total, loadingStatus } = useTable(computedDefaultPageInfo);
    const plusSearchInstance = ref(null);
    const plusTableInstance = ref(null);
    const values = ref({ ...(_a = props.search) == null ? void 0 : _a.defaultValues });
    const cellSlots = filterSlots(slots, getTableCellSlotName());
    const headerSlots = filterSlots(slots, getTableHeaderSlotName());
    const fieldSlots = filterSlots(slots, getFieldSlotName());
    const renderWrapper = () => {
      if (props.isCard) {
        return {
          search: h(ElCard, props.searchCardProps),
          table: h(ElCard, props.tableCardProps)
        };
      }
      return { search: h("div"), table: h("div") };
    };
    const getList = async () => {
      var _a2, _b;
      if (!props.request)
        return;
      try {
        loadingStatus.value = true;
        const payload = {
          ...values.value,
          // eslint-disabled no-useless-spread
          ...{
            [((_a2 = props.pageInfoMap) == null ? void 0 : _a2.page) || "page"]: pageInfo.value.page,
            [((_b = props.pageInfoMap) == null ? void 0 : _b.pageSize) || "pageSize"]: pageInfo.value.pageSize
          },
          ...props.params
        };
        const { data, total: dataTotal } = await props.request(payload);
        const list = props.postData && props.postData(data) || data;
        tableData.value = list || [];
        total.value = dataTotal || list.length;
        emit("requestComplete", tableData.value);
      } catch (error) {
        emit("requestError", error);
      }
      loadingStatus.value = false;
    };
    if (props.immediate) {
      getList();
    }
    const handlePaginationChange = (_pageInfo) => {
      pageInfo.value = _pageInfo;
      getList();
      emit("paginationChange", _pageInfo);
    };
    const handleSearch = (val) => {
      const data = props.beforeSearchSubmit && props.beforeSearchSubmit(val) || val;
      values.value = data;
      pageInfo.value.page = 1;
      getList();
      emit("search", values.value);
    };
    const handleReset = (val) => {
      values.value = { ...val };
      pageInfo.value.page = 1;
      getList();
      emit("reset", values.value);
    };
    const handleRefresh = () => {
      getList();
    };
    __expose({
      plusSearchInstance,
      plusTableInstance,
      getList,
      handleReset,
      /**
       * TODO: 将会在v0.2.0中移除
       */
      handleRest: handleReset
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_115, [
        _ctx.search ? (openBlock(), createBlock(resolveDynamicComponent(renderWrapper().search), { key: 0 }, {
          default: withCtx(() => [
            createVNode(unref(PlusSearch), mergeProps({
              ref_key: "plusSearchInstance",
              ref: plusSearchInstance
            }, _ctx.search, {
              modelValue: values.value,
              "onUpdate:modelValue": _cache[0] || (_cache[0] = ($event) => values.value = $event),
              columns: _ctx.columns,
              "search-loading": unref(loadingStatus),
              onSearch: handleSearch,
              onReset: handleReset
            }), createSlots({
              _: 2
              /* DYNAMIC */
            }, [
              _ctx.$slots["search-footer"] ? {
                name: "footer",
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, "search-footer", normalizeProps(guardReactiveProps(data)))
                ]),
                key: "0"
              } : void 0
            ]), 1040, ["modelValue", "columns", "search-loading"])
          ]),
          _: 3
          /* FORWARDED */
        })) : createCommentVNode("v-if", true),
        _ctx.dividerProps ? (openBlock(), createBlock(
          unref(ElDivider),
          normalizeProps(mergeProps({ key: 1 }, _ctx.dividerProps)),
          null,
          16
          /* FULL_PROPS */
        )) : createCommentVNode("v-if", true),
        renderSlot(_ctx.$slots, "extra"),
        (openBlock(), createBlock(resolveDynamicComponent(renderWrapper().table), { class: "plus-page__table_wrapper" }, {
          default: withCtx(() => [
            createVNode(unref(PlusTable), mergeProps({
              ref_key: "plusTableInstance",
              ref: plusTableInstance,
              "title-bar": { refresh: true }
            }, _ctx.table, {
              "table-data": unref(tableData),
              "loading-status": unref(loadingStatus),
              columns: _ctx.columns,
              pagination: _ctx.pagination === false ? void 0 : {
                ..._ctx.pagination,
                total: unref(total),
                modelValue: unref(pageInfo),
                pageSizeList: computedDefaultPageSizeList.value
              },
              onPaginationChange: handlePaginationChange,
              onRefresh: handleRefresh
            }), createSlots({
              _: 2
              /* DYNAMIC */
            }, [
              renderList(unref(headerSlots), (_, key) => {
                return {
                  name: key,
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                  ])
                };
              }),
              renderList(unref(cellSlots), (_, key) => {
                return {
                  name: key,
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                  ])
                };
              }),
              renderList(unref(fieldSlots), (_, key) => {
                return {
                  name: key,
                  fn: withCtx((data) => [
                    renderSlot(_ctx.$slots, key, normalizeProps(guardReactiveProps(data)))
                  ])
                };
              }),
              _ctx.$slots["table-title"] ? {
                name: "title",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "table-title")
                ]),
                key: "0"
              } : void 0,
              _ctx.$slots["table-toolbar"] ? {
                name: "toolbar",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "table-toolbar")
                ]),
                key: "1"
              } : void 0,
              _ctx.$slots["table-expand"] ? {
                name: "expand",
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, "table-expand", normalizeProps(guardReactiveProps(data)))
                ]),
                key: "2"
              } : void 0,
              _ctx.$slots["table-append"] ? {
                name: "append",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "table-append")
                ]),
                key: "3"
              } : void 0,
              _ctx.$slots["table-empty"] ? {
                name: "empty",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "table-empty")
                ]),
                key: "4"
              } : void 0,
              _ctx.$slots["pagination-left"] ? {
                name: "pagination-left",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "pagination-left")
                ]),
                key: "5"
              } : void 0,
              _ctx.$slots["pagination-right"] ? {
                name: "pagination-right",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "pagination-right")
                ]),
                key: "6"
              } : void 0,
              _ctx.$slots["drag-sort-icon"] ? {
                name: "drag-sort-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "drag-sort-icon")
                ]),
                key: "7"
              } : void 0,
              _ctx.$slots["column-settings-icon"] ? {
                name: "column-settings-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "column-settings-icon")
                ]),
                key: "8"
              } : void 0,
              _ctx.$slots["density-icon"] ? {
                name: "density-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "density-icon")
                ]),
                key: "9"
              } : void 0,
              _ctx.$slots["tooltip-icon"] ? {
                name: "tooltip-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "tooltip-icon")
                ]),
                key: "10"
              } : void 0,
              _ctx.$slots["action-bar-more-icon"] ? {
                name: "action-bar-more-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "action-bar-more-icon")
                ]),
                key: "11"
              } : void 0,
              _ctx.$slots["edit-icon"] ? {
                name: "edit-icon",
                fn: withCtx(() => [
                  renderSlot(_ctx.$slots, "edit-icon")
                ]),
                key: "12"
              } : void 0
            ]), 1040, ["table-data", "loading-status", "columns", "pagination"])
          ]),
          _: 3
          /* FORWARDED */
        }))
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/page/src/index.vue.mjs
var Page = _export_sfc(_sfc_main23, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/page/index.mjs
var PlusPage = Page;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/steps-form/src/index.vue2.mjs
var _sfc_main24 = defineComponent({
  ...{
    name: "PlusStepsForm"
  },
  __name: "index",
  props: {
    modelValue: { default: 1 },
    data: { default: () => [] },
    submitText: { default: void 0 },
    nextText: { default: void 0 },
    preText: { default: void 0 }
  },
  emits: ["pre", "next", "update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const active = ref();
    watchEffect(() => {
      active.value = props.modelValue;
    });
    const handleChange = (values, column) => {
      emit("change", values, column);
    };
    const pre = () => {
      if (active.value-- > props.data.length + 1)
        active.value = 1;
      emit("update:modelValue", active.value);
      emit("pre", active.value);
    };
    const next = (values) => {
      if (active.value++ > props.data.length - 1)
        active.value = props.data.length;
      emit("update:modelValue", active.value);
      emit("next", active.value, values);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        "div",
        {
          class: normalizeClass(["plus-steps-form", _ctx.$attrs.direction === "vertical" ? "plus-steps-from-vertical" : ""])
        },
        [
          createVNode(unref(ElSteps), mergeProps({
            active: active.value,
            "finish-status": "success"
          }, _ctx.$attrs), {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(
                Fragment,
                null,
                renderList(_ctx.data, (item) => {
                  return openBlock(), createBlock(
                    unref(ElStep),
                    mergeProps({
                      key: item.title
                    }, item),
                    createSlots({
                      _: 2
                      /* DYNAMIC */
                    }, [
                      _ctx.$slots.icon ? {
                        name: "icon",
                        fn: withCtx(() => [
                          renderSlot(_ctx.$slots, "icon", {
                            icon: item.icon,
                            title: item.title,
                            description: item.description
                          })
                        ]),
                        key: "0"
                      } : void 0,
                      _ctx.$slots.title ? {
                        name: "title",
                        fn: withCtx(() => [
                          renderSlot(_ctx.$slots, "title", {
                            icon: item.icon,
                            title: item.title,
                            description: item.description
                          })
                        ]),
                        key: "1"
                      } : void 0,
                      _ctx.$slots.description ? {
                        name: "description",
                        fn: withCtx(() => [
                          renderSlot(_ctx.$slots, "description", {
                            icon: item.icon,
                            title: item.title,
                            description: item.description
                          })
                        ]),
                        key: "2"
                      } : void 0
                    ]),
                    1040
                    /* FULL_PROPS, DYNAMIC_SLOTS */
                  );
                }),
                128
                /* KEYED_FRAGMENT */
              ))
            ]),
            _: 3
            /* FORWARDED */
          }, 16, ["active"]),
          createVNode(unref(PlusForm), mergeProps(_ctx.data[active.value - 1].form, {
            "has-reset": active.value !== 1,
            "submit-text": active.value === _ctx.data.length ? _ctx.submitText || unref(t)("plus.stepsForm.submitText") : _ctx.nextText || unref(t)("plus.stepsForm.nextText"),
            "reset-text": _ctx.preText || unref(t)("plus.stepsForm.preText"),
            onSubmit: next,
            onReset: pre,
            onChange: handleChange
          }), null, 16, ["has-reset", "submit-text", "reset-text"])
        ],
        2
        /* CLASS */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/steps-form/src/index.vue.mjs
var StepsForm = _export_sfc(_sfc_main24, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/steps-form/index.mjs
var PlusStepsForm = StepsForm;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/breadcrumb/src/index.vue2.mjs
var _sfc_main25 = defineComponent({
  ...{
    name: "PlusBreadcrumb"
  },
  __name: "index",
  props: {
    routes: { default: () => [] },
    replace: { type: Boolean, default: false },
    renderTitle: {}
  },
  setup(__props) {
    const props = __props;
    const instance = getCurrentInstance();
    const route = computed(
      () => instance.appContext.config.globalProperties.$route
    );
    const breadcrumbList = ref([]);
    watchEffect(() => {
      var _a;
      const breadcrumb = ((_a = props.routes) == null ? void 0 : _a.length) ? props.routes : route.value ? route.value.matched : [];
      breadcrumbList.value = breadcrumb.filter((item) => {
        var _a2;
        return ((_a2 = item.meta) == null ? void 0 : _a2.hideInBreadcrumb) !== true;
      });
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(
        unref(ElBreadcrumb),
        mergeProps(_ctx.$attrs, { class: "plus-breadcrumb" }),
        {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(
              Fragment,
              null,
              renderList(breadcrumbList.value, (item) => {
                return openBlock(), createBlock(unref(ElBreadcrumbItem), {
                  key: item.path,
                  class: "plus-breadcrumb-item",
                  to: item.redirect || item.path,
                  replace: _ctx.replace
                }, {
                  default: withCtx(() => {
                    var _a;
                    return [
                      _ctx.renderTitle && unref(isFunction)(_ctx.renderTitle) ? (openBlock(), createBlock(
                        resolveDynamicComponent(_ctx.renderTitle),
                        normalizeProps(mergeProps({ key: 0 }, item)),
                        null,
                        16
                        /* FULL_PROPS */
                      )) : _ctx.$slots["breadcrumb-item-title"] ? (openBlock(), createElementBlock(
                        Fragment,
                        { key: 1 },
                        [
                          createCommentVNode(" 面包屑title 插槽 "),
                          renderSlot(_ctx.$slots, "breadcrumb-item-title", normalizeProps(guardReactiveProps(item)))
                        ],
                        2112
                        /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                      )) : (openBlock(), createElementBlock(
                        Fragment,
                        { key: 2 },
                        [
                          createTextVNode(
                            toDisplayString(((_a = item.meta) == null ? void 0 : _a.title) || item.name || item.path),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      ))
                    ];
                  }),
                  _: 2
                  /* DYNAMIC */
                }, 1032, ["to", "replace"]);
              }),
              128
              /* KEYED_FRAGMENT */
            ))
          ]),
          _: 3
          /* FORWARDED */
        },
        16
        /* FULL_PROPS */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/breadcrumb/src/index.vue.mjs
var Breadcrumb = _export_sfc(_sfc_main25, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/breadcrumb/index.mjs
var PlusBreadcrumb = Breadcrumb;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/sidebar/src/sidebar-item.vue2.mjs
var _hoisted_116 = { class: "plus-sidebar__item-title" };
var _hoisted_25 = { class: "plus-sidebar__item-title" };
var _sfc_main26 = defineComponent({
  ...{
    name: "PlusSidebarItem"
  },
  __name: "sidebar-item",
  props: {
    item: {},
    collapse: { type: Boolean, default: false },
    renderMenuItem: { type: Function, default: void 0 },
    renderSubMenuItem: { type: Function, default: void 0 },
    renderTitle: { type: Function, default: void 0 }
  },
  setup(__props) {
    const instance = getCurrentInstance();
    const router = instance.appContext.config.globalProperties.$router;
    const resolveMenuItem = (item) => {
      var _a;
      if (!((_a = item.children) == null ? void 0 : _a.length))
        return true;
      const children = item.children.filter((i) => {
        var _a2;
        return ((_a2 = i.meta) == null ? void 0 : _a2.hideInMenu) !== true;
      });
      if (!children.length) {
        return true;
      }
      return false;
    };
    const replacePath = (path) => path.replace("/http", "http");
    const getIndex = (item) => {
      return item.redirect || item.path;
    };
    const handleClickItem = (item) => {
      if (isUrl(replacePath(item.path))) {
        const url = replacePath(item.path);
        window.open(url);
      } else {
        router && router.push(getIndex(item));
      }
    };
    return (_ctx, _cache) => {
      var _a, _b;
      const _component_PlusSidebarItem = resolveComponent("PlusSidebarItem");
      return ((_a = _ctx.item.meta) == null ? void 0 : _a.hideInMenu) !== true ? (openBlock(), createElementBlock(
        Fragment,
        { key: 0 },
        [
          createCommentVNode(" 没有子菜单的情况 "),
          resolveMenuItem(_ctx.item) ? (openBlock(), createBlock(unref(ElMenuItem), {
            key: getIndex(_ctx.item),
            class: "plus-sidebar__item",
            index: getIndex(_ctx.item),
            disabled: (_b = _ctx.item.meta) == null ? void 0 : _b.disabled,
            onClick: _cache[0] || (_cache[0] = ($event) => handleClickItem(_ctx.item))
          }, {
            title: withCtx(() => {
              var _a2;
              return [
                createBaseVNode("span", _hoisted_116, [
                  _ctx.renderTitle && unref(isFunction)(_ctx.renderTitle) ? (openBlock(), createBlock(
                    resolveDynamicComponent(_ctx.renderTitle),
                    normalizeProps(mergeProps({ key: 0 }, _ctx.item)),
                    null,
                    16
                    /* FULL_PROPS */
                  )) : _ctx.$slots["sidebar-item-title"] ? (openBlock(), createElementBlock(
                    Fragment,
                    { key: 1 },
                    [
                      createCommentVNode(" menu-item title 插槽 "),
                      renderSlot(_ctx.$slots, "sidebar-item-title", normalizeProps(guardReactiveProps(_ctx.item)))
                    ],
                    2112
                    /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                  )) : (openBlock(), createElementBlock(
                    Fragment,
                    { key: 2 },
                    [
                      createTextVNode(
                        toDisplayString(((_a2 = _ctx.item.meta) == null ? void 0 : _a2.title) || _ctx.item.name || _ctx.item.path),
                        1
                        /* TEXT */
                      )
                    ],
                    64
                    /* STABLE_FRAGMENT */
                  ))
                ])
              ];
            }),
            default: withCtx(() => [
              _ctx.renderMenuItem && unref(isFunction)(_ctx.renderMenuItem) ? (openBlock(), createBlock(
                resolveDynamicComponent(_ctx.renderMenuItem),
                normalizeProps(mergeProps({ key: 0 }, _ctx.item)),
                null,
                16
                /* FULL_PROPS */
              )) : _ctx.$slots["sidebar-item"] ? (openBlock(), createElementBlock(
                Fragment,
                { key: 1 },
                [
                  createCommentVNode(" menu-item 插槽 "),
                  renderSlot(_ctx.$slots, "sidebar-item", normalizeProps(guardReactiveProps(_ctx.item)))
                ],
                2112
                /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
              )) : _ctx.item.meta && _ctx.item.meta.icon ? (openBlock(), createBlock(unref(ElIcon), {
                key: 2,
                class: "plus-sidebar__item-icon"
              }, {
                default: withCtx(() => [
                  (openBlock(), createBlock(
                    resolveDynamicComponent(_ctx.item.meta.icon),
                    normalizeProps(guardReactiveProps(_ctx.item)),
                    null,
                    16
                    /* FULL_PROPS */
                  ))
                ]),
                _: 1
                /* STABLE */
              })) : createCommentVNode("v-if", true)
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["index", "disabled"])) : (openBlock(), createElementBlock(
            Fragment,
            { key: 1 },
            [
              createCommentVNode(" 有子菜单的情况 "),
              (openBlock(), createBlock(unref(ElSubMenu), {
                key: getIndex(_ctx.item),
                index: getIndex(_ctx.item),
                class: "plus-sidebar__item-sub"
              }, {
                title: withCtx(() => {
                  var _a2, _b2;
                  return [
                    createCommentVNode(" 自定义显示 "),
                    _ctx.renderSubMenuItem && unref(isFunction)(_ctx.renderSubMenuItem) ? (openBlock(), createBlock(
                      resolveDynamicComponent(_ctx.renderSubMenuItem),
                      normalizeProps(mergeProps({ key: 0 }, _ctx.item)),
                      null,
                      16
                      /* FULL_PROPS */
                    )) : _ctx.$slots["sidebar-sub"] ? (openBlock(), createElementBlock(
                      Fragment,
                      { key: 1 },
                      [
                        createCommentVNode(" sub-menu 插槽 "),
                        renderSlot(_ctx.$slots, "sidebar-sub", normalizeProps(guardReactiveProps(_ctx.item)))
                      ],
                      2112
                      /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                    )) : ((_a2 = _ctx.item.meta) == null ? void 0 : _a2.icon) ? (openBlock(), createBlock(unref(ElIcon), {
                      key: 2,
                      class: "plus-sidebar__item-icon"
                    }, {
                      default: withCtx(() => {
                        var _a3;
                        return [
                          (openBlock(), createBlock(
                            resolveDynamicComponent((_a3 = _ctx.item.meta) == null ? void 0 : _a3.icon),
                            normalizeProps(guardReactiveProps(_ctx.item)),
                            null,
                            16
                            /* FULL_PROPS */
                          ))
                        ];
                      }),
                      _: 1
                      /* STABLE */
                    })) : createCommentVNode("v-if", true),
                    createBaseVNode("span", _hoisted_25, [
                      _ctx.renderTitle && unref(isFunction)(_ctx.renderTitle) ? (openBlock(), createBlock(
                        resolveDynamicComponent(_ctx.renderTitle),
                        normalizeProps(mergeProps({ key: 0 }, _ctx.item)),
                        null,
                        16
                        /* FULL_PROPS */
                      )) : _ctx.$slots["sidebar-item-title"] ? (openBlock(), createElementBlock(
                        Fragment,
                        { key: 1 },
                        [
                          createCommentVNode(" sub-menu title 插槽 "),
                          renderSlot(_ctx.$slots, "sidebar-item-title", normalizeProps(guardReactiveProps(_ctx.item)))
                        ],
                        2112
                        /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
                      )) : (openBlock(), createElementBlock(
                        Fragment,
                        { key: 2 },
                        [
                          createTextVNode(
                            toDisplayString(((_b2 = _ctx.item.meta) == null ? void 0 : _b2.title) || _ctx.item.name || _ctx.item.path),
                            1
                            /* TEXT */
                          )
                        ],
                        64
                        /* STABLE_FRAGMENT */
                      ))
                    ])
                  ];
                }),
                default: withCtx(() => [
                  (openBlock(true), createElementBlock(
                    Fragment,
                    null,
                    renderList(_ctx.item.children, (child) => {
                      return openBlock(), createBlock(_component_PlusSidebarItem, {
                        key: child.path,
                        item: child,
                        collapse: _ctx.collapse,
                        "render-menu-item": _ctx.renderMenuItem,
                        "render-sub-menu-item": _ctx.renderSubMenuItem,
                        "render-title": _ctx.renderTitle
                      }, createSlots({
                        _: 2
                        /* DYNAMIC */
                      }, [
                        _ctx.$slots["sidebar-item"] ? {
                          name: "sidebar-item",
                          fn: withCtx((data) => [
                            renderSlot(_ctx.$slots, "sidebar-item", normalizeProps(guardReactiveProps(data)))
                          ]),
                          key: "0"
                        } : void 0,
                        _ctx.$slots["sidebar-sub"] ? {
                          name: "sidebar-sub",
                          fn: withCtx((data) => [
                            renderSlot(_ctx.$slots, "sidebar-sub", normalizeProps(guardReactiveProps(data)))
                          ]),
                          key: "1"
                        } : void 0,
                        _ctx.$slots["sidebar-item-title"] ? {
                          name: "sidebar-item-title",
                          fn: withCtx((data) => [
                            renderSlot(_ctx.$slots, "sidebar-item-title", normalizeProps(guardReactiveProps(data)))
                          ]),
                          key: "2"
                        } : void 0
                      ]), 1032, ["item", "collapse", "render-menu-item", "render-sub-menu-item", "render-title"]);
                    }),
                    128
                    /* KEYED_FRAGMENT */
                  ))
                ]),
                _: 3
                /* FORWARDED */
              }, 8, ["index"]))
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          ))
        ],
        64
        /* STABLE_FRAGMENT */
      )) : createCommentVNode("v-if", true);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/sidebar/src/sidebar-item.vue.mjs
var SidebarItem = _export_sfc(_sfc_main26, [["__file", "sidebar-item.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/sidebar/src/index.vue2.mjs
var _sfc_main27 = defineComponent({
  ...{
    name: "PlusSidebar"
  },
  __name: "index",
  props: {
    routes: { default: () => [] },
    collapse: { type: Boolean, default: false },
    defaultActive: { default: void 0 },
    renderMenuItem: { type: Function, default: void 0 },
    renderSubMenuItem: { type: Function, default: void 0 },
    renderTitle: { type: Function, default: void 0 },
    renderMenuExtra: { type: Function, default: void 0 },
    scrollbarProps: { default: () => ({}) },
    width: { default: 200 }
  },
  emits: ["update:collapse", "toggleCollapse"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const w = computed(() => isString(props.width) ? props.width : props.width + "px");
    const instance = getCurrentInstance();
    const route = computed(
      () => instance.appContext.config.globalProperties.$route
    );
    const plusSidebarInstance = ref(null);
    const subCollapse = ref(false);
    const subRoutes = computed(
      () => cloneDeep_default(props.routes).sort((a, b) => {
        var _a, _b;
        return (((_a = a.meta) == null ? void 0 : _a.sort) || 0) - (((_b = b.meta) == null ? void 0 : _b.sort) || 0);
      })
    );
    const computedDefaultActive = computed(
      () => {
        var _a, _b, _c, _d;
        return ((_a = route.value) == null ? void 0 : _a.redirectedFrom) && ((_c = (_b = route.value) == null ? void 0 : _b.redirectedFrom) == null ? void 0 : _c.path) || ((_d = route.value) == null ? void 0 : _d.path);
      }
    );
    const subDefaultActive = computed(
      () => unref(props.defaultActive) || computedDefaultActive.value
    );
    const toggleCollapse = () => {
      subCollapse.value = !subCollapse.value;
      emit("update:collapse", subCollapse.value);
      emit("toggleCollapse", subCollapse.value);
    };
    watchEffect(() => {
      subCollapse.value = props.collapse;
    });
    __expose({
      collapse: subCollapse,
      toggleCollapse,
      plusSidebarInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElMenu), mergeProps({
        ref_key: "plusSidebarInstance",
        ref: plusSidebarInstance,
        style: {
          "--plus-sidebar-width": w.value
        },
        mode: "vertical",
        collapse: subCollapse.value,
        "default-active": subDefaultActive.value,
        "collapse-transition": true,
        class: ["plus-sidebar", [_ctx.$attrs.mode === "horizontal" ? "is-horizontal" : "is-vertical"]],
        ellipsis: false,
        "unique-opened": ""
      }, _ctx.$attrs), {
        default: withCtx(() => [
          _ctx.renderMenuExtra && unref(isFunction)(_ctx.renderMenuExtra) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.renderMenuExtra), { key: 0 })) : _ctx.$slots["sidebar-extra"] ? (openBlock(), createElementBlock(
            Fragment,
            { key: 1 },
            [
              createCommentVNode(" 菜单头插槽 "),
              renderSlot(_ctx.$slots, "sidebar-extra")
            ],
            2112
            /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */
          )) : createCommentVNode("v-if", true),
          createVNode(
            unref(ElScrollbar),
            mergeProps({ class: "plus-sidebar__scrollbar" }, _ctx.scrollbarProps),
            {
              default: withCtx(() => [
                createCommentVNode(" 添加递归组件，用来生成多级菜单 "),
                (openBlock(true), createElementBlock(
                  Fragment,
                  null,
                  renderList(subRoutes.value, (item) => {
                    return openBlock(), createBlock(SidebarItem, {
                      key: item.path,
                      item,
                      collapse: subCollapse.value,
                      "render-menu-item": _ctx.renderMenuItem,
                      "render-sub-menu-item": _ctx.renderSubMenuItem,
                      "render-title": _ctx.renderTitle
                    }, createSlots({
                      _: 2
                      /* DYNAMIC */
                    }, [
                      _ctx.$slots["sidebar-item"] ? {
                        name: "sidebar-item",
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, "sidebar-item", normalizeProps(guardReactiveProps(data)))
                        ]),
                        key: "0"
                      } : void 0,
                      _ctx.$slots["sidebar-sub"] ? {
                        name: "sidebar-sub",
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, "sidebar-sub", normalizeProps(guardReactiveProps(data)))
                        ]),
                        key: "1"
                      } : void 0,
                      _ctx.$slots["sidebar-item-title"] ? {
                        name: "sidebar-item-title",
                        fn: withCtx((data) => [
                          renderSlot(_ctx.$slots, "sidebar-item-title", normalizeProps(guardReactiveProps(data)))
                        ]),
                        key: "2"
                      } : void 0
                    ]), 1032, ["item", "collapse", "render-menu-item", "render-sub-menu-item", "render-title"]);
                  }),
                  128
                  /* KEYED_FRAGMENT */
                ))
              ]),
              _: 3
              /* FORWARDED */
            },
            16
            /* FULL_PROPS */
          ),
          _ctx.$attrs.mode !== "horizontal" ? (openBlock(), createBlock(unref(ElMenuItem), {
            key: 2,
            class: normalizeClass(["plus-sidebar__collapse", subCollapse.value ? "is-collapse" : ""]),
            onClick: toggleCollapse
          }, {
            default: withCtx(() => [
              _ctx.collapse ? (openBlock(), createBlock(unref(ElIcon), { key: 0 }, {
                default: withCtx(() => [
                  createVNode(unref(expand_default))
                ]),
                _: 1
                /* STABLE */
              })) : (openBlock(), createBlock(unref(ElIcon), { key: 1 }, {
                default: withCtx(() => [
                  createVNode(unref(fold_default))
                ]),
                _: 1
                /* STABLE */
              }))
            ]),
            _: 1
            /* STABLE */
          }, 8, ["class"])) : createCommentVNode("v-if", true)
        ]),
        _: 3
        /* FORWARDED */
      }, 16, ["style", "collapse", "default-active", "class"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/sidebar/src/index.vue.mjs
var Sidebar = _export_sfc(_sfc_main27, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/sidebar/index.mjs
var PlusSidebar = Sidebar;
var PlusSidebarItem = SidebarItem;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/header/src/index.vue2.mjs
var _hoisted_117 = { class: "plus-header__left" };
var _hoisted_26 = ["src"];
var _hoisted_34 = {
  key: 1,
  class: "plus-header__title"
};
var _hoisted_42 = createBaseVNode(
  "div",
  { class: "plus-header__placeholder" },
  null,
  -1
  /* HOISTED */
);
var _hoisted_52 = { class: "plus-header__right" };
var _hoisted_62 = { class: "plus-header__dropdown-area" };
var _hoisted_72 = ["src"];
var _hoisted_82 = { class: "plus-header__username" };
var _hoisted_9 = {
  key: 0,
  class: "plus-header-placeholder"
};
var _sfc_main28 = defineComponent({
  ...{
    name: "PlusHeader"
  },
  __name: "index",
  props: {
    logo: { default: "https://plus-pro-components.com/logo.png" },
    fixed: { type: Boolean, default: false },
    title: { default: "PlusProComponents" },
    logoutText: { default: "" },
    trigger: { default: "click" },
    userInfo: { default: () => ({}) },
    hasUserInfo: { type: Boolean, default: true },
    dropdownList: { default: () => [] },
    renderHeaderLeft: {},
    renderHeaderRight: {}
  },
  emits: ["clickDropdownItem"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const { t } = useLocale();
    const logoutItem = {
      label: props.logoutText || t("plus.header.logout"),
      value: "logout"
    };
    const handleClickItem = (item) => {
      emit("clickDropdownItem", item);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        Fragment,
        null,
        [
          createVNode(unref(ElHeader), {
            class: normalizeClass(["plus-header", { "is-fixed": _ctx.fixed }])
          }, {
            default: withCtx(() => [
              createBaseVNode("div", _hoisted_117, [
                _ctx.renderHeaderLeft && unref(isFunction)(_ctx.renderHeaderLeft) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.renderHeaderLeft), {
                  key: 0,
                  logo: _ctx.logo,
                  title: _ctx.title
                }, null, 8, ["logo", "title"])) : _ctx.$slots["header-left"] ? renderSlot(_ctx.$slots, "header-left", {
                  key: 1,
                  logo: _ctx.logo,
                  title: _ctx.title
                }) : (openBlock(), createElementBlock(
                  Fragment,
                  { key: 2 },
                  [
                    _ctx.logo ? (openBlock(), createElementBlock("img", {
                      key: 0,
                      src: _ctx.logo,
                      alt: "",
                      class: "plus-header__logo"
                    }, null, 8, _hoisted_26)) : createCommentVNode("v-if", true),
                    _ctx.title ? (openBlock(), createElementBlock(
                      "h2",
                      _hoisted_34,
                      toDisplayString(_ctx.title),
                      1
                      /* TEXT */
                    )) : createCommentVNode("v-if", true)
                  ],
                  64
                  /* STABLE_FRAGMENT */
                ))
              ]),
              _hoisted_42,
              createBaseVNode("div", _hoisted_52, [
                _ctx.renderHeaderRight && unref(isFunction)(_ctx.renderHeaderRight) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.renderHeaderRight), {
                  key: 0,
                  "user-info": _ctx.userInfo,
                  title: _ctx.title
                }, null, 8, ["user-info", "title"])) : _ctx.$slots["header-right"] ? renderSlot(_ctx.$slots, "header-right", {
                  key: 1,
                  userInfo: _ctx.userInfo,
                  title: _ctx.title
                }) : createCommentVNode("v-if", true),
                _ctx.hasUserInfo ? (openBlock(), createBlock(unref(ElDropdown), {
                  key: 2,
                  placement: "bottom-end",
                  trigger: "click"
                }, {
                  dropdown: withCtx(() => [
                    createVNode(unref(ElDropdownMenu), { class: "header-dropdown" }, {
                      default: withCtx(() => [
                        createVNode(unref(ElDropdownItem), {
                          onClick: _cache[0] || (_cache[0] = ($event) => handleClickItem(logoutItem))
                        }, {
                          default: withCtx(() => [
                            createTextVNode(
                              toDisplayString(_ctx.logoutText || unref(t)("plus.header.logout")),
                              1
                              /* TEXT */
                            )
                          ]),
                          _: 1
                          /* STABLE */
                        }),
                        (openBlock(true), createElementBlock(
                          Fragment,
                          null,
                          renderList(_ctx.dropdownList, (item) => {
                            return openBlock(), createBlock(unref(ElDropdownItem), {
                              key: item.value,
                              onClick: ($event) => handleClickItem(item)
                            }, {
                              default: withCtx(() => [
                                createTextVNode(
                                  toDisplayString(item.label),
                                  1
                                  /* TEXT */
                                )
                              ]),
                              _: 2
                              /* DYNAMIC */
                            }, 1032, ["onClick"]);
                          }),
                          128
                          /* KEYED_FRAGMENT */
                        ))
                      ]),
                      _: 1
                      /* STABLE */
                    })
                  ]),
                  default: withCtx(() => [
                    createBaseVNode("span", _hoisted_62, [
                      createCommentVNode(" avatar "),
                      _ctx.userInfo.avatar ? (openBlock(), createElementBlock("img", {
                        key: 0,
                        src: _ctx.userInfo.avatar,
                        alt: "",
                        class: "plus-header__avatar"
                      }, null, 8, _hoisted_72)) : (openBlock(), createBlock(unref(ElIcon), {
                        key: 1,
                        size: 20,
                        class: "plus-header__avatar"
                      }, {
                        default: withCtx(() => [
                          createVNode(unref(user_default))
                        ]),
                        _: 1
                        /* STABLE */
                      })),
                      createCommentVNode(" username "),
                      createBaseVNode(
                        "p",
                        _hoisted_82,
                        toDisplayString(_ctx.userInfo.username || "admin"),
                        1
                        /* TEXT */
                      ),
                      createVNode(unref(ElIcon), { class: "el-icon-caret-bottom el-icon--right" }, {
                        default: withCtx(() => [
                          createVNode(unref(arrow_down_default))
                        ]),
                        _: 1
                        /* STABLE */
                      })
                    ])
                  ]),
                  _: 1
                  /* STABLE */
                })) : createCommentVNode("v-if", true)
              ])
            ]),
            _: 3
            /* FORWARDED */
          }, 8, ["class"]),
          _ctx.fixed ? (openBlock(), createElementBlock("div", _hoisted_9)) : createCommentVNode("v-if", true)
        ],
        64
        /* STABLE_FRAGMENT */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/header/src/index.vue.mjs
var Header = _export_sfc(_sfc_main28, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/header/index.mjs
var PlusHeader = Header;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/layout/src/index.vue2.mjs
var _sfc_main29 = defineComponent({
  ...{
    name: "PlusLayout"
  },
  __name: "index",
  props: {
    hasSidebar: { type: Boolean, default: true },
    hasHeader: { type: Boolean, default: true },
    hasBreadcrumb: { type: Boolean, default: true },
    sidebarProps: { default: void 0 },
    headerProps: { default: void 0 },
    breadcrumbProps: { default: void 0 },
    scrollbarProps: { default: () => ({
      always: true
    }) },
    backtop: { type: [Boolean, Object], default: true }
  },
  setup(__props, { expose: __expose }) {
    const props = __props;
    const PlusBreadcrumb$1 = PlusBreadcrumb;
    const PlusSidebar$1 = PlusSidebar;
    const PlusHeader$1 = PlusHeader;
    const plusSidebarInstance = ref();
    const backtopProps = computed(
      () => isPlainObject(props.backtop) ? props.backtop : {}
    );
    __expose({
      plusSidebarInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createBlock(unref(ElContainer), {
        class: "plus-layout",
        style: normalizeStyle(!_ctx.hasHeader ? "--plus-header-height: 0px" : void 0)
      }, {
        default: withCtx(() => [
          createCommentVNode(" 头部 "),
          _ctx.hasHeader ? (openBlock(), createBlock(
            unref(PlusHeader$1),
            normalizeProps(mergeProps({ key: 0 }, _ctx.headerProps)),
            createSlots({
              _: 2
              /* DYNAMIC */
            }, [
              _ctx.$slots["header-left"] ? {
                name: "header-left",
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, "header-left", normalizeProps(guardReactiveProps(data)))
                ]),
                key: "0"
              } : void 0,
              _ctx.$slots["header-right"] ? {
                name: "header-right",
                fn: withCtx((data) => [
                  renderSlot(_ctx.$slots, "header-right", normalizeProps(guardReactiveProps(data)))
                ]),
                key: "1"
              } : void 0
            ]),
            1040
            /* FULL_PROPS, DYNAMIC_SLOTS */
          )) : createCommentVNode("v-if", true),
          createVNode(unref(ElContainer), null, {
            default: withCtx(() => [
              createCommentVNode(" 侧边栏 "),
              _ctx.hasSidebar ? (openBlock(), createBlock(
                unref(PlusSidebar$1),
                mergeProps({ key: 0 }, _ctx.sidebarProps, {
                  ref_key: "plusSidebarInstance",
                  ref: plusSidebarInstance
                }),
                createSlots({
                  _: 2
                  /* DYNAMIC */
                }, [
                  _ctx.$slots["sidebar-extra"] ? {
                    name: "sidebar-extra",
                    fn: withCtx((data) => [
                      renderSlot(_ctx.$slots, "sidebar-extra", normalizeProps(guardReactiveProps(data)))
                    ]),
                    key: "0"
                  } : void 0,
                  _ctx.$slots["sidebar-item"] ? {
                    name: "sidebar-item",
                    fn: withCtx((data) => [
                      renderSlot(_ctx.$slots, "sidebar-item", normalizeProps(guardReactiveProps(data)))
                    ]),
                    key: "1"
                  } : void 0,
                  _ctx.$slots["sidebar-sub"] ? {
                    name: "sidebar-sub",
                    fn: withCtx((data) => [
                      renderSlot(_ctx.$slots, "sidebar-sub", normalizeProps(guardReactiveProps(data)))
                    ]),
                    key: "2"
                  } : void 0,
                  _ctx.$slots["sidebar-item-title"] ? {
                    name: "sidebar-item-title",
                    fn: withCtx((data) => [
                      renderSlot(_ctx.$slots, "sidebar-item-title", normalizeProps(guardReactiveProps(data)))
                    ]),
                    key: "3"
                  } : void 0
                ]),
                1040
                /* FULL_PROPS, DYNAMIC_SLOTS */
              )) : createCommentVNode("v-if", true),
              createCommentVNode(" 主内容 "),
              createVNode(unref(ElMain), null, {
                default: withCtx(() => [
                  createVNode(
                    unref(ElScrollbar),
                    mergeProps({ class: "plus-layout-main__scrollbar" }, _ctx.scrollbarProps),
                    {
                      default: withCtx(() => [
                        createCommentVNode(" 面包屑上方 "),
                        _ctx.$slots["layout-extra"] ? renderSlot(_ctx.$slots, "layout-extra", { key: 0 }) : createCommentVNode("v-if", true),
                        createCommentVNode(" 面包屑 "),
                        _ctx.hasBreadcrumb ? (openBlock(), createBlock(
                          unref(PlusBreadcrumb$1),
                          normalizeProps(mergeProps({ key: 1 }, _ctx.breadcrumbProps)),
                          createSlots({
                            _: 2
                            /* DYNAMIC */
                          }, [
                            _ctx.$slots["breadcrumb-item-title"] ? {
                              name: "breadcrumb-item-title",
                              fn: withCtx((data) => [
                                renderSlot(_ctx.$slots, "breadcrumb-item-title", normalizeProps(guardReactiveProps(data)))
                              ]),
                              key: "0"
                            } : void 0
                          ]),
                          1040
                          /* FULL_PROPS, DYNAMIC_SLOTS */
                        )) : createCommentVNode("v-if", true),
                        renderSlot(_ctx.$slots, "default"),
                        _ctx.backtop ? (openBlock(), createBlock(
                          unref(ElBacktop),
                          mergeProps({ key: 2 }, backtopProps.value, { target: ".plus-layout .plus-layout-main__scrollbar" }),
                          null,
                          16
                          /* FULL_PROPS */
                        )) : createCommentVNode("v-if", true)
                      ]),
                      _: 3
                      /* FORWARDED */
                    },
                    16
                    /* FULL_PROPS */
                  )
                ]),
                _: 3
                /* FORWARDED */
              })
            ]),
            _: 3
            /* FORWARDED */
          })
        ]),
        _: 3
        /* FORWARDED */
      }, 8, ["style"]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/layout/src/index.vue.mjs
var Layout = _export_sfc(_sfc_main29, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/layout/index.mjs
var PlusLayout = Layout;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card/src/index.vue2.mjs
var _hoisted_118 = { class: "plus-check-card__avatar-wrapper" };
var _hoisted_27 = { class: "plus-check-card__right-content" };
var _hoisted_35 = {
  key: 0,
  class: "plus-check-card__title"
};
var _hoisted_43 = { class: "plus-check-card__title-left" };
var _hoisted_53 = {
  key: 1,
  class: "plus-check-card__description"
};
var _sfc_main30 = defineComponent({
  ...{
    name: "PlusCheckCard"
  },
  __name: "index",
  props: {
    modelValue: { type: Boolean, default: false },
    size: { default: "default" },
    avatar: { type: [String, Function], default: void 0 },
    avatarProps: { default: () => ({}) },
    title: { type: [String, Function], default: void 0 },
    description: { type: [String, Function], default: void 0 },
    disabled: { type: Boolean, default: false },
    extra: { type: Function, default: void 0 }
  },
  emits: ["update:modelValue", "change", "extra"],
  setup(__props, { emit: __emit }) {
    const classDataEnum = {
      large: "plus-check-card--large",
      default: "plus-check-card--default",
      small: "plus-check-card--small"
    };
    const props = __props;
    const emit = __emit;
    const state = reactive({
      checked: false
    });
    watchEffect(() => {
      state.checked = props.modelValue;
    });
    const getClass = () => {
      return props.size ? classDataEnum[props.size] : "plus-check-card--default";
    };
    const handleClick = () => {
      if (props.disabled)
        return;
      state.checked = !state.checked;
      emit("update:modelValue", state.checked);
      emit("change", state.checked);
    };
    const handelExtra = () => {
      if (props.disabled)
        return;
      emit("extra");
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        "div",
        {
          class: normalizeClass(["plus-check-card", [
            getClass(),
            state.checked ? "plus-check-card--checked" : "",
            _ctx.disabled ? "plus-check-card--disabled" : ""
          ]]),
          onClick: handleClick
        },
        [
          createBaseVNode("div", _hoisted_118, [
            unref(isFunction)(_ctx.avatar) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.avatar), {
              key: 0,
              avatar: _ctx.avatar,
              title: _ctx.title,
              description: _ctx.description
            }, null, 8, ["avatar", "title", "description"])) : _ctx.$slots.avatar ? renderSlot(_ctx.$slots, "avatar", {
              key: 1,
              avatar: _ctx.avatar,
              title: _ctx.title,
              description: _ctx.description
            }) : unref(isString)(_ctx.avatar) ? (openBlock(), createBlock(unref(ElAvatar), mergeProps({
              key: 2,
              src: _ctx.avatar
            }, _ctx.avatarProps), null, 16, ["src"])) : createCommentVNode("v-if", true)
          ]),
          createBaseVNode("div", _hoisted_27, [
            _ctx.title || _ctx.$slots.title ? (openBlock(), createElementBlock("div", _hoisted_35, [
              createBaseVNode("div", _hoisted_43, [
                unref(isFunction)(_ctx.title) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.title), {
                  key: 0,
                  avatar: _ctx.avatar,
                  title: _ctx.title,
                  description: _ctx.description
                }, null, 8, ["avatar", "title", "description"])) : _ctx.$slots.title ? renderSlot(_ctx.$slots, "title", {
                  key: 1,
                  title: _ctx.title,
                  avatar: _ctx.avatar,
                  description: _ctx.description
                }) : (openBlock(), createElementBlock(
                  Fragment,
                  { key: 2 },
                  [
                    createTextVNode(
                      toDisplayString(_ctx.title),
                      1
                      /* TEXT */
                    )
                  ],
                  64
                  /* STABLE_FRAGMENT */
                ))
              ]),
              createBaseVNode("div", {
                class: "plus-check-card__title-right",
                onClick: withModifiers(handelExtra, ["stop"])
              }, [
                unref(isFunction)(_ctx.extra) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.extra), {
                  key: 0,
                  avatar: _ctx.avatar,
                  title: _ctx.title,
                  description: _ctx.description
                }, null, 8, ["avatar", "title", "description"])) : _ctx.$slots.extra ? renderSlot(_ctx.$slots, "extra", {
                  key: 1,
                  title: _ctx.title,
                  avatar: _ctx.avatar,
                  description: _ctx.description
                }) : createCommentVNode("v-if", true)
              ])
            ])) : createCommentVNode("v-if", true),
            _ctx.description || _ctx.$slots.description ? (openBlock(), createElementBlock("div", _hoisted_53, [
              unref(isFunction)(_ctx.description) ? (openBlock(), createBlock(resolveDynamicComponent(_ctx.description), {
                key: 0,
                title: _ctx.title,
                avatar: _ctx.avatar,
                description: _ctx.description
              }, null, 8, ["title", "avatar", "description"])) : _ctx.$slots.description ? renderSlot(_ctx.$slots, "description", {
                key: 1,
                title: _ctx.title,
                description: _ctx.description,
                avatar: _ctx.avatar
              }) : (openBlock(), createElementBlock(
                Fragment,
                { key: 2 },
                [
                  createTextVNode(
                    toDisplayString(_ctx.description),
                    1
                    /* TEXT */
                  )
                ],
                64
                /* STABLE_FRAGMENT */
              ))
            ])) : createCommentVNode("v-if", true)
          ])
        ],
        2
        /* CLASS */
      );
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card/src/index.vue.mjs
var CheckCard = _export_sfc(_sfc_main30, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card/index.mjs
var PlusCheckCard = CheckCard;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card-group/src/index.vue2.mjs
var _hoisted_119 = { class: "plus-check-card-group" };
var _sfc_main31 = defineComponent({
  ...{
    name: "PlusCheckCardGroup"
  },
  __name: "index",
  props: {
    modelValue: { default: () => [] },
    options: { default: () => [] },
    size: { default: void 0 },
    disabled: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false }
  },
  emits: ["update:modelValue", "change", "extra"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const state = reactive({
      checkList: [],
      checked: ""
    });
    watchEffect(() => {
      if (props.multiple) {
        state.checkList = props.modelValue;
      } else {
        state.checked = props.modelValue;
      }
    });
    const getChecked = (value) => {
      if (props.multiple) {
        return state.checkList.includes(value);
      } else {
        return state.checked === value;
      }
    };
    const handleChange = (model, value) => {
      if (props.multiple) {
        if (model) {
          state.checkList.push(value);
        } else {
          state.checkList = state.checkList.filter((item) => item !== value);
        }
        emit("update:modelValue", state.checkList);
        emit("change", state.checkList);
      } else {
        const val = model ? value : "";
        emit("update:modelValue", val);
        emit("change", val);
      }
    };
    const handleExtra = (item) => {
      if (props.disabled)
        return;
      emit("extra", item);
    };
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_119, [
        (openBlock(true), createElementBlock(
          Fragment,
          null,
          renderList(_ctx.options, (item, index) => {
            return openBlock(), createBlock(unref(PlusCheckCard), mergeProps({
              key: item.value || index,
              size: _ctx.size,
              disabled: _ctx.disabled
            }, item, {
              "model-value": getChecked(item.value),
              onChange: ($event) => handleChange($event, item.value),
              onExtra: ($event) => handleExtra(item)
            }), createSlots({
              _: 2
              /* DYNAMIC */
            }, [
              _ctx.$slots["avatar-" + item.value] || _ctx.$slots.avatar ? {
                name: "avatar",
                fn: withCtx((data) => [
                  _ctx.$slots["avatar-" + item.value] ? renderSlot(_ctx.$slots, "avatar-" + item.value, normalizeProps(mergeProps({ key: 0 }, data))) : createCommentVNode("v-if", true),
                  _ctx.$slots.avatar ? renderSlot(_ctx.$slots, "avatar", normalizeProps(mergeProps({ key: 1 }, data))) : createCommentVNode("v-if", true)
                ]),
                key: "0"
              } : void 0,
              _ctx.$slots["title-" + item.value] || _ctx.$slots.title ? {
                name: "title",
                fn: withCtx((data) => [
                  _ctx.$slots["title-" + item.value] ? renderSlot(_ctx.$slots, "title-" + item.value, normalizeProps(mergeProps({ key: 0 }, data))) : createCommentVNode("v-if", true),
                  _ctx.$slots.title ? renderSlot(_ctx.$slots, "title", normalizeProps(mergeProps({ key: 1 }, data))) : createCommentVNode("v-if", true)
                ]),
                key: "1"
              } : void 0,
              _ctx.$slots["description-" + item.value] || _ctx.$slots.description ? {
                name: "description",
                fn: withCtx((data) => [
                  _ctx.$slots["description-" + item.value] ? renderSlot(_ctx.$slots, "description-" + item.value, normalizeProps(mergeProps({ key: 0 }, data))) : createCommentVNode("v-if", true),
                  _ctx.$slots.description ? renderSlot(_ctx.$slots, "description", normalizeProps(mergeProps({ key: 1 }, data))) : createCommentVNode("v-if", true)
                ]),
                key: "2"
              } : void 0,
              _ctx.$slots["extra-" + item.value] || _ctx.$slots.extra ? {
                name: "extra",
                fn: withCtx((data) => [
                  _ctx.$slots["extra-" + item.value] ? renderSlot(_ctx.$slots, "extra-" + item.value, normalizeProps(mergeProps({ key: 0 }, data))) : createCommentVNode("v-if", true),
                  _ctx.$slots.extra ? renderSlot(_ctx.$slots, "extra", normalizeProps(mergeProps({ key: 1 }, data))) : createCommentVNode("v-if", true)
                ]),
                key: "3"
              } : void 0
            ]), 1040, ["size", "disabled", "model-value", "onChange", "onExtra"]);
          }),
          128
          /* KEYED_FRAGMENT */
        ))
      ]);
    };
  }
});

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card-group/src/index.vue.mjs
var CheckCardGroup = _export_sfc(_sfc_main31, [["__file", "index.vue"]]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/components/check-card-group/index.mjs
var PlusCheckCardGroup = CheckCardGroup;

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/component.mjs
var plugins = [
  PlusDialog,
  PlusPagination,
  PlusTable,
  PlusRadio,
  PlusDatePicker,
  PlusDescriptions,
  PlusDisplayItem,
  PlusFormItem,
  PlusForm,
  PlusPopover,
  PlusSearch,
  PlusDialogForm,
  PlusDrawerForm,
  PlusPage,
  PlusStepsForm,
  PlusInputTag,
  PlusBreadcrumb,
  PlusSidebar,
  PlusHeader,
  PlusLayout,
  PlusCheckCard,
  PlusCheckCardGroup
];

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/defaults.mjs
var installer = makeInstaller([...plugins]);

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/version.mjs
var version2 = "0.1.16";

// node_modules/.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/index.mjs
var install = installer.install;
export {
  DatePickerValueIsArrayList,
  DefaultPageInfo,
  DefaultPageSizeList,
  PlusBreadcrumb,
  PlusCheckCard,
  PlusCheckCardGroup,
  PlusDatePicker,
  PlusDescriptions,
  PlusDialog,
  PlusDialogForm,
  PlusDisplayItem,
  PlusDrawerForm,
  PlusForm,
  PlusFormItem,
  PlusHeader,
  PlusInputTag,
  PlusLayout,
  PlusPage,
  PlusPagination,
  PlusPopover,
  PlusRadio,
  PlusSearch,
  PlusSidebar,
  PlusSidebarItem,
  PlusStepsForm,
  PlusTable,
  TableFormFieldRefInjectionKey,
  TableFormRefInjectionKey,
  ValueIsArrayList,
  ValueIsBooleanList,
  ValueIsNumberList,
  buildLocaleContext,
  buildTranslator,
  installer as default,
  getOptionsByOptionsMap,
  install,
  translate,
  useGetOptions,
  useLocale,
  useTable,
  version2 as version
};
//# sourceMappingURL=plus-pro-components.js.map
