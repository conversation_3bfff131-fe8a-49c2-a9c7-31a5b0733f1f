using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_department")]
public class Department : BaseModel
{
    [Column("parent_id")]
    public ulong ParentId { get; set; } = 0;

    [Required]
    [StringLength(255)]
    public string Ancestors { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("dept_name")]
    public string DeptName { get; set; } = string.Empty;

    [StringLength(32)]
    [Column("dept_code")]
    public string DeptCode { get; set; } = string.Empty;

    [StringLength(24)]
    public string Leader { get; set; } = string.Empty;

    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;
    public sbyte Status { get; set; } = 1; // 1正常 2停用

    [StringLength(255)]
    public string Brief { get; set; } = string.Empty;

    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
