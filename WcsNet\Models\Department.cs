using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_department")]
public class Department : BaseModel
{
    public uint ParentId { get; set; } = 0;
    
    [Required]
    [StringLength(255)]
    public string Ancestors { get; set; } = string.Empty;
    
    [Required]
    [StringLength(64)]
    public string DeptName { get; set; } = string.Empty;
    
    [StringLength(32)]
    public string DeptCode { get; set; } = string.Empty;
    
    [StringLength(24)]
    public string Leader { get; set; } = string.Empty;
    
    public int SortNumber { get; set; } = 0;
    public sbyte Status { get; set; } = 1; // 1正常 2停用
    
    [StringLength(255)]
    public string Brief { get; set; } = string.Empty;
}
