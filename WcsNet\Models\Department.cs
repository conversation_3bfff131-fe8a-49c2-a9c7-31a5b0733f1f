using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_dept")]
public class Department : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("dept_name")]
    public string DeptName { get; set; } = string.Empty;

    [StringLength(64)]
    [Column("dept_code")]
    public string DeptCode { get; set; } = string.Empty;

    [Column("parent_id")]
    public ulong ParentId { get; set; } = 0;

    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;

    public sbyte Status { get; set; } = 1;

    [StringLength(128)]
    public string Brief { get; set; } = string.Empty;
}
