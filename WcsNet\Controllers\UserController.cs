using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;
using WcsNet.Utils;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class UserController : ControllerBase
{
    private readonly UserService _userService;
    private readonly IStorageService _storage;
    private const string TokenPrefix = "loginTokens:";
    private const string RefreshTokenPrefix = "refreshTokens:";

    public UserController(UserService userService, IStorageService storage)
    {
        _userService = userService;
        _storage = storage;
    }

    /// <summary>
    /// 用户登录
    /// </summary>
    /// <param name="request">登录请求</param>
    /// <returns>登录响应</returns>
    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<LoginResponse>>> Login([FromBody] LoginRequest request)
    {
        try
        {
            var user = await _userService.LoginAsync(request.Account, request.Password);
            if (user == null)
            {
                return Ok(ApiResponse<LoginResponse>.Error(401, "账号或密码错误"));
            }

            var response = new LoginResponse
            {
                Id = user.Id,
                Username = user.Account,
                Realname = user.Realname,
                Photo = user.Photo,
                DeptId = user.DeptId,
                RoleId = user.RoleId,
                Mobile = user.Mobile,
                Email = user.Email
            };

            // 设置角色和权限
            if (user.RoleId == 1)
            {
                response.Roles = new List<string> { "admin" };
                response.Permissions = new List<string> { "*:*:*" };
            }
            else if (user.RoleId > 1)
            {
                // TODO: 从数据库获取角色权限
                response.Roles = new List<string> { "user" };
                response.Permissions = new List<string>();
            }

            // 生成Token
            var token = TokenGenerator.GenerateToken();
            var refreshToken = TokenGenerator.GenerateToken();
            
            // 设置过期时间
            var expires = request.Days > 0 
                ? TimeSpan.FromDays(request.Days) 
                : TimeSpan.FromHours(2);

            // 存储到存储服务
            var userJson = JsonSerializer.Serialize(response);
            await _storage.StringSetAsync(TokenPrefix + token, userJson, expires);
            await _storage.StringSetAsync(RefreshTokenPrefix + refreshToken, userJson, TimeSpan.FromDays(45));

            response.AccessToken = token;
            response.RefreshToken = refreshToken;
            response.Expires = DateTimeOffset.UtcNow.Add(expires).ToUnixTimeMilliseconds();

            return Ok(ApiResponse<LoginResponse>.Success(response, "登录成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LoginResponse>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 用户登出
    /// </summary>
    /// <returns>登出响应</returns>
    [HttpGet("logout")]
    [Authorize]
    public async Task<ActionResult<ApiResponse>> Logout()
    {
        try
        {
            var token = GetTokenFromHeader();
            if (!string.IsNullOrEmpty(token))
            {
                await _storage.KeyDeleteAsync(TokenPrefix + token);
            }

            return Ok(ApiResponse.Success("登出成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 刷新Token
    /// </summary>
    /// <param name="request">刷新Token请求</param>
    /// <returns>新的Token</returns>
    [HttpPost("refreshtoken")]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<LoginResponse>>> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Ok(ApiResponse<LoginResponse>.Error(400, "请求参数无效"));
            }

            var userJson = await _storage.StringGetAsync(RefreshTokenPrefix + request.RefreshToken);
            if (string.IsNullOrEmpty(userJson))
            {
                return Ok(ApiResponse<LoginResponse>.Error(401, "刷新Token无效"));
            }

            var response = JsonSerializer.Deserialize<LoginResponse>(userJson);
            if (response == null)
            {
                return Ok(ApiResponse<LoginResponse>.Error(401, "Token数据无效"));
            }

            // 生成新的Token
            var newToken = TokenGenerator.GenerateToken();
            var newRefreshToken = TokenGenerator.GenerateToken();

            var expires = TimeSpan.FromHours(2);

            // 删除旧的Token
            await _storage.KeyDeleteAsync(RefreshTokenPrefix + request.RefreshToken);

            // 存储新的Token
            var newUserJson = JsonSerializer.Serialize(response);
            await _storage.StringSetAsync(TokenPrefix + newToken, newUserJson, expires);
            await _storage.StringSetAsync(RefreshTokenPrefix + newRefreshToken, newUserJson, TimeSpan.FromDays(45));

            response.AccessToken = newToken;
            response.RefreshToken = newRefreshToken;
            response.Expires = DateTimeOffset.UtcNow.Add(expires).ToUnixTimeMilliseconds();

            return Ok(ApiResponse<LoginResponse>.Success(response, "Token刷新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<LoginResponse>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    /// <returns>用户信息</returns>
    [HttpGet("mine")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<User>>> GetCurrentUser()
    {
        try
        {
            var userId = GetCurrentUserId();
            if (userId == null)
            {
                return Ok(ApiResponse<User>.Error(401, "用户未登录"));
            }

            var user = await _userService.GetUserInfoAsync(userId.Value);
            if (user == null)
            {
                return Ok(ApiResponse<User>.Error(404, "用户不存在"));
            }

            return Ok(ApiResponse<User>.Success(user));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<User>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="user">用户信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    [Authorize]
    public async Task<ActionResult<ApiResponse<User>>> CreateUser([FromBody] User user)
    {
        try
        {
            var createdUser = await _userService.CreateAsync(user);
            return Ok(ApiResponse<User>.Success(createdUser, "用户创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<User>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse>> UpdateUser(ulong id, [FromBody] Dictionary<string, object> updates)
    {
        try
        {
            var operatorId = GetCurrentUserId();
            var success = await _userService.UpdateAsync(id, updates, (long?)operatorId);
            
            if (success)
            {
                return Ok(ApiResponse.Success("用户更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "用户不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>用户列表</returns>
    [HttpGet]
    [Authorize]
    public async Task<ActionResult<ApiResponse<PagedResponse<User>>>> GetUsers(int pageIndex = 1, int pageSize = 20)
    {
        try
        {
            var (items, total) = await _userService.GetPagedAsync(pageIndex, pageSize);
            
            // 清空密码字段
            foreach (var user in items)
            {
                user.Password = string.Empty;
            }

            var response = new PagedResponse<User>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<User>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<User>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    [Authorize]
    public async Task<ActionResult<ApiResponse>> DeleteUser(ulong id)
    {
        try
        {
            var success = await _userService.DeleteAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("用户删除成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "用户不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    private string? GetTokenFromHeader()
    {
        var authHeader = Request.Headers.Authorization.FirstOrDefault();
        if (authHeader?.StartsWith("Bearer ") == true)
        {
            return authHeader.Substring("Bearer ".Length);
        }
        return null;
    }

    private ulong? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst("uid");
        if (userIdClaim != null && ulong.TryParse(userIdClaim.Value, out var userId))
        {
            return userId;
        }
        return null;
    }
}
