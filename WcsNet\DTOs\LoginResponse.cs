namespace WcsNet.DTOs;

/// <summary>
/// 登录响应DTO
/// </summary>
public class LoginResponse
{
    public ulong Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Realname { get; set; } = string.Empty;
    public string Photo { get; set; } = string.Empty;
    public ulong DeptId { get; set; }
    public ulong RoleId { get; set; }
    public string Mobile { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string AccessToken { get; set; } = string.Empty;
    public string RefreshToken { get; set; } = string.Empty;
    public long Expires { get; set; }
}
