using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_user")]
public class User : BaseModel
{
    [Required]
    [StringLength(64)]
    public string Account { get; set; } = string.Empty;
    
    [Required]
    [StringLength(128)]
    public string Password { get; set; } = string.Empty;
    
    [Required]
    [StringLength(64)]
    public string Realname { get; set; } = string.Empty;
    
    [StringLength(24)]
    [Column("staff_code")]
    public string StaffCode { get; set; } = string.Empty;
    
    [StringLength(128)]
    public string Photo { get; set; } = string.Empty;

    [Column("dept_id")]
    public uint DeptId { get; set; } = 0;

    [Column("role_id")]
    public uint RoleId { get; set; } = 0;
    
    [StringLength(15)]
    public string Mobile { get; set; } = string.Empty;
    
    [StringLength(64)]
    public string Email { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1启用 2停用

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;
}
