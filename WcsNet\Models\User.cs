using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_user")]
public class User : BaseModel
{
    [Required]
    [StringLength(64)]
    public string Account { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    public string Password { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    public string Realname { get; set; } = string.Empty;

    [StringLength(24)]
    [Column("staff_code")]
    public string StaffCode { get; set; } = string.Empty;

    [StringLength(128)]
    public string Photo { get; set; } = string.Empty;

    [Column("dept_id")]
    public ulong DeptId { get; set; } = 0;

    [Column("role_id")]
    public ulong RoleId { get; set; } = 0;

    [StringLength(15)]
    public string Mobile { get; set; } = string.Empty;

    [StringLength(255)]
    public string Email { get; set; } = string.Empty;

    public sbyte Gender { get; set; } = 3; // 1男 2女 3未知

    public sbyte Status { get; set; } = 1; // 1启用 2停用

    [Column("user_source")]
    public sbyte UserSource { get; set; } = 1; // 1内部 2外部

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;

    // 添加时间戳字段
    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
