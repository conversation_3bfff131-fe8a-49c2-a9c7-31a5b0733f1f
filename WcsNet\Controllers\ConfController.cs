using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 系统配置管理控制器
/// </summary>
[ApiController]
[Route("api/sys/confs")]
public class ConfController : ControllerBase
{
    private readonly WcsDbContext _context;

    public ConfController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取配置参数列表
    /// </summary>
    /// <param name="name">参数名称</param>
    /// <param name="key">参数键</param>
    /// <param name="type">参数类型</param>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>配置参数列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<object>>> GetConfs(
        string? name = null,
        string? key = null,
        int? type = null,
        int page_size = 10,
        int page_no = 1)
    {
        try
        {
            var query = _context.Parameters.AsQueryable();

            // 按名称过滤
            if (!string.IsNullOrEmpty(name))
            {
                query = query.Where(p => p.ConfName.Contains(name));
            }

            // 按键过滤
            if (!string.IsNullOrEmpty(key))
            {
                query = query.Where(p => p.ConfKey.Contains(key));
            }

            // 按类型过滤
            if (type.HasValue)
            {
                query = query.Where(p => p.ConfType == type.Value);
            }

            var total = await query.CountAsync();
            var confs = await query
                .OrderBy(p => p.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(p => new
                {
                    p.Id,
                    Name = p.ConfName,
                    Key = p.ConfKey,
                    Value = p.ConfValue,
                    Type = p.ConfType,
                    p.Remark,
                    CreatedAt = p.CreatedAt ?? DateTime.Now,
                    UpdatedAt = p.UpdatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new
            {
                list = confs,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取配置参数
    /// </summary>
    /// <param name="id">参数ID</param>
    /// <returns>配置参数</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<object>>> GetConf(ulong id)
    {
        try
        {
            var conf = await _context.Parameters.FindAsync(id);
            if (conf == null)
            {
                return Ok(ApiResponse<object>.Error(404, "配置参数不存在"));
            }

            var response = new
            {
                conf.Id,
                Name = conf.ConfName,
                Key = conf.ConfKey,
                Value = conf.ConfValue,
                Type = conf.ConfType,
                conf.Remark,
                CreatedAt = conf.CreatedAt ?? DateTime.Now,
                UpdatedAt = conf.UpdatedAt ?? DateTime.Now
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建配置参数
    /// </summary>
    /// <param name="request">配置参数信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<string>>> CreateConf([FromBody] CreateConfRequest request)
    {
        try
        {
            // 检查键是否已存在
            var existingConf = await _context.Parameters
                .FirstOrDefaultAsync(p => p.ConfKey == request.Key);
            if (existingConf != null)
            {
                return Ok(ApiResponse<string>.Error(400, "配置键已存在"));
            }

            var conf = new Parameter
            {
                ConfName = request.Name,
                ConfKey = request.Key,
                ConfValue = request.Value,
                ConfType = request.Type,
                Remark = request.Remark ?? ""
            };

            _context.Parameters.Add(conf);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("配置参数创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新配置参数
    /// </summary>
    /// <param name="id">参数ID</param>
    /// <param name="request">配置参数信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateConf(ulong id, [FromBody] UpdateConfRequest request)
    {
        try
        {
            var conf = await _context.Parameters.FindAsync(id);
            if (conf == null)
            {
                return Ok(ApiResponse<string>.Error(404, "配置参数不存在"));
            }

            if (!string.IsNullOrEmpty(request.Name)) conf.ConfName = request.Name;
            if (!string.IsNullOrEmpty(request.Key)) conf.ConfKey = request.Key;
            if (!string.IsNullOrEmpty(request.Value)) conf.ConfValue = request.Value;
            if (request.Type.HasValue) conf.ConfType = request.Type.Value;
            if (request.Remark != null) conf.Remark = request.Remark;

            conf.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("配置参数更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除配置参数
    /// </summary>
    /// <param name="id">参数ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteConf(ulong id)
    {
        try
        {
            var conf = await _context.Parameters.FindAsync(id);
            if (conf == null)
            {
                return Ok(ApiResponse<string>.Error(404, "配置参数不存在"));
            }

            _context.Parameters.Remove(conf);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("配置参数删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}

public class CreateConfRequest
{
    public string Name { get; set; } = string.Empty;
    public string Key { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public int Type { get; set; } = 1;
    public string? Remark { get; set; }
}

public class UpdateConfRequest
{
    public string? Name { get; set; }
    public string? Key { get; set; }
    public string? Value { get; set; }
    public int? Type { get; set; }
    public string? Remark { get; set; }
}
