using Microsoft.EntityFrameworkCore;
using Serilog;
using WcsNet.Configuration;
using WcsNet.Middleware;
using WcsNet.Models;
using WcsNet.Services;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .WriteTo.Console()
    .WriteTo.File("logs/wcs-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// 绑定配置
var appConfig = new AppConfig();
builder.Configuration.Bind(appConfig);
builder.Services.AddSingleton(appConfig);

// 配置数据库
var connectionString = BuildConnectionString(appConfig.Database);
builder.Services.AddDbContext<WcsDbContext>(options =>
{
    if (appConfig.Database.Driver.ToLower() == "mysql")
    {
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
    }
    else
    {
        throw new NotSupportedException($"Database driver '{appConfig.Database.Driver}' is not supported");
    }
});

// 配置存储服务（使用内存存储）
builder.Services.AddSingleton<IStorageService, MemoryStorageService>();
Log.Information("使用内存存储服务");

// 注册服务
builder.Services.AddScoped<UserService>(provider =>
{
    var context = provider.GetRequiredService<WcsDbContext>();
    var logger = provider.GetRequiredService<ILogger<UserService>>();
    return new UserService(context, appConfig.Secret.Salt, logger);
});
builder.Services.AddScoped<WorkService>();
builder.Services.AddScoped<DeviceService>();
builder.Services.AddScoped<StockService>();
builder.Services.AddScoped<PermissionService>();
builder.Services.AddScoped<DictService>();

// 添加控制器
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        // 配置JSON序列化使用camelCase命名（默认）
        options.JsonSerializerOptions.PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
        options.JsonSerializerOptions.DictionaryKeyPolicy = System.Text.Json.JsonNamingPolicy.CamelCase;
    });

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 配置Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// 确保数据库已创建并初始化数据
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<WcsDbContext>();
    try
    {
        // 强制创建数据库和表
        await context.Database.EnsureCreatedAsync();
        Log.Information("数据库和表创建成功");

        // 数据库连接测试
        var userCount = await context.Users.CountAsync();
        Log.Information("数据库连接正常，当前有 {UserCount} 个用户", userCount);
    }
    catch (Exception ex)
    {
        Log.Error(ex, "数据库初始化失败: {Message}", ex.Message);
        // 不要因为数据库初始化失败而停止应用启动
    }
}

// 确保上传目录存在
var uploadDir = Path.IsPathRooted(appConfig.UploadDir)
    ? appConfig.UploadDir
    : Path.Combine(Directory.GetCurrentDirectory(), appConfig.UploadDir);

if (!Directory.Exists(uploadDir))
{
    Directory.CreateDirectory(uploadDir);
}

// 配置中间件管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 启用CORS
app.UseCors();

// 配置静态文件服务
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(uploadDir),
    RequestPath = "/static"
});

// 自定义日志中间件
app.UseCustomLogging();

// 自定义认证中间件
app.UseCustomAuthentication();

app.UseHttpsRedirection();

app.MapControllers();

// 启动服务器
var port = appConfig.Port;
app.Urls.Add($"http://*:{port}");

Log.Information("WCS.NET服务启动，端口: {Port}", port);

app.Run();

static string BuildConnectionString(DatabaseConfig config)
{
    return $"Server={config.Host};Port={config.Port};Database={config.Name};Uid={config.User};Pwd={config.Password};CharSet=utf8mb4;";
}


