using Microsoft.EntityFrameworkCore;
using Serilog;
using WcsNet.Configuration;
using WcsNet.Middleware;
using WcsNet.Models;
using WcsNet.Services;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .WriteTo.Console()
    .WriteTo.File("logs/wcs-.log", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// 绑定配置
var appConfig = new AppConfig();
builder.Configuration.Bind(appConfig);
builder.Services.AddSingleton(appConfig);

// 配置数据库
var connectionString = BuildConnectionString(appConfig.Database);
builder.Services.AddDbContext<WcsDbContext>(options =>
{
    if (appConfig.Database.Driver.ToLower() == "mysql")
    {
        options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
    }
    else
    {
        throw new NotSupportedException($"Database driver '{appConfig.Database.Driver}' is not supported");
    }
});

// 配置存储服务（使用内存存储）
builder.Services.AddSingleton<IStorageService, MemoryStorageService>();
Log.Information("使用内存存储服务");

// 注册服务
builder.Services.AddScoped<UserService>(provider =>
{
    var context = provider.GetRequiredService<WcsDbContext>();
    var logger = provider.GetRequiredService<ILogger<UserService>>();
    return new UserService(context, appConfig.Secret.Salt, logger);
});
builder.Services.AddScoped<WorkService>();
builder.Services.AddScoped<DeviceService>();
builder.Services.AddScoped<StockService>();
builder.Services.AddScoped<PermissionService>();

// 添加控制器
builder.Services.AddControllers();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 配置Swagger
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

// 确保数据库已创建并初始化数据
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<WcsDbContext>();
    try
    {
        // 强制创建数据库和表
        await context.Database.EnsureCreatedAsync();
        Log.Information("数据库和表创建成功");

        // 检查是否有角色数据
        var roleCount = await context.Roles.CountAsync();
        if (roleCount == 0)
        {
            await InitializeRoleDataAsync(context);
            Log.Information("已初始化角色数据");
        }

        // 检查是否有用户数据
        var userCount = await context.Users.CountAsync();
        if (userCount == 0)
        {
            var defaultUser = new User
            {
                Account = "admin",
                Password = UserService.HashPassword("123456", appConfig.Secret.Salt),
                Realname = "管理员",
                Email = "<EMAIL>",
                Mobile = "***********",
                Status = 1,
                RoleId = 1,
                DeptId = 1
            };
            context.Users.Add(defaultUser);
            await context.SaveChangesAsync();
            Log.Information("已创建默认用户: admin/123456");
        }
        else
        {
            Log.Information("数据库已有 {UserCount} 个用户", userCount);
        }

        // 检查是否有菜单数据
        var menuCount = await context.Permissions.CountAsync();
        if (menuCount == 0)
        {
            await InitializeMenuDataAsync(context);
            Log.Information("已初始化菜单数据");
        }
        else
        {
            Log.Information("数据库已有 {MenuCount} 个菜单", menuCount);
        }
    }
    catch (Exception ex)
    {
        Log.Error(ex, "数据库初始化失败: {Message}", ex.Message);
        // 不要因为数据库初始化失败而停止应用启动
    }
}

// 确保上传目录存在
var uploadDir = Path.IsPathRooted(appConfig.UploadDir)
    ? appConfig.UploadDir
    : Path.Combine(Directory.GetCurrentDirectory(), appConfig.UploadDir);

if (!Directory.Exists(uploadDir))
{
    Directory.CreateDirectory(uploadDir);
}

// 配置中间件管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// 启用CORS
app.UseCors();

// 配置静态文件服务
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new Microsoft.Extensions.FileProviders.PhysicalFileProvider(uploadDir),
    RequestPath = "/static"
});

// 自定义日志中间件
app.UseCustomLogging();

// 自定义认证中间件
app.UseCustomAuthentication();

app.UseHttpsRedirection();

app.MapControllers();

// 启动服务器
var port = appConfig.Port;
app.Urls.Add($"http://*:{port}");

Log.Information("WCS.NET服务启动，端口: {Port}", port);

app.Run();

static string BuildConnectionString(DatabaseConfig config)
{
    return $"Server={config.Host};Port={config.Port};Database={config.Name};Uid={config.User};Pwd={config.Password};CharSet=utf8mb4;";
}

/// <summary>
/// 初始化菜单数据（基于Go版本的SQL数据）
/// </summary>
static async Task InitializeMenuDataAsync(WcsDbContext context)
{
    var permissions = new List<Permission>
    {
        // 首页
        new Permission
        {
            Id = 1,
            PermType = 0,
            Title = "menus.pureHome",
            ParentId = 0,
            RoutePath = "/welcome",
            Name = "Welcome",
            Component = "welcome",
            Redirect = "",
            Rank = 0,
            Icon = "ep:home-filled",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        },
        // 系统管理
        new Permission
        {
            Id = 300,
            PermType = 0,
            Title = "系统管理",
            ParentId = 0,
            RoutePath = "/system",
            Name = "System",
            Component = "",
            Redirect = "",
            Rank = 80,
            Icon = "ri:settings-3-line",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        },
        // 用户管理
        new Permission
        {
            Id = 301,
            PermType = 0,
            Title = "用户管理",
            ParentId = 300,
            RoutePath = "/system/user/index",
            Name = "SystemUser",
            Component = "",
            Redirect = "",
            Rank = 0,
            Icon = "ri:admin-line",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        },
        // 角色管理
        new Permission
        {
            Id = 302,
            PermType = 0,
            Title = "角色管理",
            ParentId = 300,
            RoutePath = "/system/role/index",
            Name = "SystemRole",
            Component = "",
            Redirect = "",
            Rank = 0,
            Icon = "ri:admin-fill",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        },
        // 菜单管理
        new Permission
        {
            Id = 303,
            PermType = 0,
            Title = "菜单管理",
            ParentId = 300,
            RoutePath = "/system/menu/index",
            Name = "SystemMenu",
            Component = "",
            Redirect = "",
            Rank = 0,
            Icon = "ep:menu",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        },
        // 部门管理
        new Permission
        {
            Id = 304,
            PermType = 0,
            Title = "部门管理",
            ParentId = 300,
            RoutePath = "/system/dept/index",
            Name = "SystemDept",
            Component = "",
            Redirect = "",
            Rank = 0,
            Icon = "ri:git-branch-line",
            ExtraIcon = "",
            ActivePath = "",
            FrameSrc = "",
            FrameLoading = 1,
            ShowLink = 1,
            HideTag = 0,
            Keepalive = 0,
            FixedTag = 0,
            ShowParent = 0,
            PermCode = ""
        }
    };

    context.Permissions.AddRange(permissions);
    await context.SaveChangesAsync();
}

/// <summary>
/// 初始化角色数据（基于Go版本的SQL数据）
/// </summary>
static async Task InitializeRoleDataAsync(WcsDbContext context)
{
    var roles = new List<Role>
    {
        new Role
        {
            Id = 1,
            RoleName = "超级管理员",
            RoleCode = "admin",
            RoleType = 3,
            SortNumber = 0,
            Status = 1,
            Remark = "",
            Perms = "1,300,301,302,303,304"
        }
    };

    context.Roles.AddRange(roles);
    await context.SaveChangesAsync();
}
