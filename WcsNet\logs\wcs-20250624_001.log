2025-06-24 10:34:09.896 +08:00 [INF] 使用内存存储服务
2025-06-24 10:34:11.052 +08:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:34:11.059 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:34:11.172 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:34:11.177 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:34:11.240 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:34:11.281 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://[::]:8666: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.AnyIPListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-24 10:34:52.311 +08:00 [INF] 使用内存存储服务
2025-06-24 10:34:53.466 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:34:53.473 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:34:53.631 +08:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:34:53.636 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:34:53.689 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:34:53.722 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:34:53.724 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:34:53.724 +08:00 [INF] Hosting environment: Development
2025-06-24 10:34:53.725 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:37:04.860 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - null null
2025-06-24 10:37:04.914 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:04.920 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:37:04.922 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:04.952 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:05.155 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:37:05.255 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_0 OFFSET @__p_0
2025-06-24 10:37:05.270 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:05.356 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 397.592ms
2025-06-24 10:37:05.360 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:05.362 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3 - 状态码: 200 - 耗时: 449ms - 响应大小: 862bytes
2025-06-24 10:37:05.373 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - 200 null application/json; charset=utf-8 512.8881ms
2025-06-24 10:37:24.035 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:37:24.042 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:24.047 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:24.048 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:24.274 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:37:24.384 +08:00 [INF] Executed DbCommand (83ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:37:24.393 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:24.396 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 344.9402ms
2025-06-24 10:37:24.397 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:24.398 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 355ms - 响应大小: 852bytes
2025-06-24 10:37:24.399 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 364.0723ms
2025-06-24 10:37:34.587 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - null null
2025-06-24 10:37:34.590 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_type=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:34.592 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:34.593 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:34.833 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
2025-06-24 10:37:34.888 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:37:34.890 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:34.892 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 296.9033ms
2025-06-24 10:37:34.893 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:34.894 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_type=1 - 状态码: 200 - 耗时: 303ms - 响应大小: 874bytes
2025-06-24 10:37:34.899 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - 200 null application/json; charset=utf-8 311.4355ms
2025-06-24 10:37:44.072 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - null null
2025-06-24 10:37:44.074 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:44.077 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:44.078 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:44.583 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
2025-06-24 10:37:44.678 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:37:44.682 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:44.683 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 603.0884ms
2025-06-24 10:37:44.685 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:44.686 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 状态码: 200 - 耗时: 611ms - 响应大小: 595bytes
2025-06-24 10:37:44.688 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 200 null application/json; charset=utf-8 616.5468ms
2025-06-24 10:43:50.221 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 10:43:50.224 +08:00 [INF] CORS policy execution successful.
2025-06-24 10:43:50.226 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:50.236 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 10:43:50.243 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 10:43:50.461 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 10:43:50.742 +08:00 [INF] Executed DbCommand (122ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 10:43:50.819 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 10:43:50.845 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 10:43:50.860 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:50.864 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 617.8785ms
2025-06-24 10:43:50.865 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 10:43:50.866 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 640ms - 响应大小: 455bytes
2025-06-24 10:43:50.869 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 647.2922ms
2025-06-24 10:43:50.878 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 10:43:50.881 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:50.884 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:43:50.889 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 10:43:51.129 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 10:43:51.179 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:43:51.190 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 298.6191ms
2025-06-24 10:43:51.192 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:43:51.193 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 312ms - 响应大小: 6656bytes
2025-06-24 10:43:51.196 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 317.7938ms
2025-06-24 10:43:54.811 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 10:43:54.816 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.819 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-24 10:43:54.823 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 10:43:54.828 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 16.5066ms
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 10:43:54.842 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 10:43:54.846 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.854 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 10:43:54.856 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.861 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.862 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.864 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.865 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.868 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:43:54.869 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.871 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:54.873 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.876 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:43:54.876 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.878 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.880 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.881 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.884 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.885 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.885 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.887 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 14.196ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 12.6138ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 17.6179ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 16.1676ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 19.3607ms
2025-06-24 10:43:54.902 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:54.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.905 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.906 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:43:54.907 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 42ms - 响应大小: 1143bytes
2025-06-24 10:43:54.907 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 45ms - 响应大小: 617bytes
2025-06-24 10:43:54.908 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 61ms - 响应大小: 844bytes
2025-06-24 10:43:54.909 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 48ms - 响应大小: 619bytes
2025-06-24 10:43:54.910 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 54ms - 响应大小: 1599bytes
2025-06-24 10:43:54.911 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 69.2975ms
2025-06-24 10:43:54.913 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 71.6582ms
2025-06-24 10:43:54.915 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 92.3119ms
2025-06-24 10:43:54.916 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 75.2197ms
2025-06-24 10:43:54.918 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 77.049ms
2025-06-24 10:43:55.156 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 10:43:55.160 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:55.164 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:55.166 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:55.174 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:55.177 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 3.1472ms
2025-06-24 10:43:55.178 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:55.180 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 19ms - 响应大小: 612bytes
2025-06-24 10:43:55.181 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 25.8702ms
2025-06-24 10:43:59.669 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 10:43:59.672 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:59.674 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:43:59.681 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 10:44:00.107 +08:00 [INF] Executed DbCommand (99ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 10:44:00.131 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:44:00.145 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 461.9187ms
2025-06-24 10:44:00.146 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:44:00.147 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 475ms - 响应大小: 328bytes
2025-06-24 10:44:00.149 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 479.4595ms
2025-06-24 10:48:05.313 +08:00 [INF] 使用内存存储服务
2025-06-24 10:48:06.414 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:48:06.420 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:48:06.552 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:48:06.558 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:48:06.620 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:48:06.655 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:48:06.656 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:48:06.657 +08:00 [INF] Hosting environment: Development
2025-06-24 10:48:06.658 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:48:52.276 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:48:52.305 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:48:52.309 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:48:52.310 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:48:52.326 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:48:52.490 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:48:52.645 +08:00 [INF] Executed DbCommand (109ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:48:52.655 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:48:52.703 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 374.5133ms
2025-06-24 10:48:52.705 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:48:52.706 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 401ms - 响应大小: 810bytes
2025-06-24 10:48:52.712 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 436.8063ms
2025-06-24 10:49:48.632 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 10:49:48.638 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:48.640 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:49:48.645 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 10:49:49.115 +08:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 10:49:49.163 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:49:49.170 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 522.6846ms
2025-06-24 10:49:49.171 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:49:49.172 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 534ms - 响应大小: 6656bytes
2025-06-24 10:49:49.174 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 541.9479ms
2025-06-24 10:49:50.375 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 10:49:50.378 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.379 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 10:49:50.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 4.8283ms
2025-06-24 10:49:50.382 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 10:49:50.393 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 10:49:50.394 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.395 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.396 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.397 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.398 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.400 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.403 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.404 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:49:50.407 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.409 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.411 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.412 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.413 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:49:50.413 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.414 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.416 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.416 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.419 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.421 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.422 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 15.6273ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 9.234ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 12.6333ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 11.0887ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 14.013ms
2025-06-24 10:49:50.431 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.432 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.433 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:49:50.434 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.434 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.435 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 41ms - 响应大小: 577bytes
2025-06-24 10:49:50.436 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 37ms - 响应大小: 572bytes
2025-06-24 10:49:50.436 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 40ms - 响应大小: 1555bytes
2025-06-24 10:49:50.437 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 39ms - 响应大小: 1100bytes
2025-06-24 10:49:50.438 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 42ms - 响应大小: 802bytes
2025-06-24 10:49:50.439 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 46.9409ms
2025-06-24 10:49:50.442 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 49.2181ms
2025-06-24 10:49:50.444 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 51.1507ms
2025-06-24 10:49:50.445 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 52.7039ms
2025-06-24 10:49:50.448 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 55.7892ms
2025-06-24 10:49:50.702 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 10:49:50.703 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.704 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.705 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.706 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 0.8752ms
2025-06-24 10:49:50.708 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.708 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 4ms - 响应大小: 570bytes
2025-06-24 10:49:50.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 7.26ms
2025-06-24 10:49:56.132 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - null null
2025-06-24 10:49:56.135 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_type=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:49:56.166 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:49:56.169 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:49:56.347 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
2025-06-24 10:49:56.403 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:49:56.406 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:56.409 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 235.6264ms
2025-06-24 10:49:56.410 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:49:56.411 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_type=1 - 状态码: 200 - 耗时: 276ms - 响应大小: 831bytes
2025-06-24 10:49:56.413 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - 200 null application/json; charset=utf-8 281.0418ms
2025-06-24 10:50:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:00.726 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:00.740 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:00.744 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:00.746 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7958ms
2025-06-24 10:50:00.747 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:00.748 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 61ms - 响应大小: 1552bytes
2025-06-24 10:50:00.752 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 68.2304ms
2025-06-24 10:50:05.063 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - null null
2025-06-24 10:50:05.078 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:05.083 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:05.084 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:05.310 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
2025-06-24 10:50:05.359 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:50:05.363 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:05.364 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 268.491ms
2025-06-24 10:50:05.365 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:05.366 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 状态码: 200 - 耗时: 288ms - 响应大小: 552bytes
2025-06-24 10:50:05.368 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 200 null application/json; charset=utf-8 305.7174ms
2025-06-24 10:50:10.381 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:10.384 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:10.386 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:10.387 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:10.390 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5101ms
2025-06-24 10:50:10.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:10.393 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 10:50:10.395 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.2376ms
2025-06-24 10:50:20.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:20.685 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:20.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:20.687 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:20.689 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:20.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.7237ms
2025-06-24 10:50:20.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:20.698 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1552bytes
2025-06-24 10:50:20.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.4822ms
2025-06-24 10:50:23.373 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 183
2025-06-24 10:50:23.380 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:23.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 10:50:23.400 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:23.611 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE `w`.`device_code` = @__request_DeviceCode_0)
2025-06-24 10:50:23.919 +08:00 [INF] Executed DbCommand (66ms) [Parameters=[@p0='?' (Size = 128), @p1='?' (DbType = SByte), @p2='?' (DbType = DateTime), @p3='?' (DbType = Int64), @p4='?' (Size = 128), @p5='?' (Size = 128), @p6='?' (DbType = SByte), @p7='?' (DbType = SByte), @p8='?' (DbType = Int64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = SByte), @p12='?' (DbType = DateTime), @p13='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_device` (`Addr`, `comm_type`, `created_at`, `created_id`, `device_code`, `device_name`, `device_type`, `Online`, `Port`, `Position`, `run_status`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
SELECT `Id`
FROM `wcs_device`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-24 10:50:23.989 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:23.993 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 590.731ms
2025-06-24 10:50:23.994 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 10:50:23.995 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 200 - 耗时: 614ms - 响应大小: 301bytes
2025-06-24 10:50:23.997 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 624.4776ms
2025-06-24 10:50:30.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:30.379 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:30.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:30.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:30.390 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4684ms
2025-06-24 10:50:30.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:30.393 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:50:30.395 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.6867ms
2025-06-24 10:50:33.632 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 10:50:33.668 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:33.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:33.675 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:33.956 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:50:33.983 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:50:33.987 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:33.989 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 308.2222ms
2025-06-24 10:50:33.990 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:33.992 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 323ms - 响应大小: 1824bytes
2025-06-24 10:50:33.993 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 361.167ms
2025-06-24 10:50:40.688 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:40.696 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:40.698 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:40.700 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:40.705 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:40.709 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.4756ms
2025-06-24 10:50:40.710 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:40.711 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 15ms - 响应大小: 1553bytes
2025-06-24 10:50:40.713 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.4711ms
2025-06-24 10:50:44.420 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - null 0
2025-06-24 10:50:44.450 +08:00 [INF] 请求开始: DELETE /api/devices/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:44.455 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 10:50:44.466 +08:00 [INF] Route matched with {action = "DeleteDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteDevice(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:44.820 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 10:50:44.849 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__id_0
2025-06-24 10:50:44.923 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_device`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-24 10:50:44.931 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:50:44.933 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet) in 464.4077ms
2025-06-24 10:50:44.935 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 10:50:44.935 +08:00 [INF] 请求完成: DELETE /api/devices/8 - 状态码: 200 - 耗时: 485ms - 响应大小: 48bytes
2025-06-24 10:50:44.937 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - 200 null application/json; charset=utf-8 517.3012ms
2025-06-24 10:50:50.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:50.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:50.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:50.382 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7363ms
2025-06-24 10:50:50.383 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:50:50.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8392ms
2025-06-24 10:50:54.357 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 10:50:54.370 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:54.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:54.385 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:54.556 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:50:54.633 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:50:54.635 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:54.636 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 248.4499ms
2025-06-24 10:50:54.637 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:54.638 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 268ms - 响应大小: 1576bytes
2025-06-24 10:50:54.640 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 282.7056ms
2025-06-24 10:51:00.676 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:00.678 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:00.680 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:00.681 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:00.684 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:00.686 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7242ms
2025-06-24 10:51:00.687 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:00.692 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1554bytes
2025-06-24 10:51:00.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 18.9358ms
2025-06-24 10:51:10.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:10.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:10.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:10.397 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 9.2742ms
2025-06-24 10:51:10.398 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:10.400 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1555bytes
2025-06-24 10:51:10.404 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 31.9305ms
2025-06-24 10:51:20.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:20.692 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:20.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:20.697 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:20.698 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8972ms
2025-06-24 10:51:20.703 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:20.704 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:51:20.706 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.5668ms
2025-06-24 10:51:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:30.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3995ms
2025-06-24 10:51:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1555bytes
2025-06-24 10:51:30.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8659ms
2025-06-24 10:51:40.698 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:40.702 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:40.712 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:40.717 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:40.721 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:40.723 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3538ms
2025-06-24 10:51:40.724 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:40.725 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1553bytes
2025-06-24 10:51:40.726 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 28.1655ms
2025-06-24 10:51:50.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:50.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:50.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8607ms
2025-06-24 10:51:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:51:50.386 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8682ms
2025-06-24 10:52:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:00.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.6192ms
2025-06-24 10:52:00.697 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:00.705 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 18ms - 响应大小: 1553bytes
2025-06-24 10:52:00.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.4842ms
2025-06-24 10:52:10.366 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:10.368 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:10.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:10.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7606ms
2025-06-24 10:52:10.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:10.378 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1556bytes
2025-06-24 10:52:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.2774ms
2025-06-24 10:52:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:20.694 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:20.701 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:20.704 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:20.709 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:20.712 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.1402ms
2025-06-24 10:52:20.714 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:20.718 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 23ms - 响应大小: 1554bytes
2025-06-24 10:52:20.721 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 34.0385ms
2025-06-24 10:52:30.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:30.379 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:30.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4008ms
2025-06-24 10:52:30.381 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:30.382 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:52:30.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0055ms
2025-06-24 10:52:40.706 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:40.709 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:40.710 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:40.711 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:40.713 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:40.716 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.8168ms
2025-06-24 10:52:40.717 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:40.718 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:52:40.719 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.5635ms
2025-06-24 10:52:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:50.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:50.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:50.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:50.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:50.391 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.15ms
2025-06-24 10:52:50.394 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:50.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 19ms - 响应大小: 1556bytes
2025-06-24 10:52:50.399 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 25.5195ms
2025-06-24 10:53:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:00.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7157ms
2025-06-24 10:53:00.695 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:53:00.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.8089ms
2025-06-24 10:53:10.380 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:10.383 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:10.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:10.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:10.393 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.3875ms
2025-06-24 10:53:10.395 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:10.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1555bytes
2025-06-24 10:53:10.397 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.1242ms
2025-06-24 10:53:20.667 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:20.669 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:20.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:20.672 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:20.674 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:20.676 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8013ms
2025-06-24 10:53:20.677 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:20.677 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:53:20.679 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.557ms
2025-06-24 10:53:30.374 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:30.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:30.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:30.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:30.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6983ms
2025-06-24 10:53:30.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:30.388 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:53:30.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.6374ms
2025-06-24 10:53:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:40.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:40.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:40.690 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:40.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:40.696 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.777ms
2025-06-24 10:53:40.697 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:40.698 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1556bytes
2025-06-24 10:53:40.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.5515ms
2025-06-24 10:53:50.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:50.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:50.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:50.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6914ms
2025-06-24 10:53:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1553bytes
2025-06-24 10:53:50.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.8238ms
2025-06-24 10:54:00.678 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:00.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:00.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:00.685 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:00.688 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:00.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5504ms
2025-06-24 10:54:00.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:00.692 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1555bytes
2025-06-24 10:54:00.694 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.8787ms
2025-06-24 10:54:10.374 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:10.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:10.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:10.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:10.386 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.3069ms
2025-06-24 10:54:10.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:10.388 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1552bytes
2025-06-24 10:54:10.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.6515ms
2025-06-24 10:54:20.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:20.690 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:20.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:20.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:20.696 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2653ms
2025-06-24 10:54:20.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:20.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 10:54:20.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.2861ms
2025-06-24 10:54:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:30.383 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:30.385 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:30.387 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4624ms
2025-06-24 10:54:30.388 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:30.389 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:54:30.391 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 20.063ms
2025-06-24 10:54:40.674 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:40.677 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:40.679 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:40.680 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:40.682 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:40.684 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4866ms
2025-06-24 10:54:40.685 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:40.686 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:54:40.693 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 18.8523ms
2025-06-24 10:54:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:50.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:50.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:50.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:50.384 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:50.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7358ms
2025-06-24 10:54:50.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:50.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1554bytes
2025-06-24 10:54:50.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.6334ms
2025-06-24 10:55:00.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:00.681 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:00.683 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:00.684 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:00.685 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:00.687 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5779ms
2025-06-24 10:55:00.688 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:00.689 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 10:55:00.691 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.5795ms
2025-06-24 10:55:10.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:10.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:10.382 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:10.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:10.384 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1249ms
2025-06-24 10:55:10.385 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:10.386 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:55:10.388 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.5047ms
2025-06-24 10:55:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:20.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:20.692 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:20.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:20.695 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7237ms
2025-06-24 10:55:20.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:20.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:55:20.698 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.1088ms
2025-06-24 10:55:30.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:30.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.358ms
2025-06-24 10:55:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:55:30.388 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.0931ms
2025-06-24 10:55:40.691 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:40.693 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:40.695 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:40.695 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:40.700 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:40.704 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.7841ms
2025-06-24 10:55:40.705 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:40.706 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1553bytes
2025-06-24 10:55:40.708 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8636ms
2025-06-24 10:55:50.380 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:50.382 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:50.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:50.386 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:50.405 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:50.408 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.5293ms
2025-06-24 10:55:50.409 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:50.410 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 27ms - 响应大小: 1556bytes
2025-06-24 10:55:50.412 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 32.4229ms
2025-06-24 10:56:00.682 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:00.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:00.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:00.688 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:00.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:00.691 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5778ms
2025-06-24 10:56:00.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:00.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:56:00.696 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.6544ms
2025-06-24 10:56:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:10.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:10.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:10.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:10.383 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3025ms
2025-06-24 10:56:10.384 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:10.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1554bytes
2025-06-24 10:56:10.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 20.3173ms
2025-06-24 10:56:20.692 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:20.695 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:20.698 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:20.699 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:20.701 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:20.702 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0727ms
2025-06-24 10:56:20.703 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:20.704 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:56:20.706 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.489ms
2025-06-24 10:56:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:30.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:30.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:30.387 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.7612ms
2025-06-24 10:56:30.394 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:30.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1556bytes
2025-06-24 10:56:30.398 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 27.4976ms
2025-06-24 10:56:40.680 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:40.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:40.685 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:40.687 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:40.689 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:40.691 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.449ms
2025-06-24 10:56:40.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:40.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1556bytes
2025-06-24 10:56:40.705 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.8616ms
2025-06-24 10:56:50.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:50.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:50.383 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:50.387 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:50.410 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 23.2436ms
2025-06-24 10:56:50.411 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:50.412 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 40ms - 响应大小: 1555bytes
2025-06-24 10:56:50.413 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 44.1005ms
2025-06-24 10:57:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:00.688 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:00.692 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:00.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:00.695 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:00.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9198ms
2025-06-24 10:57:00.700 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:00.701 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1556bytes
2025-06-24 10:57:00.702 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.9574ms
2025-06-24 10:57:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:10.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:10.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:10.372 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6725ms
2025-06-24 10:57:10.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:10.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:57:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.9007ms
2025-06-24 10:57:20.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:20.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:20.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:20.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:20.695 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:20.699 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 6.2359ms
2025-06-24 10:57:20.700 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:20.702 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1556bytes
2025-06-24 10:57:20.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.6657ms
2025-06-24 10:57:30.366 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:30.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:30.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:30.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:30.373 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5211ms
2025-06-24 10:57:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:30.378 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:57:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8584ms
2025-06-24 10:57:40.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:40.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:40.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:40.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:40.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:40.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5086ms
2025-06-24 10:57:40.698 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:40.699 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:57:40.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0506ms
2025-06-24 10:57:50.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:50.373 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:50.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:50.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:50.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.6626ms
2025-06-24 10:57:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1555bytes
2025-06-24 10:57:50.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.4352ms
2025-06-24 10:58:00.695 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:00.709 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:00.711 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:00.712 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:00.714 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:00.715 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1867ms
2025-06-24 10:58:00.716 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:00.717 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 10:58:00.721 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 26.0091ms
2025-06-24 10:58:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:10.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:10.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:10.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:10.379 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:10.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2521ms
2025-06-24 10:58:10.381 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:10.382 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:58:10.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.5848ms
2025-06-24 10:58:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:20.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:20.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:20.692 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:20.726 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:20.727 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5349ms
2025-06-24 10:58:20.728 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:20.730 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 39ms - 响应大小: 1555bytes
2025-06-24 10:58:20.731 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 43.8184ms
2025-06-24 10:58:30.367 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:30.379 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:30.382 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:30.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:30.388 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4605ms
2025-06-24 10:58:30.390 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:30.391 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1555bytes
2025-06-24 10:58:30.393 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 26.1873ms
2025-06-24 10:58:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:40.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:40.694 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:40.696 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:40.699 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:40.700 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4636ms
2025-06-24 10:58:40.701 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:40.702 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1556bytes
2025-06-24 10:58:40.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.8949ms
2025-06-24 10:58:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:50.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:50.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2097ms
2025-06-24 10:58:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:58:50.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.4558ms
2025-06-24 10:59:00.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:00.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:00.690 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:00.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:00.695 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.423ms
2025-06-24 10:59:00.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1553bytes
2025-06-24 10:59:00.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0113ms
2025-06-24 10:59:10.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:10.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:10.372 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:10.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3584ms
2025-06-24 10:59:10.377 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:10.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:59:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0334ms
2025-06-24 10:59:20.672 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:20.675 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:20.676 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:20.677 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:20.679 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:20.681 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4744ms
2025-06-24 10:59:20.682 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:20.682 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:59:20.687 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.891ms
2025-06-24 10:59:30.364 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:30.367 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:30.369 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:30.370 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:30.372 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.7971ms
2025-06-24 10:59:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:30.377 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:59:30.378 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.1933ms
2025-06-24 10:59:40.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:40.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:40.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:40.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:40.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:40.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4382ms
2025-06-24 10:59:40.695 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:40.696 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:59:40.698 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.7158ms
2025-06-24 10:59:50.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:50.374 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:50.375 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:50.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:50.378 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5596ms
2025-06-24 10:59:50.379 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:50.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1554bytes
2025-06-24 10:59:50.381 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.763ms
2025-06-24 11:00:00.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:00.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:00.693 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:00.699 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:00.704 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:00.709 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.1265ms
2025-06-24 11:00:00.712 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:00.716 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 25ms - 响应大小: 1556bytes
2025-06-24 11:00:00.722 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 35.9001ms
2025-06-24 11:00:10.375 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:10.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:10.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:10.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:10.383 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1246ms
2025-06-24 11:00:10.384 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:10.385 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1555bytes
2025-06-24 11:00:10.386 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.174ms
2025-06-24 11:00:20.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:20.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:20.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:20.686 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:20.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:20.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6686ms
2025-06-24 11:00:20.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:20.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1553bytes
2025-06-24 11:00:20.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.1087ms
2025-06-24 11:00:30.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:30.368 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:30.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:30.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:30.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7266ms
2025-06-24 11:00:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:30.377 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 11:00:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0931ms
2025-06-24 11:00:40.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:40.681 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:40.683 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:40.683 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:40.686 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:40.688 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7193ms
2025-06-24 11:00:40.689 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:40.691 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 11:00:40.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 23.9818ms
2025-06-24 11:00:50.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:50.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:50.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:50.372 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:50.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:50.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3346ms
2025-06-24 11:00:50.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:50.376 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 11:00:50.378 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.8532ms
2025-06-24 11:01:00.682 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:00.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:00.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:00.686 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:00.688 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:00.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3207ms
2025-06-24 11:01:00.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:00.691 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1553bytes
2025-06-24 11:01:00.692 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.0776ms
2025-06-24 11:01:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:10.373 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:10.375 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:10.376 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:10.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:10.378 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0747ms
2025-06-24 11:01:10.379 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:10.380 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:01:10.382 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.3173ms
2025-06-24 11:01:20.671 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:20.673 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:20.674 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:20.675 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:20.677 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:20.678 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2215ms
2025-06-24 11:01:20.679 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:20.680 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:01:20.683 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.1144ms
2025-06-24 11:01:30.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:30.371 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:30.375 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:30.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3207ms
2025-06-24 11:01:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1556bytes
2025-06-24 11:01:30.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.7532ms
2025-06-24 11:01:40.696 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:40.699 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:40.701 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:40.703 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:40.705 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:40.706 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4404ms
2025-06-24 11:01:40.707 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:40.708 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1553bytes
2025-06-24 11:01:40.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.2227ms
2025-06-24 11:01:50.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:50.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:50.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:50.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:50.372 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:50.372 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6482ms
2025-06-24 11:01:50.373 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:50.374 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1556bytes
2025-06-24 11:01:50.376 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 7.3489ms
2025-06-24 11:02:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:00.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:00.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9127ms
2025-06-24 11:02:00.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:00.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 11:02:00.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.0423ms
2025-06-24 11:02:10.410 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:10.413 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:10.414 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:10.416 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:10.419 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:10.444 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 25.4542ms
2025-06-24 11:02:10.446 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:10.449 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 35ms - 响应大小: 1553bytes
2025-06-24 11:02:10.451 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 40.5867ms
2025-06-24 11:02:20.671 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:20.674 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:20.675 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:20.677 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:20.678 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:20.679 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0788ms
2025-06-24 11:02:20.680 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:20.681 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 11:02:20.682 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.8019ms
2025-06-24 11:02:30.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:30.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:30.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:30.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:30.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9539ms
2025-06-24 11:02:30.388 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:30.389 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1555bytes
2025-06-24 11:02:30.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 22.254ms
2025-06-24 11:02:40.689 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:40.692 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:40.693 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:40.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:40.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:40.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5212ms
2025-06-24 11:02:40.698 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:40.700 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 11:02:40.702 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.5965ms
2025-06-24 11:02:50.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:50.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:50.372 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:50.373 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:50.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:50.376 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5236ms
2025-06-24 11:02:50.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:50.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 11:02:50.381 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.563ms
2025-06-24 11:03:00.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:00.690 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:00.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:00.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.302ms
2025-06-24 11:03:00.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1554bytes
2025-06-24 11:03:00.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.4964ms
2025-06-24 11:03:10.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:10.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:10.379 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:10.381 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:10.382 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1544ms
2025-06-24 11:03:10.383 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:10.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1556bytes
2025-06-24 11:03:10.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.4205ms
2025-06-24 11:03:20.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:20.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:20.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:20.685 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:20.687 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:20.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3175ms
2025-06-24 11:03:20.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:20.690 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1554bytes
2025-06-24 11:03:20.693 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.5809ms
2025-06-24 11:03:30.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:30.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:30.373 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:30.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:30.376 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:30.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2346ms
2025-06-24 11:03:30.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:30.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 11:03:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.2136ms
2025-06-24 11:03:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:40.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:40.687 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:40.688 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:40.691 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:40.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3859ms
2025-06-24 11:03:40.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:40.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:03:40.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.3659ms
2025-06-24 11:03:45.135 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:03:45.139 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:45.140 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:45.145 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:03:45.523 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:03:45.541 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:45.558 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 410.768ms
2025-06-24 11:03:45.560 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:45.561 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 421ms - 响应大小: 328bytes
2025-06-24 11:03:45.562 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 427.217ms
2025-06-24 11:03:55.977 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 11:03:55.980 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:55.983 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:03:55.985 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 11:03:56.360 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:03:56.364 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:56.365 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 378.9363ms
2025-06-24 11:03:56.367 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:03:56.368 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 387ms - 响应大小: 6656bytes
2025-06-24 11:03:56.369 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 391.986ms
2025-06-24 11:03:56.745 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 11:03:56.748 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:56.751 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-24 11:03:56.753 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 7.2664ms
2025-06-24 11:03:56.755 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 11:03:56.790 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:03:56.792 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:56.796 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:56.797 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:03:57.078 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:03:57.080 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:57.081 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 281.8292ms
2025-06-24 11:03:57.082 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:57.083 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 290ms - 响应大小: 328bytes
2025-06-24 11:03:57.084 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 294.9252ms
