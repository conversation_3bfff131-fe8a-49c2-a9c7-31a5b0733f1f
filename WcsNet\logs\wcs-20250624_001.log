2025-06-24 10:34:09.896 +08:00 [INF] 使用内存存储服务
2025-06-24 10:34:11.052 +08:00 [INF] Executed DbCommand (49ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:34:11.059 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:34:11.172 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:34:11.177 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:34:11.240 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:34:11.281 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://[::]:8666: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.AnyIPListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-24 10:34:52.311 +08:00 [INF] 使用内存存储服务
2025-06-24 10:34:53.466 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:34:53.473 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:34:53.631 +08:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:34:53.636 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:34:53.689 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:34:53.722 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:34:53.724 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:34:53.724 +08:00 [INF] Hosting environment: Development
2025-06-24 10:34:53.725 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:37:04.860 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - null null
2025-06-24 10:37:04.914 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:04.920 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:37:04.922 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:04.952 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:05.155 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:37:05.255 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_0 OFFSET @__p_0
2025-06-24 10:37:05.270 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:05.356 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 397.592ms
2025-06-24 10:37:05.360 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:05.362 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3 - 状态码: 200 - 耗时: 449ms - 响应大小: 862bytes
2025-06-24 10:37:05.373 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - 200 null application/json; charset=utf-8 512.8881ms
2025-06-24 10:37:24.035 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:37:24.042 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:24.047 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:24.048 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:24.274 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:37:24.384 +08:00 [INF] Executed DbCommand (83ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:37:24.393 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:24.396 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 344.9402ms
2025-06-24 10:37:24.397 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:24.398 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 355ms - 响应大小: 852bytes
2025-06-24 10:37:24.399 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 364.0723ms
2025-06-24 10:37:34.587 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - null null
2025-06-24 10:37:34.590 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_type=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:34.592 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:34.593 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:34.833 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
2025-06-24 10:37:34.888 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:37:34.890 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:34.892 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 296.9033ms
2025-06-24 10:37:34.893 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:34.894 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_type=1 - 状态码: 200 - 耗时: 303ms - 响应大小: 874bytes
2025-06-24 10:37:34.899 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - 200 null application/json; charset=utf-8 311.4355ms
2025-06-24 10:37:44.072 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - null null
2025-06-24 10:37:44.074 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:37:44.077 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:44.078 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:37:44.583 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
2025-06-24 10:37:44.678 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:37:44.682 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:37:44.683 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 603.0884ms
2025-06-24 10:37:44.685 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:37:44.686 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 状态码: 200 - 耗时: 611ms - 响应大小: 595bytes
2025-06-24 10:37:44.688 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 200 null application/json; charset=utf-8 616.5468ms
2025-06-24 10:43:50.221 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 10:43:50.224 +08:00 [INF] CORS policy execution successful.
2025-06-24 10:43:50.226 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:50.236 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 10:43:50.243 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 10:43:50.461 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 10:43:50.742 +08:00 [INF] Executed DbCommand (122ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 10:43:50.819 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 10:43:50.845 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 10:43:50.860 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:50.864 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 617.8785ms
2025-06-24 10:43:50.865 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 10:43:50.866 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 640ms - 响应大小: 455bytes
2025-06-24 10:43:50.869 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 647.2922ms
2025-06-24 10:43:50.878 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 10:43:50.881 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:50.884 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:43:50.889 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 10:43:51.129 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 10:43:51.179 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:43:51.190 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 298.6191ms
2025-06-24 10:43:51.192 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:43:51.193 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 312ms - 响应大小: 6656bytes
2025-06-24 10:43:51.196 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 317.7938ms
2025-06-24 10:43:54.811 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 10:43:54.816 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.819 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-24 10:43:54.823 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 10:43:54.828 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 16.5066ms
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 10:43:54.841 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 10:43:54.842 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 10:43:54.846 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.854 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 10:43:54.856 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.861 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.862 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.864 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:54.865 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.868 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:43:54.869 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.871 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:54.873 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.876 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:43:54.876 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.878 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.880 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.881 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:54.884 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.885 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.885 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.887 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.889 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 14.196ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 12.6138ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 17.6179ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 16.1676ms
2025-06-24 10:43:54.901 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 19.3607ms
2025-06-24 10:43:54.902 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:54.903 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.905 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:43:54.906 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:43:54.907 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 42ms - 响应大小: 1143bytes
2025-06-24 10:43:54.907 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 45ms - 响应大小: 617bytes
2025-06-24 10:43:54.908 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 61ms - 响应大小: 844bytes
2025-06-24 10:43:54.909 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 48ms - 响应大小: 619bytes
2025-06-24 10:43:54.910 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 54ms - 响应大小: 1599bytes
2025-06-24 10:43:54.911 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 69.2975ms
2025-06-24 10:43:54.913 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 71.6582ms
2025-06-24 10:43:54.915 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 92.3119ms
2025-06-24 10:43:54.916 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 75.2197ms
2025-06-24 10:43:54.918 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 77.049ms
2025-06-24 10:43:55.156 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 10:43:55.160 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:55.164 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:55.166 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:43:55.174 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:43:55.177 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 3.1472ms
2025-06-24 10:43:55.178 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:43:55.180 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 19ms - 响应大小: 612bytes
2025-06-24 10:43:55.181 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 25.8702ms
2025-06-24 10:43:59.669 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 10:43:59.672 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:43:59.674 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:43:59.681 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 10:44:00.107 +08:00 [INF] Executed DbCommand (99ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 10:44:00.131 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:44:00.145 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 461.9187ms
2025-06-24 10:44:00.146 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:44:00.147 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 475ms - 响应大小: 328bytes
2025-06-24 10:44:00.149 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 479.4595ms
2025-06-24 10:48:05.313 +08:00 [INF] 使用内存存储服务
2025-06-24 10:48:06.414 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:48:06.420 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:48:06.552 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:48:06.558 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:48:06.620 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:48:06.655 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:48:06.656 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:48:06.657 +08:00 [INF] Hosting environment: Development
2025-06-24 10:48:06.658 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:48:52.276 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:48:52.305 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:48:52.309 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:48:52.310 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:48:52.326 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:48:52.490 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:48:52.645 +08:00 [INF] Executed DbCommand (109ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:48:52.655 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:48:52.703 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 374.5133ms
2025-06-24 10:48:52.705 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:48:52.706 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 401ms - 响应大小: 810bytes
2025-06-24 10:48:52.712 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 436.8063ms
2025-06-24 10:49:48.632 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 10:49:48.638 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:48.640 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:49:48.645 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 10:49:49.115 +08:00 [INF] Executed DbCommand (79ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 10:49:49.163 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:49:49.170 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 522.6846ms
2025-06-24 10:49:49.171 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 10:49:49.172 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 534ms - 响应大小: 6656bytes
2025-06-24 10:49:49.174 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 541.9479ms
2025-06-24 10:49:50.375 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 10:49:50.378 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.379 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 10:49:50.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 4.8283ms
2025-06-24 10:49:50.382 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:49:50.392 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 10:49:50.393 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 10:49:50.394 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.395 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.396 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.397 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.398 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.400 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.403 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.404 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:49:50.407 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.409 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.411 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.412 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.413 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:49:50.413 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.414 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.416 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.416 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.419 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.421 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.422 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 15.6273ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 9.234ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 12.6333ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 11.0887ms
2025-06-24 10:49:50.430 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 14.013ms
2025-06-24 10:49:50.431 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.432 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.433 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:49:50.434 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.434 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 10:49:50.435 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 41ms - 响应大小: 577bytes
2025-06-24 10:49:50.436 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 37ms - 响应大小: 572bytes
2025-06-24 10:49:50.436 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 40ms - 响应大小: 1555bytes
2025-06-24 10:49:50.437 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 39ms - 响应大小: 1100bytes
2025-06-24 10:49:50.438 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 42ms - 响应大小: 802bytes
2025-06-24 10:49:50.439 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 46.9409ms
2025-06-24 10:49:50.442 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 49.2181ms
2025-06-24 10:49:50.444 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 51.1507ms
2025-06-24 10:49:50.445 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 52.7039ms
2025-06-24 10:49:50.448 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 55.7892ms
2025-06-24 10:49:50.702 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 10:49:50.703 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:49:50.704 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.705 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 10:49:50.706 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:50.707 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 0.8752ms
2025-06-24 10:49:50.708 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 10:49:50.708 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 4ms - 响应大小: 570bytes
2025-06-24 10:49:50.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 7.26ms
2025-06-24 10:49:56.132 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - null null
2025-06-24 10:49:56.135 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_type=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:49:56.166 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:49:56.169 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:49:56.347 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
2025-06-24 10:49:56.403 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__request_DeviceType_Value_0='?' (DbType = SByte), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_type` = @__request_DeviceType_Value_0
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:49:56.406 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:49:56.409 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 235.6264ms
2025-06-24 10:49:56.410 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:49:56.411 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_type=1 - 状态码: 200 - 耗时: 276ms - 响应大小: 831bytes
2025-06-24 10:49:56.413 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_type=1 - 200 null application/json; charset=utf-8 281.0418ms
2025-06-24 10:50:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:00.726 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:00.740 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:00.744 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:00.746 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7958ms
2025-06-24 10:50:00.747 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:00.748 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 61ms - 响应大小: 1552bytes
2025-06-24 10:50:00.752 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 68.2304ms
2025-06-24 10:50:05.063 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - null null
2025-06-24 10:50:05.078 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:05.083 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:05.084 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:05.310 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
2025-06-24 10:50:05.359 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[@__request_DeviceName_0_contains='?' (Size = 128), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`device_name` LIKE @__request_DeviceName_0_contains
ORDER BY `w`.`created_at` DESC
LIMIT @__p_2 OFFSET @__p_1
2025-06-24 10:50:05.363 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:05.364 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 268.491ms
2025-06-24 10:50:05.365 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:05.366 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 状态码: 200 - 耗时: 288ms - 响应大小: 552bytes
2025-06-24 10:50:05.368 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10&device_name=%E5%B0%8F%E8%BD%A6 - 200 null application/json; charset=utf-8 305.7174ms
2025-06-24 10:50:10.381 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:10.384 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:10.386 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:10.387 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:10.390 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5101ms
2025-06-24 10:50:10.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:10.393 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 10:50:10.395 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.2376ms
2025-06-24 10:50:20.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:20.685 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:20.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:20.687 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:20.689 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:20.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.7237ms
2025-06-24 10:50:20.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:20.698 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1552bytes
2025-06-24 10:50:20.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.4822ms
2025-06-24 10:50:23.373 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 183
2025-06-24 10:50:23.380 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:23.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 10:50:23.400 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:23.611 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE `w`.`device_code` = @__request_DeviceCode_0)
2025-06-24 10:50:23.919 +08:00 [INF] Executed DbCommand (66ms) [Parameters=[@p0='?' (Size = 128), @p1='?' (DbType = SByte), @p2='?' (DbType = DateTime), @p3='?' (DbType = Int64), @p4='?' (Size = 128), @p5='?' (Size = 128), @p6='?' (DbType = SByte), @p7='?' (DbType = SByte), @p8='?' (DbType = Int64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = SByte), @p12='?' (DbType = DateTime), @p13='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_device` (`Addr`, `comm_type`, `created_at`, `created_id`, `device_code`, `device_name`, `device_type`, `Online`, `Port`, `Position`, `run_status`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
SELECT `Id`
FROM `wcs_device`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-24 10:50:23.989 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:23.993 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 590.731ms
2025-06-24 10:50:23.994 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 10:50:23.995 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 200 - 耗时: 614ms - 响应大小: 301bytes
2025-06-24 10:50:23.997 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 624.4776ms
2025-06-24 10:50:30.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:30.379 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:30.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:30.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:30.390 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4684ms
2025-06-24 10:50:30.392 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:30.393 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:50:30.395 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.6867ms
2025-06-24 10:50:33.632 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 10:50:33.668 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:33.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:33.675 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:33.956 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:50:33.983 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:50:33.987 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:33.989 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 308.2222ms
2025-06-24 10:50:33.990 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:33.992 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 323ms - 响应大小: 1824bytes
2025-06-24 10:50:33.993 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 361.167ms
2025-06-24 10:50:40.688 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:40.696 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:40.698 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:40.700 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:40.705 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:40.709 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.4756ms
2025-06-24 10:50:40.710 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:40.711 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 15ms - 响应大小: 1553bytes
2025-06-24 10:50:40.713 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.4711ms
2025-06-24 10:50:44.420 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - null 0
2025-06-24 10:50:44.450 +08:00 [INF] 请求开始: DELETE /api/devices/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:44.455 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 10:50:44.466 +08:00 [INF] Route matched with {action = "DeleteDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteDevice(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:44.820 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 10:50:44.849 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__id_0
2025-06-24 10:50:44.923 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_device`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-24 10:50:44.931 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:50:44.933 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet) in 464.4077ms
2025-06-24 10:50:44.935 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 10:50:44.935 +08:00 [INF] 请求完成: DELETE /api/devices/8 - 状态码: 200 - 耗时: 485ms - 响应大小: 48bytes
2025-06-24 10:50:44.937 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - 200 null application/json; charset=utf-8 517.3012ms
2025-06-24 10:50:50.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:50:50.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:50:50.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:50:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:50.382 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7363ms
2025-06-24 10:50:50.383 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:50:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:50:50.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8392ms
2025-06-24 10:50:54.357 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 10:50:54.370 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:50:54.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:54.385 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:50:54.556 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:50:54.633 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:50:54.635 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:50:54.636 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 248.4499ms
2025-06-24 10:50:54.637 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:50:54.638 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 268ms - 响应大小: 1576bytes
2025-06-24 10:50:54.640 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 282.7056ms
2025-06-24 10:51:00.676 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:00.678 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:00.680 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:00.681 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:00.684 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:00.686 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7242ms
2025-06-24 10:51:00.687 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:00.692 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1554bytes
2025-06-24 10:51:00.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 18.9358ms
2025-06-24 10:51:10.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:10.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:10.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:10.397 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 9.2742ms
2025-06-24 10:51:10.398 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:10.400 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1555bytes
2025-06-24 10:51:10.404 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 31.9305ms
2025-06-24 10:51:20.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:20.692 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:20.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:20.697 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:20.698 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8972ms
2025-06-24 10:51:20.703 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:20.704 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:51:20.706 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.5668ms
2025-06-24 10:51:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:30.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3995ms
2025-06-24 10:51:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1555bytes
2025-06-24 10:51:30.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8659ms
2025-06-24 10:51:40.698 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:40.702 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:40.712 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:40.717 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:40.721 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:40.723 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3538ms
2025-06-24 10:51:40.724 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:40.725 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1553bytes
2025-06-24 10:51:40.726 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 28.1655ms
2025-06-24 10:51:50.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:51:50.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:51:50.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:51:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:51:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8607ms
2025-06-24 10:51:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:51:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:51:50.386 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8682ms
2025-06-24 10:52:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:00.692 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.6192ms
2025-06-24 10:52:00.697 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:00.705 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 18ms - 响应大小: 1553bytes
2025-06-24 10:52:00.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.4842ms
2025-06-24 10:52:10.366 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:10.368 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:10.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:10.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7606ms
2025-06-24 10:52:10.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:10.378 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1556bytes
2025-06-24 10:52:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.2774ms
2025-06-24 10:52:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:20.694 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:20.701 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:20.704 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:20.709 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:20.712 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.1402ms
2025-06-24 10:52:20.714 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:20.718 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 23ms - 响应大小: 1554bytes
2025-06-24 10:52:20.721 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 34.0385ms
2025-06-24 10:52:30.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:30.379 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:30.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4008ms
2025-06-24 10:52:30.381 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:30.382 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:52:30.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0055ms
2025-06-24 10:52:40.706 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:40.709 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:40.710 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:40.711 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:40.713 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:40.716 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.8168ms
2025-06-24 10:52:40.717 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:40.718 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:52:40.719 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.5635ms
2025-06-24 10:52:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:52:50.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:52:50.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:50.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:52:50.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:52:50.391 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.15ms
2025-06-24 10:52:50.394 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:52:50.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 19ms - 响应大小: 1556bytes
2025-06-24 10:52:50.399 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 25.5195ms
2025-06-24 10:53:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:00.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7157ms
2025-06-24 10:53:00.695 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:53:00.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.8089ms
2025-06-24 10:53:10.380 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:10.383 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:10.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:10.385 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:10.388 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:10.393 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.3875ms
2025-06-24 10:53:10.395 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:10.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1555bytes
2025-06-24 10:53:10.397 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.1242ms
2025-06-24 10:53:20.667 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:20.669 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:20.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:20.672 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:20.674 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:20.676 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.8013ms
2025-06-24 10:53:20.677 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:20.677 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:53:20.679 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.557ms
2025-06-24 10:53:30.374 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:30.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:30.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:30.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:30.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6983ms
2025-06-24 10:53:30.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:30.388 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:53:30.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.6374ms
2025-06-24 10:53:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:40.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:40.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:40.690 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:40.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:40.696 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.777ms
2025-06-24 10:53:40.697 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:40.698 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1556bytes
2025-06-24 10:53:40.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.5515ms
2025-06-24 10:53:50.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:53:50.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:53:50.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:50.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:53:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:53:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6914ms
2025-06-24 10:53:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:53:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1553bytes
2025-06-24 10:53:50.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.8238ms
2025-06-24 10:54:00.678 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:00.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:00.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:00.685 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:00.688 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:00.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5504ms
2025-06-24 10:54:00.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:00.692 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1555bytes
2025-06-24 10:54:00.694 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.8787ms
2025-06-24 10:54:10.374 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:10.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:10.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:10.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:10.386 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.3069ms
2025-06-24 10:54:10.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:10.388 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1552bytes
2025-06-24 10:54:10.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.6515ms
2025-06-24 10:54:20.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:20.690 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:20.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:20.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:20.696 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2653ms
2025-06-24 10:54:20.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:20.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 10:54:20.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.2861ms
2025-06-24 10:54:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:30.383 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:30.385 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:30.387 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4624ms
2025-06-24 10:54:30.388 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:30.389 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1555bytes
2025-06-24 10:54:30.391 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 20.063ms
2025-06-24 10:54:40.674 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:40.677 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:40.679 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:40.680 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:40.682 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:40.684 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4866ms
2025-06-24 10:54:40.685 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:40.686 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:54:40.693 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 18.8523ms
2025-06-24 10:54:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:54:50.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:54:50.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:50.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:54:50.384 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:54:50.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7358ms
2025-06-24 10:54:50.387 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:54:50.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1554bytes
2025-06-24 10:54:50.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.6334ms
2025-06-24 10:55:00.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:00.681 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:00.683 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:00.684 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:00.685 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:00.687 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5779ms
2025-06-24 10:55:00.688 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:00.689 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 10:55:00.691 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.5795ms
2025-06-24 10:55:10.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:10.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:10.382 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:10.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:10.384 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1249ms
2025-06-24 10:55:10.385 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:10.386 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:55:10.388 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.5047ms
2025-06-24 10:55:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:20.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:20.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:20.692 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:20.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:20.695 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7237ms
2025-06-24 10:55:20.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:20.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:55:20.698 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.1088ms
2025-06-24 10:55:30.372 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:30.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:30.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:30.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.358ms
2025-06-24 10:55:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:55:30.388 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.0931ms
2025-06-24 10:55:40.691 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:40.693 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:40.695 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:40.695 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:40.700 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:40.704 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.7841ms
2025-06-24 10:55:40.705 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:40.706 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1553bytes
2025-06-24 10:55:40.708 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8636ms
2025-06-24 10:55:50.380 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:55:50.382 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:55:50.384 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:50.386 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:55:50.405 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:55:50.408 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.5293ms
2025-06-24 10:55:50.409 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:55:50.410 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 27ms - 响应大小: 1556bytes
2025-06-24 10:55:50.412 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 32.4229ms
2025-06-24 10:56:00.682 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:00.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:00.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:00.688 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:00.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:00.691 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5778ms
2025-06-24 10:56:00.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:00.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:56:00.696 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.6544ms
2025-06-24 10:56:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:10.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:10.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:10.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:10.383 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3025ms
2025-06-24 10:56:10.384 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:10.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1554bytes
2025-06-24 10:56:10.389 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 20.3173ms
2025-06-24 10:56:20.692 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:20.695 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:20.698 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:20.699 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:20.701 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:20.702 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0727ms
2025-06-24 10:56:20.703 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:20.704 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:56:20.706 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.489ms
2025-06-24 10:56:30.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:30.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:30.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:30.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:30.387 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.7612ms
2025-06-24 10:56:30.394 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:30.396 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 22ms - 响应大小: 1556bytes
2025-06-24 10:56:30.398 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 27.4976ms
2025-06-24 10:56:40.680 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:40.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:40.685 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:40.687 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:40.689 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:40.691 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.449ms
2025-06-24 10:56:40.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:40.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1556bytes
2025-06-24 10:56:40.705 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 24.8616ms
2025-06-24 10:56:50.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:56:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:56:50.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:50.383 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:56:50.387 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:56:50.410 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 23.2436ms
2025-06-24 10:56:50.411 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:56:50.412 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 40ms - 响应大小: 1555bytes
2025-06-24 10:56:50.413 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 44.1005ms
2025-06-24 10:57:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:00.688 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:00.692 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:00.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:00.695 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:00.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9198ms
2025-06-24 10:57:00.700 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:00.701 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1556bytes
2025-06-24 10:57:00.702 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 17.9574ms
2025-06-24 10:57:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:10.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:10.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:10.372 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6725ms
2025-06-24 10:57:10.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:10.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:57:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.9007ms
2025-06-24 10:57:20.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:20.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:20.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:20.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:20.695 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:20.699 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 6.2359ms
2025-06-24 10:57:20.700 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:20.702 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 1556bytes
2025-06-24 10:57:20.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.6657ms
2025-06-24 10:57:30.366 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:30.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:30.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:30.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:30.373 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5211ms
2025-06-24 10:57:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:30.378 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:57:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.8584ms
2025-06-24 10:57:40.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:40.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:40.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:40.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:40.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:40.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5086ms
2025-06-24 10:57:40.698 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:40.699 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:57:40.700 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0506ms
2025-06-24 10:57:50.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:57:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:57:50.373 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:50.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:57:50.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:57:50.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.6626ms
2025-06-24 10:57:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:57:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1555bytes
2025-06-24 10:57:50.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.4352ms
2025-06-24 10:58:00.695 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:00.709 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:00.711 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:00.712 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:00.714 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:00.715 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1867ms
2025-06-24 10:58:00.716 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:00.717 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 10:58:00.721 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 26.0091ms
2025-06-24 10:58:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:10.374 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:10.376 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:10.377 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:10.379 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:10.380 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2521ms
2025-06-24 10:58:10.381 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:10.382 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1554bytes
2025-06-24 10:58:10.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.5848ms
2025-06-24 10:58:20.687 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:20.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:20.691 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:20.692 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:20.726 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:20.727 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5349ms
2025-06-24 10:58:20.728 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:20.730 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 39ms - 响应大小: 1555bytes
2025-06-24 10:58:20.731 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 43.8184ms
2025-06-24 10:58:30.367 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:30.379 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:30.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:30.382 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:30.386 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:30.388 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4605ms
2025-06-24 10:58:30.390 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:30.391 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1555bytes
2025-06-24 10:58:30.393 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 26.1873ms
2025-06-24 10:58:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:40.689 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:40.694 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:40.696 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:40.699 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:40.700 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4636ms
2025-06-24 10:58:40.701 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:40.702 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1556bytes
2025-06-24 10:58:40.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.8949ms
2025-06-24 10:58:50.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:58:50.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:58:50.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:50.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:58:50.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:58:50.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2097ms
2025-06-24 10:58:50.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:58:50.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:58:50.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.4558ms
2025-06-24 10:59:00.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:00.689 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:00.690 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:00.694 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:00.695 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.423ms
2025-06-24 10:59:00.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1553bytes
2025-06-24 10:59:00.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0113ms
2025-06-24 10:59:10.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:10.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:10.372 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:10.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:10.375 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:10.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3584ms
2025-06-24 10:59:10.377 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:10.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 10:59:10.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0334ms
2025-06-24 10:59:20.672 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:20.675 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:20.676 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:20.677 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:20.679 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:20.681 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4744ms
2025-06-24 10:59:20.682 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:20.682 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 10:59:20.687 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.891ms
2025-06-24 10:59:30.364 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:30.367 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:30.369 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:30.370 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:30.372 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.7971ms
2025-06-24 10:59:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:30.377 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 10:59:30.378 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.1933ms
2025-06-24 10:59:40.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:40.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:40.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:40.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:40.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:40.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4382ms
2025-06-24 10:59:40.695 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:40.696 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1555bytes
2025-06-24 10:59:40.698 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.7158ms
2025-06-24 10:59:50.370 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 10:59:50.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:59:50.374 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:50.375 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 10:59:50.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:59:50.378 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5596ms
2025-06-24 10:59:50.379 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 10:59:50.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1554bytes
2025-06-24 10:59:50.381 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.763ms
2025-06-24 11:00:00.686 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:00.690 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:00.693 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:00.699 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:00.704 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:00.709 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.1265ms
2025-06-24 11:00:00.712 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:00.716 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 25ms - 响应大小: 1556bytes
2025-06-24 11:00:00.722 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 35.9001ms
2025-06-24 11:00:10.375 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:10.378 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:10.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:10.380 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:10.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:10.383 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1246ms
2025-06-24 11:00:10.384 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:10.385 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1555bytes
2025-06-24 11:00:10.386 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.174ms
2025-06-24 11:00:20.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:20.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:20.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:20.686 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:20.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:20.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6686ms
2025-06-24 11:00:20.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:20.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1553bytes
2025-06-24 11:00:20.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.1087ms
2025-06-24 11:00:30.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:30.368 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:30.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:30.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:30.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:30.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7266ms
2025-06-24 11:00:30.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:30.377 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 11:00:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.0931ms
2025-06-24 11:00:40.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:40.681 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:40.683 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:40.683 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:40.686 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:40.688 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.7193ms
2025-06-24 11:00:40.689 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:40.691 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1556bytes
2025-06-24 11:00:40.703 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 23.9818ms
2025-06-24 11:00:50.365 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:00:50.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:00:50.371 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:50.372 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:00:50.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:00:50.375 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3346ms
2025-06-24 11:00:50.376 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:00:50.376 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 11:00:50.378 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.8532ms
2025-06-24 11:01:00.682 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:00.684 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:00.686 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:00.686 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:00.688 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:00.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3207ms
2025-06-24 11:01:00.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:00.691 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1553bytes
2025-06-24 11:01:00.692 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.0776ms
2025-06-24 11:01:10.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:10.373 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:10.375 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:10.376 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:10.377 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:10.378 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0747ms
2025-06-24 11:01:10.379 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:10.380 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:01:10.382 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.3173ms
2025-06-24 11:01:20.671 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:20.673 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:20.674 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:20.675 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:20.677 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:20.678 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2215ms
2025-06-24 11:01:20.679 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:20.680 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:01:20.683 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.1144ms
2025-06-24 11:01:30.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:30.371 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:30.375 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:30.378 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:30.380 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:30.381 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3207ms
2025-06-24 11:01:30.382 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:30.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 11ms - 响应大小: 1556bytes
2025-06-24 11:01:30.384 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.7532ms
2025-06-24 11:01:40.696 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:40.699 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:40.701 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:40.703 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:40.705 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:40.706 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4404ms
2025-06-24 11:01:40.707 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:40.708 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1553bytes
2025-06-24 11:01:40.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.2227ms
2025-06-24 11:01:50.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:01:50.369 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:01:50.370 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:50.371 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:01:50.372 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:01:50.372 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6482ms
2025-06-24 11:01:50.373 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:01:50.374 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1556bytes
2025-06-24 11:01:50.376 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 7.3489ms
2025-06-24 11:02:00.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:00.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:00.688 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:00.689 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:00.690 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:00.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9127ms
2025-06-24 11:02:00.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:00.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 11:02:00.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.0423ms
2025-06-24 11:02:10.410 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:10.413 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:10.414 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:10.416 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:10.419 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:10.444 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 25.4542ms
2025-06-24 11:02:10.446 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:10.449 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 35ms - 响应大小: 1553bytes
2025-06-24 11:02:10.451 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 40.5867ms
2025-06-24 11:02:20.671 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:20.674 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:20.675 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:20.677 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:20.678 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:20.679 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0788ms
2025-06-24 11:02:20.680 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:20.681 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1556bytes
2025-06-24 11:02:20.682 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.8019ms
2025-06-24 11:02:30.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:30.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:30.379 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:30.381 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:30.383 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:30.385 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.9539ms
2025-06-24 11:02:30.388 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:30.389 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1555bytes
2025-06-24 11:02:30.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 22.254ms
2025-06-24 11:02:40.689 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:40.692 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:40.693 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:40.694 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:40.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:40.697 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5212ms
2025-06-24 11:02:40.698 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:40.700 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1556bytes
2025-06-24 11:02:40.702 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.5965ms
2025-06-24 11:02:50.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:02:50.370 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:02:50.372 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:50.373 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:02:50.374 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:02:50.376 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.5236ms
2025-06-24 11:02:50.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:02:50.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1555bytes
2025-06-24 11:02:50.381 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.563ms
2025-06-24 11:03:00.685 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:00.687 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:00.690 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:00.691 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:00.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:00.694 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.302ms
2025-06-24 11:03:00.696 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:00.697 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1554bytes
2025-06-24 11:03:00.699 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.4964ms
2025-06-24 11:03:10.373 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:10.376 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:10.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:10.379 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:10.381 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:10.382 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.1544ms
2025-06-24 11:03:10.383 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:10.383 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1556bytes
2025-06-24 11:03:10.385 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.4205ms
2025-06-24 11:03:20.679 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:20.682 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:20.684 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:20.685 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:20.687 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:20.689 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3175ms
2025-06-24 11:03:20.690 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:20.690 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1554bytes
2025-06-24 11:03:20.693 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.5809ms
2025-06-24 11:03:30.369 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:30.372 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:30.373 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:30.374 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:30.376 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:30.377 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.2346ms
2025-06-24 11:03:30.378 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:30.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1553bytes
2025-06-24 11:03:30.380 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.2136ms
2025-06-24 11:03:40.683 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:03:40.686 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:40.687 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:40.688 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:03:40.691 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:03:40.692 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3859ms
2025-06-24 11:03:40.693 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:03:40.694 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1555bytes
2025-06-24 11:03:40.695 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.3659ms
2025-06-24 11:03:45.135 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:03:45.139 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:45.140 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:45.145 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:03:45.523 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:03:45.541 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:45.558 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 410.768ms
2025-06-24 11:03:45.560 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:45.561 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 421ms - 响应大小: 328bytes
2025-06-24 11:03:45.562 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 427.217ms
2025-06-24 11:03:55.977 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 11:03:55.980 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:55.983 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:03:55.985 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 11:03:56.360 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:03:56.364 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:56.365 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 378.9363ms
2025-06-24 11:03:56.367 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:03:56.368 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 387ms - 响应大小: 6656bytes
2025-06-24 11:03:56.369 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 391.986ms
2025-06-24 11:03:56.745 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 11:03:56.748 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:56.751 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-24 11:03:56.753 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 7.2664ms
2025-06-24 11:03:56.755 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 11:03:56.790 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:03:56.792 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:03:56.796 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:56.797 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:03:57.078 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:03:57.080 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:03:57.081 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 281.8292ms
2025-06-24 11:03:57.082 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:03:57.083 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 290ms - 响应大小: 328bytes
2025-06-24 11:03:57.084 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 294.9252ms
2025-06-24 11:06:33.815 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 11:06:33.816 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:06:33.817 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:06:33.818 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:06:34.239 +08:00 [INF] Executed DbCommand (48ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:06:34.314 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:06:34.315 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:06:34.316 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 496.5249ms
2025-06-24 11:06:34.316 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:06:34.317 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 500ms - 响应大小: 810bytes
2025-06-24 11:06:34.317 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 502.7074ms
2025-06-24 11:08:38.842 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 11:08:38.845 +08:00 [INF] CORS policy execution successful.
2025-06-24 11:08:38.846 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:08:38.847 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:08:38.849 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:08:39.093 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:08:39.153 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:08:39.157 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:08:39.159 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 308.6254ms
2025-06-24 11:08:39.160 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:08:39.161 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 314ms - 响应大小: 810bytes
2025-06-24 11:08:39.164 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 321.8041ms
2025-06-24 11:09:48.288 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 11:09:48.289 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:09:48.291 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:09:48.292 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:09:48.497 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:09:48.556 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:09:48.557 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:09:48.558 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 264.6423ms
2025-06-24 11:09:48.559 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:09:48.559 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 269ms - 响应大小: 810bytes
2025-06-24 11:09:48.560 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 272.2328ms
2025-06-24 11:10:27.695 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 11:10:27.696 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:10:27.697 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:10:27.698 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:10:28.138 +08:00 [INF] Executed DbCommand (44ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:10:28.242 +08:00 [INF] Executed DbCommand (86ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:10:28.243 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:10:28.244 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 545.2676ms
2025-06-24 11:10:28.245 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:10:28.246 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 549ms - 响应大小: 1576bytes
2025-06-24 11:10:28.247 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 552.0433ms
2025-06-24 11:11:46.217 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:11:46.226 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:11:46.228 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:11:46.229 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:11:46.333 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:11:46.340 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:11:46.343 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 112.981ms
2025-06-24 11:11:46.344 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:11:46.346 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 119ms - 响应大小: 328bytes
2025-06-24 11:11:46.347 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 130.8278ms
2025-06-24 11:13:08.684 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:13:08.686 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:13:08.687 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:13:08.688 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:13:09.218 +08:00 [INF] Executed DbCommand (64ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:13:09.220 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:13:09.221 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 531.7658ms
2025-06-24 11:13:09.221 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:13:09.222 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 535ms - 响应大小: 328bytes
2025-06-24 11:13:09.223 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 538.8992ms
2025-06-24 11:13:38.915 +08:00 [INF] 使用内存存储服务
2025-06-24 11:13:40.050 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 11:13:40.059 +08:00 [INF] 数据库和表创建成功
2025-06-24 11:13:40.215 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 11:13:40.220 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 11:13:40.286 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 11:13:40.322 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 11:13:40.325 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 11:13:40.325 +08:00 [INF] Hosting environment: Development
2025-06-24 11:13:40.326 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 11:14:22.726 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:14:22.757 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:14:22.761 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 11:14:22.762 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:14:22.776 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:14:22.980 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:14:23.011 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:14:23.054 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 275.8348ms
2025-06-24 11:14:23.056 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:14:23.057 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 301ms - 响应大小: 347bytes
2025-06-24 11:14:23.062 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 336.8237ms
2025-06-24 11:15:09.756 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 11:15:09.760 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:09.762 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:15:09.766 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 11:15:10.059 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:15:10.083 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:10.092 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 325.5057ms
2025-06-24 11:15:10.093 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:15:10.094 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 333ms - 响应大小: 6656bytes
2025-06-24 11:15:10.095 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 338.541ms
2025-06-24 11:15:10.589 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - null null
2025-06-24 11:15:10.593 +08:00 [INF] 请求开始: GET /static/avatar/91686750225ab13d1e7fe480.png - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:10.594 +08:00 [INF] 请求完成: GET /static/avatar/91686750225ab13d1e7fe480.png - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:15:10.595 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png - 404 0 null 6.3911ms
2025-06-24 11:15:10.598 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/static/avatar/91686750225ab13d1e7fe480.png, Response status code: 404
2025-06-24 11:15:10.624 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:15:10.625 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:10.627 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:10.627 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:10.986 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:10.989 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:10.990 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 361.4631ms
2025-06-24 11:15:10.990 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:10.991 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 365ms - 响应大小: 347bytes
2025-06-24 11:15:10.992 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 367.791ms
2025-06-24 11:15:16.642 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - null null
2025-06-24 11:15:16.646 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:16.649 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:16.652 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:17.067 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:17.071 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:17.072 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 417.8578ms
2025-06-24 11:15:17.074 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:17.076 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/2 - 状态码: 200 - 耗时: 429ms - 响应大小: 531bytes
2025-06-24 11:15:17.078 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - 200 null application/json; charset=utf-8 435.2857ms
2025-06-24 11:15:17.395 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:15:17.398 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:17.399 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:17.409 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:15:17.849 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:15:18.013 +08:00 [INF] Executed DbCommand (84ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:15:18.027 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:15:18.063 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 652.3907ms
2025-06-24 11:15:18.073 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:18.074 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 676ms - 响应大小: 1576bytes
2025-06-24 11:15:18.076 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 680.9371ms
2025-06-24 11:15:22.677 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:15:22.680 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:22.682 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:22.683 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:15:23.175 +08:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:15:23.312 +08:00 [INF] Executed DbCommand (73ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:15:23.316 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:15:23.320 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 632.4952ms
2025-06-24 11:15:23.322 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:23.323 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 642ms - 响应大小: 1576bytes
2025-06-24 11:15:23.324 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 647.7425ms
2025-06-24 11:15:25.985 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - null null
2025-06-24 11:15:25.988 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:25.990 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:25.991 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:26.244 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:26.247 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:26.250 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 256.405ms
2025-06-24 11:15:26.252 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:26.256 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/7 - 状态码: 200 - 耗时: 267ms - 响应大小: 268bytes
2025-06-24 11:15:26.258 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - 200 null application/json; charset=utf-8 272.6774ms
2025-06-24 11:15:26.264 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - null null
2025-06-24 11:15:26.267 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:26.272 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:26.273 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:26.862 +08:00 [INF] Executed DbCommand (103ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:26.864 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:26.865 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 590.3829ms
2025-06-24 11:15:26.867 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:26.868 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/8 - 状态码: 200 - 耗时: 601ms - 响应大小: 431bytes
2025-06-24 11:15:26.894 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - 200 null application/json; charset=utf-8 629.6701ms
2025-06-24 11:15:26.900 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - null null
2025-06-24 11:15:26.905 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:26.907 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:26.908 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:27.310 +08:00 [INF] Executed DbCommand (99ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:27.311 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:27.312 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 402.248ms
2025-06-24 11:15:27.313 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:27.313 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/9 - 状态码: 200 - 耗时: 408ms - 响应大小: 1014bytes
2025-06-24 11:15:27.314 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - 200 null application/json; charset=utf-8 413.929ms
2025-06-24 11:15:27.324 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:15:27.326 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:27.328 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:27.329 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:27.820 +08:00 [INF] Executed DbCommand (67ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:27.825 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:27.829 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 498.6531ms
2025-06-24 11:15:27.832 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:27.834 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 507ms - 响应大小: 347bytes
2025-06-24 11:15:27.838 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 513.7608ms
2025-06-24 11:15:28.160 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-24 11:15:28.162 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:28.164 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:28.164 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:15:28.571 +08:00 [INF] Executed DbCommand (81ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:15:28.812 +08:00 [INF] Executed DbCommand (226ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:15:28.816 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:15:28.818 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 651.6818ms
2025-06-24 11:15:28.822 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:15:28.825 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 662ms - 响应大小: 1576bytes
2025-06-24 11:15:28.828 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 668.2999ms
2025-06-24 11:15:28.839 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - null null
2025-06-24 11:15:28.842 +08:00 [INF] 请求开始: GET /api/devices/props/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:28.845 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:15:28.850 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:15:29.195 +08:00 [INF] Executed DbCommand (81ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-24 11:15:29.199 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:29.204 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 347.1459ms
2025-06-24 11:15:29.205 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:15:29.208 +08:00 [INF] 请求完成: GET /api/devices/props/7 - 状态码: 200 - 耗时: 365ms - 响应大小: 36bytes
2025-06-24 11:15:29.210 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - 200 null application/json; charset=utf-8 370.5792ms
2025-06-24 11:15:32.771 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - null null
2025-06-24 11:15:32.776 +08:00 [INF] 请求开始: GET /api/devices/props/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:32.777 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:15:32.779 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:15:32.980 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-24 11:15:32.983 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:32.984 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 203.6818ms
2025-06-24 11:15:32.987 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:15:32.988 +08:00 [INF] 请求完成: GET /api/devices/props/7 - 状态码: 200 - 耗时: 211ms - 响应大小: 36bytes
2025-06-24 11:15:32.989 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - 200 null application/json; charset=utf-8 218.1523ms
2025-06-24 11:15:39.414 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - null null
2025-06-24 11:15:39.416 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:39.418 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:39.420 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:39.723 +08:00 [INF] Executed DbCommand (79ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:39.728 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:39.729 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 305.8071ms
2025-06-24 11:15:39.730 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:39.731 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/10 - 状态码: 200 - 耗时: 314ms - 响应大小: 377bytes
2025-06-24 11:15:39.732 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - 200 null application/json; charset=utf-8 318.327ms
2025-06-24 11:15:39.744 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:15:39.746 +08:00 [INF] 请求开始: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:39.748 +08:00 [INF] 请求完成: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:15:39.750 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 404 0 null 5.8439ms
2025-06-24 11:15:39.758 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/actgroups, Response status code: 404
2025-06-24 11:15:49.421 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - null null
2025-06-24 11:15:49.423 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:49.424 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:49.424 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:15:49.692 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:15:49.693 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:15:49.694 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 268.582ms
2025-06-24 11:15:49.694 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:15:49.695 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/11 - 状态码: 200 - 耗时: 272ms - 响应大小: 362bytes
2025-06-24 11:15:49.696 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - 200 null application/json; charset=utf-8 274.6123ms
2025-06-24 11:15:49.701 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups - null null
2025-06-24 11:15:49.705 +08:00 [INF] 请求开始: GET /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:49.707 +08:00 [INF] 请求完成: GET /api/task/actgroups - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-24 11:15:49.708 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups - 404 0 null 6.91ms
2025-06-24 11:15:49.710 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/actgroups, Response status code: 404
2025-06-24 11:15:57.368 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-24 11:15:57.370 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:15:57.371 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 0ms - 响应大小: 0bytes
2025-06-24 11:15:57.372 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 404 0 null 3.9507ms
2025-06-24 11:15:57.374 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/paths, Response status code: 404
2025-06-24 11:16:01.823 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - null null
2025-06-24 11:16:01.826 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/5 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:01.827 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:01.827 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:16:02.112 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:16:02.113 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:02.114 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 285.6614ms
2025-06-24 11:16:02.115 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:02.115 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/5 - 状态码: 200 - 耗时: 289ms - 响应大小: 194bytes
2025-06-24 11:16:02.117 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - 200 null application/json; charset=utf-8 293.1551ms
2025-06-24 11:16:02.129 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:16:02.130 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:02.131 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-24 11:16:02.134 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.Models.Work]]]] GetWorks(Int32, Int32, System.Nullable`1[System.UInt64], System.Nullable`1[System.SByte]) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-24 11:16:02.343 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-24 11:16:02.368 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:02.385 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:02.390 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 255.0987ms
2025-06-24 11:16:02.391 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-24 11:16:02.392 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 261ms - 响应大小: 5728bytes
2025-06-24 11:16:02.393 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 263.879ms
2025-06-24 11:16:15.752 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:16:15.753 +08:00 [INF] 请求开始: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:15.754 +08:00 [INF] 请求完成: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 0ms - 响应大小: 0bytes
2025-06-24 11:16:15.755 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 404 0 null 3.1982ms
2025-06-24 11:16:15.757 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/apilogs, Response status code: 404
2025-06-24 11:16:31.553 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:16:31.553 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-24 11:16:31.554 +08:00 [INF] 请求开始: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:31.555 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:31.556 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:16:31.557 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:16:31.559 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:16:31.560 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:16:31.562 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:31.568 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 7.4179ms
2025-06-24 11:16:31.569 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:16:31.569 +08:00 [INF] 请求完成: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 14ms - 响应大小: 1556bytes
2025-06-24 11:16:31.570 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 17.6915ms
2025-06-24 11:16:31.865 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:16:31.959 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:31.960 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:31.961 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 401.6787ms
2025-06-24 11:16:31.962 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:16:31.963 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 407ms - 响应大小: 1576bytes
2025-06-24 11:16:31.964 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 411.3555ms
2025-06-24 11:16:36.131 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - null null
2025-06-24 11:16:36.133 +08:00 [INF] 请求开始: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:36.134 +08:00 [INF] 请求完成: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:16:36.135 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 404 0 null 3.5922ms
2025-06-24 11:16:36.137 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/lanes, Response status code: 404
2025-06-24 11:16:39.662 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/6 - null null
2025-06-24 11:16:39.664 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/6 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:39.665 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:39.665 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:16:40.013 +08:00 [INF] Executed DbCommand (67ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:16:40.015 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:40.016 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 350.2214ms
2025-06-24 11:16:40.018 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:40.019 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/6 - 状态码: 200 - 耗时: 355ms - 响应大小: 282bytes
2025-06-24 11:16:40.022 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/6 - 200 null application/json; charset=utf-8 359.8312ms
2025-06-24 11:16:40.030 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/12 - null null
2025-06-24 11:16:40.031 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/12 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:40.032 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:40.032 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:16:40.277 +08:00 [INF] Executed DbCommand (62ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:16:40.279 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:40.280 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 247.4891ms
2025-06-24 11:16:40.281 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:16:40.282 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/12 - 状态码: 200 - 耗时: 251ms - 响应大小: 341bytes
2025-06-24 11:16:40.283 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/12 - 200 null application/json; charset=utf-8 253.5108ms
2025-06-24 11:16:40.291 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stocks?stock_name=&status=&abc_type=&page_size=10&page_no=1 - null null
2025-06-24 11:16:40.292 +08:00 [INF] 请求开始: GET /api/stocks?stock_name=&status=&abc_type=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:40.293 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StockController.GetStocks (WcsNet)'
2025-06-24 11:16:40.295 +08:00 [INF] Route matched with {action = "GetStocks", controller = "Stock"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.Models.Stock]]]] GetStocks(Int32, Int32, System.String, System.Nullable`1[System.SByte]) on controller WcsNet.Controllers.StockController (WcsNet).
2025-06-24 11:16:40.570 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_stock` AS `w`
2025-06-24 11:16:40.742 +08:00 [INF] Executed DbCommand (65ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`abc_type`, `w`.`created_at`, `w`.`created_id`, `w`.`Deep`, `w`.`goods_code`, `w`.`lane_code`, `w`.`Layer`, `w`.`Shelves`, `w`.`Side`, `w`.`Status`, `w`.`stock_code`, `w`.`stock_name`, `w`.`updated_at`, `w`.`updated_id`, `w`.`X`, `w`.`Y`, `w`.`Z`
FROM `wcs_stock` AS `w`
ORDER BY `w`.`lane_code`, `w`.`X`, `w`.`Y`, `w`.`Z`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:40.756 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:40.759 +08:00 [INF] Executed action WcsNet.Controllers.StockController.GetStocks (WcsNet) in 463.2652ms
2025-06-24 11:16:40.760 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StockController.GetStocks (WcsNet)'
2025-06-24 11:16:40.760 +08:00 [INF] 请求完成: GET /api/stocks?stock_name=&status=&abc_type=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 468ms - 响应大小: 5421bytes
2025-06-24 11:16:40.761 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stocks?stock_name=&status=&abc_type=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 470.785ms
2025-06-24 11:16:43.763 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/lanes - null null
2025-06-24 11:16:43.764 +08:00 [INF] 请求开始: GET /api/lanes - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:43.765 +08:00 [INF] 请求完成: GET /api/lanes - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:16:43.766 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/lanes - 404 0 null 3.5836ms
2025-06-24 11:16:43.769 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/lanes, Response status code: 404
2025-06-24 11:16:50.315 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 11:16:50.317 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:50.318 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 11:16:50.321 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 11:16:50.703 +08:00 [INF] Executed DbCommand (111ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:16:50.704 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:50.707 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 385.1814ms
2025-06-24 11:16:50.707 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 11:16:50.708 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 390ms - 响应大小: 336bytes
2025-06-24 11:16:50.709 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 393.3296ms
2025-06-24 11:16:50.732 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:16:50.732 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 11:16:50.733 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:50.734 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:50.735 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 11:16:50.736 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 11:16:50.739 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 11:16:50.739 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 11:16:51.172 +08:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 11:16:51.176 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 11:16:51.177 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 11:16:51.389 +08:00 [INF] Executed DbCommand (185ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:51.390 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:51.396 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 654.9801ms
2025-06-24 11:16:51.396 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 11:16:51.397 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 663ms - 响应大小: 452bytes
2025-06-24 11:16:51.398 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 666.2043ms
2025-06-24 11:16:51.498 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 11:16:51.499 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:51.503 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 762.7007ms
2025-06-24 11:16:51.503 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 11:16:51.504 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 769ms - 响应大小: 201bytes
2025-06-24 11:16:51.505 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 772.8627ms
2025-06-24 11:16:52.923 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:16:52.923 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menulist - null null
2025-06-24 11:16:52.925 +08:00 [INF] 请求开始: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:52.926 +08:00 [INF] 请求开始: GET /api/sys/menulist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:52.926 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 11:16:52.928 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 11:16:52.931 +08:00 [INF] Route matched with {action = "GetMenuList", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetMenuList() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 11:16:52.932 +08:00 [INF] Route matched with {action = "GetRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetRoles(Int32, Int32, System.String) on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 11:16:53.133 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_role` AS `s`
2025-06-24 11:16:53.136 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`title` AS `Title`, `s`.`parent_id` AS `ParentId`, `s`.`perm_type` AS `PermType`, `s`.`icon` AS `Icon`, `s`.`rank` AS `Rank`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:16:53.164 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:53.174 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenuList (WcsNet) in 240.8971ms
2025-06-24 11:16:53.174 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 11:16:53.175 +08:00 [INF] 请求完成: GET /api/sys/menulist - 状态码: 200 - 耗时: 249ms - 响应大小: 2871bytes
2025-06-24 11:16:53.176 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menulist - 200 null application/json; charset=utf-8 252.8046ms
2025-06-24 11:16:53.232 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`, `s`.`role_type` AS `RoleType`, `s`.`sort_number` AS `SortNumber`, `s`.`status` AS `Status`, `s`.`remark` AS `Remark`, `s`.`perms` AS `Perms`
FROM `sys_role` AS `s`
ORDER BY `s`.`sort_number`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:53.236 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:53.244 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetRoles (WcsNet) in 310.0006ms
2025-06-24 11:16:53.244 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 11:16:53.245 +08:00 [INF] 请求完成: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 320ms - 响应大小: 561bytes
2025-06-24 11:16:53.246 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 322.5694ms
2025-06-24 11:16:54.513 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - null null
2025-06-24 11:16:54.514 +08:00 [INF] 请求开始: GET /api/sys/menus?title= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:54.515 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 11:16:54.518 +08:00 [INF] Route matched with {action = "GetMenus", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetMenus() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 11:16:54.786 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:16:54.792 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:54.793 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenus (WcsNet) in 274.6235ms
2025-06-24 11:16:54.794 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 11:16:54.795 +08:00 [INF] 请求完成: GET /api/sys/menus?title= - 状态码: 200 - 耗时: 280ms - 响应大小: 6958bytes
2025-06-24 11:16:54.796 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - 200 null application/json; charset=utf-8 283.2363ms
2025-06-24 11:16:56.526 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - null null
2025-06-24 11:16:56.528 +08:00 [INF] 请求开始: GET /api/sys/depts?name=&status= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:56.528 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 11:16:56.530 +08:00 [INF] Route matched with {action = "GetDepts", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDepts() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 11:16:56.951 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:16:56.965 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:16:56.967 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDepts (WcsNet) in 435.5475ms
2025-06-24 11:16:56.968 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 11:16:56.969 +08:00 [INF] 请求完成: GET /api/sys/depts?name=&status= - 状态码: 200 - 耗时: 441ms - 响应大小: 1011bytes
2025-06-24 11:16:56.971 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - 200 null application/json; charset=utf-8 444.6901ms
2025-06-24 11:16:58.165 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:16:58.166 +08:00 [INF] 请求开始: GET /api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:58.167 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDicts (WcsNet)'
2025-06-24 11:16:58.170 +08:00 [INF] Route matched with {action = "GetDicts", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetDicts(Int32, Int32, System.String) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:16:58.298 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_dict` AS `s`
2025-06-24 11:16:58.352 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_code`, `s`.`dict_name`, `s`.`Remark`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict` AS `s`
ORDER BY `s`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:58.360 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:58.362 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDicts (WcsNet) in 190.9925ms
2025-06-24 11:16:58.363 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDicts (WcsNet)'
2025-06-24 11:16:58.363 +08:00 [INF] 请求完成: GET /api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 196ms - 响应大小: 1042bytes
2025-06-24 11:16:58.364 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 199.1865ms
2025-06-24 11:16:58.367 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:16:58.368 +08:00 [INF] 请求开始: GET /api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:58.369 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDicts (WcsNet)'
2025-06-24 11:16:58.370 +08:00 [INF] Route matched with {action = "GetDicts", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetDicts(Int32, Int32, System.String) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:16:58.545 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_dict` AS `s`
2025-06-24 11:16:58.594 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_code`, `s`.`dict_name`, `s`.`Remark`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict` AS `s`
ORDER BY `s`.`Id`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:16:58.596 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:16:58.596 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDicts (WcsNet) in 225.3414ms
2025-06-24 11:16:58.597 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDicts (WcsNet)'
2025-06-24 11:16:58.597 +08:00 [INF] 请求完成: GET /api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 228ms - 响应大小: 1042bytes
2025-06-24 11:16:58.598 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dicts?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 231.0456ms
2025-06-24 11:16:59.907 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/confs?name=&key=&type=&page_size=10&page_no=1 - null null
2025-06-24 11:16:59.908 +08:00 [INF] 请求开始: GET /api/sys/confs?name=&key=&type=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:16:59.909 +08:00 [INF] 请求完成: GET /api/sys/confs?name=&key=&type=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 0ms - 响应大小: 0bytes
2025-06-24 11:16:59.910 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/confs?name=&key=&type=&page_size=10&page_no=1 - 404 0 null 2.9312ms
2025-06-24 11:16:59.911 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/sys/confs, Response status code: 404
2025-06-24 11:17:04.674 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/logs/loginlog?username=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:17:04.675 +08:00 [INF] 请求开始: GET /api/sys/logs/loginlog?username=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:17:04.676 +08:00 [INF] 请求完成: GET /api/sys/logs/loginlog?username=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:17:04.684 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/logs/loginlog?username=&status=&start_time=&end_time=&page_size=10&page_no=1 - 404 0 null 10.216ms
2025-06-24 11:17:04.693 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/sys/logs/loginlog, Response status code: 404
2025-06-24 11:17:12.840 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/logs/operation?module=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:17:14.822 +08:00 [INF] 请求开始: GET /api/sys/logs/operation?module=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:17:15.333 +08:00 [INF] 请求完成: GET /api/sys/logs/operation?module=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 510ms - 响应大小: 0bytes
2025-06-24 11:17:16.090 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/logs/operation?module=&status=&start_time=&end_time=&page_size=10&page_no=1 - 404 0 null 3250.3148ms
2025-06-24 11:17:16.308 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/sys/logs/operation, Response status code: 404
2025-06-24 11:20:12.412 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 11:20:12.414 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:12.416 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:20:12.417 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 11:20:12.640 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 11:20:12.642 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:12.643 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 224.1178ms
2025-06-24 11:20:12.644 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 11:20:12.644 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 230ms - 响应大小: 6656bytes
2025-06-24 11:20:12.646 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 234.2274ms
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 11:20:14.108 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 11:20:14.109 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.110 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.112 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.112 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.114 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.117 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:14.120 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.125 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:20:14.126 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.128 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.130 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 11:20:14.131 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 11:20:14.134 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 11:20:14.135 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:20:14.136 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 11:20:14.136 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 11:20:14.138 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 11:20:14.138 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 11:20:14.139 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.140 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.141 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.141 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.143 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.143 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:14.145 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.3058ms
2025-06-24 11:20:14.146 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 5.3318ms
2025-06-24 11:20:14.146 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.3926ms
2025-06-24 11:20:14.147 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 5.1939ms
2025-06-24 11:20:14.147 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 4.7695ms
2025-06-24 11:20:14.148 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 4.4074ms
2025-06-24 11:20:14.148 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:20:14.149 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.150 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.150 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 11:20:14.151 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 11:20:14.152 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 11:20:14.152 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 41ms - 响应大小: 1555bytes
2025-06-24 11:20:14.153 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 40ms - 响应大小: 577bytes
2025-06-24 11:20:14.153 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 43ms - 响应大小: 801bytes
2025-06-24 11:20:14.153 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 41ms - 响应大小: 1101bytes
2025-06-24 11:20:14.154 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 40ms - 响应大小: 576bytes
2025-06-24 11:20:14.154 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 37ms - 响应大小: 569bytes
2025-06-24 11:20:14.155 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 47.4533ms
2025-06-24 11:20:14.156 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 48.3175ms
2025-06-24 11:20:14.158 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 49.9634ms
2025-06-24 11:20:14.159 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 51.0306ms
2025-06-24 11:20:14.160 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 52.0349ms
2025-06-24 11:20:14.161 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 52.9226ms
2025-06-24 11:20:18.940 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:20:18.942 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:18.944 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:18.946 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:19.063 +08:00 [INF] Executed DbCommand (4ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:19.065 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:19.066 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 114.5876ms
2025-06-24 11:20:19.070 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:19.073 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 131ms - 响应大小: 347bytes
2025-06-24 11:20:19.075 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 135.1086ms
2025-06-24 11:20:19.088 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - null null
2025-06-24 11:20:19.099 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:19.104 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:19.105 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:19.273 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:19.276 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:19.277 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 170.4433ms
2025-06-24 11:20:19.278 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:19.280 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/2 - 状态码: 200 - 耗时: 180ms - 响应大小: 531bytes
2025-06-24 11:20:19.281 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/2 - 200 null application/json; charset=utf-8 193.2765ms
2025-06-24 11:20:19.295 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:20:19.311 +08:00 [INF] 请求开始: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:19.320 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:19.323 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:20:19.441 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:20:19.482 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:20:19.488 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:19.490 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 164.8367ms
2025-06-24 11:20:19.490 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:19.491 +08:00 [INF] 请求完成: GET /api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 180ms - 响应大小: 1576bytes
2025-06-24 11:20:19.493 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?device_name=&device_code=&device_type=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 198.4647ms
2025-06-24 11:20:21.920 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - null null
2025-06-24 11:20:21.922 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:21.924 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:21.932 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:22.123 +08:00 [INF] Executed DbCommand (62ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:22.126 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:22.132 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 191.6761ms
2025-06-24 11:20:22.133 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.135 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/7 - 状态码: 200 - 耗时: 212ms - 响应大小: 268bytes
2025-06-24 11:20:22.137 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/7 - 200 null application/json; charset=utf-8 216.9779ms
2025-06-24 11:20:22.144 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - null null
2025-06-24 11:20:22.147 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:22.152 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.153 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:22.250 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:22.255 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:22.256 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 101.8777ms
2025-06-24 11:20:22.258 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.259 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/8 - 状态码: 200 - 耗时: 111ms - 响应大小: 431bytes
2025-06-24 11:20:22.260 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/8 - 200 null application/json; charset=utf-8 116.0016ms
2025-06-24 11:20:22.265 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - null null
2025-06-24 11:20:22.270 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/9 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:22.272 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.274 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:22.464 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:22.467 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:22.471 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 194.9418ms
2025-06-24 11:20:22.472 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.473 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/9 - 状态码: 200 - 耗时: 202ms - 响应大小: 1014bytes
2025-06-24 11:20:22.474 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/9 - 200 null application/json; charset=utf-8 209.4408ms
2025-06-24 11:20:22.797 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:20:22.799 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:22.802 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.803 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:22.951 +08:00 [INF] Executed DbCommand (21ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:22.971 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:22.972 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 168.1912ms
2025-06-24 11:20:22.973 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:22.974 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 175ms - 响应大小: 347bytes
2025-06-24 11:20:22.976 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 179.2036ms
2025-06-24 11:20:23.056 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-24 11:20:23.060 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:23.063 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:23.064 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:20:23.203 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:20:23.252 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:20:23.256 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:23.257 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 191.7785ms
2025-06-24 11:20:23.259 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:23.261 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 200ms - 响应大小: 1576bytes
2025-06-24 11:20:23.262 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 206.4827ms
2025-06-24 11:20:23.276 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - null null
2025-06-24 11:20:23.278 +08:00 [INF] 请求开始: GET /api/devices/props/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:23.279 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:20:23.280 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:20:23.394 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-24 11:20:23.397 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:23.398 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 116.666ms
2025-06-24 11:20:23.404 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 11:20:23.406 +08:00 [INF] 请求完成: GET /api/devices/props/7 - 状态码: 200 - 耗时: 128ms - 响应大小: 36bytes
2025-06-24 11:20:23.408 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/7 - 200 null application/json; charset=utf-8 131.7946ms
2025-06-24 11:20:23.750 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - null null
2025-06-24 11:20:23.753 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:23.754 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:23.755 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:23.946 +08:00 [INF] Executed DbCommand (71ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:23.954 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:23.955 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 197.795ms
2025-06-24 11:20:23.956 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:23.957 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/10 - 状态码: 200 - 耗时: 204ms - 响应大小: 377bytes
2025-06-24 11:20:23.958 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - 200 null application/json; charset=utf-8 208.6724ms
2025-06-24 11:20:23.965 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - null null
2025-06-24 11:20:23.971 +08:00 [INF] 请求开始: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:23.973 +08:00 [INF] 请求完成: GET /api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:20:23.975 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups?group_name=&group_type=&status=&page_size=10&page_no=1 - 404 0 null 9.4527ms
2025-06-24 11:20:24.000 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/actgroups, Response status code: 404
2025-06-24 11:20:27.099 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - null null
2025-06-24 11:20:27.103 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/11 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:27.104 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:27.106 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:27.302 +08:00 [INF] Executed DbCommand (29ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:27.305 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:27.306 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 191.45ms
2025-06-24 11:20:27.308 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:27.309 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/11 - 状态码: 200 - 耗时: 206ms - 响应大小: 362bytes
2025-06-24 11:20:27.319 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/11 - 200 null application/json; charset=utf-8 220.2747ms
2025-06-24 11:20:27.336 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/actgroups - null null
2025-06-24 11:20:28.412 +08:00 [INF] 请求开始: GET /api/task/actgroups - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:28.754 +08:00 [INF] 请求完成: GET /api/task/actgroups - 状态码: 404 - 耗时: 342ms - 响应大小: 0bytes
2025-06-24 11:20:28.897 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/actgroups - 404 0 null 1561.1209ms
2025-06-24 11:20:29.096 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/actgroups, Response status code: 404
2025-06-24 11:20:29.481 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - null null
2025-06-24 11:20:29.485 +08:00 [INF] 请求开始: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:29.487 +08:00 [INF] 请求完成: GET /api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:20:29.489 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/task/paths?path_name=&start_point=&end_point=&page_size=10&page_no=1 - 404 0 null 8.2105ms
2025-06-24 11:20:29.495 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/task/paths, Response status code: 404
2025-06-24 11:20:31.545 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 11:20:31.557 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 11:20:31.563 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:31.564 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:31.774 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:31.777 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:31.778 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 207.7702ms
2025-06-24 11:20:31.779 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:31.781 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 223ms - 响应大小: 347bytes
2025-06-24 11:20:31.792 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 247.3142ms
2025-06-24 11:20:32.192 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - null null
2025-06-24 11:20:32.198 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/5 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:32.213 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:32.232 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 11:20:32.391 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 11:20:32.394 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 11:20:32.395 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 158.1546ms
2025-06-24 11:20:32.396 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 11:20:32.397 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/5 - 状态码: 200 - 耗时: 198ms - 响应大小: 194bytes
2025-06-24 11:20:32.398 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/5 - 200 null application/json; charset=utf-8 205.8169ms
2025-06-24 11:20:32.416 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:20:32.423 +08:00 [INF] 请求开始: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:32.425 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-24 11:20:32.425 +08:00 [INF] Route matched with {action = "GetWorks", controller = "Work"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.Models.Work]]]] GetWorks(Int32, Int32, System.Nullable`1[System.UInt64], System.Nullable`1[System.SByte]) on controller WcsNet.Controllers.WorkController (WcsNet).
2025-06-24 11:20:32.757 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_work` AS `w`
2025-06-24 11:20:32.830 +08:00 [INF] Executed DbCommand (33ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`created_at`, `w`.`created_id`, `w`.`err_msg`, `w`.`item_code`, `w`.`lane_id`, `w`.`max_retries`, `w`.`Parameters`, `w`.`Retries`, `w`.`Status`, `w`.`task_type`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_work` AS `w`
ORDER BY `w`.`Id` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:20:32.832 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:32.839 +08:00 [INF] Executed action WcsNet.Controllers.WorkController.GetWorks (WcsNet) in 411.6653ms
2025-06-24 11:20:32.841 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.WorkController.GetWorks (WcsNet)'
2025-06-24 11:20:32.843 +08:00 [INF] 请求完成: GET /api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 419ms - 响应大小: 5728bytes
2025-06-24 11:20:32.845 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/works?task_type=&status=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 428.7412ms
2025-06-24 11:20:35.544 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:20:35.546 +08:00 [INF] 请求开始: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:35.552 +08:00 [INF] 请求完成: GET /api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 6ms - 响应大小: 0bytes
2025-06-24 11:20:35.555 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/apilogs?api_name=&status=&start_time=&end_time=&page_size=10&page_no=1 - 404 0 null 10.9306ms
2025-06-24 11:20:35.558 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/apilogs, Response status code: 404
2025-06-24 11:20:37.423 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - null null
2025-06-24 11:20:37.423 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices - null null
2025-06-24 11:20:37.425 +08:00 [INF] 请求开始: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:37.427 +08:00 [INF] 请求开始: GET /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:37.428 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:20:37.429 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:37.432 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 11:20:37.433 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 11:20:37.436 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:37.439 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.2672ms
2025-06-24 11:20:37.440 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 11:20:37.440 +08:00 [INF] 请求完成: GET /api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 15ms - 响应大小: 1556bytes
2025-06-24 11:20:37.442 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?alarm_type=&start_time=&end_time=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 19.082ms
2025-06-24 11:20:37.709 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 11:20:37.727 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 11:20:37.730 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 11:20:37.731 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 293.786ms
2025-06-24 11:20:37.732 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 11:20:37.735 +08:00 [INF] 请求完成: GET /api/devices - 状态码: 200 - 耗时: 308ms - 响应大小: 1576bytes
2025-06-24 11:20:37.737 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 314.0219ms
2025-06-24 11:20:50.272 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - null null
2025-06-24 11:20:50.273 +08:00 [INF] 请求开始: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 11:20:50.274 +08:00 [INF] 请求完成: GET /api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-24 11:20:50.275 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/lanes?lane_name=&lane_code=&page_size=10&page_no=1 - 404 0 null 3.5187ms
2025-06-24 11:20:50.277 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/lanes, Response status code: 404
