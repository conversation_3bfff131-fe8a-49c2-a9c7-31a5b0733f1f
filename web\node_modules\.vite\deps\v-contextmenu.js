import {
  Teleport,
  computed,
  createVNode,
  defineComponent,
  inject,
  isRef,
  isVNode,
  nextTick,
  onBeforeUnmount,
  provide,
  reactive,
  ref,
  vShow,
  watch,
  withDirectives
} from "./chunk-WS2SEKHB.js";
import "./chunk-4MZFK6UX.js";

// node_modules/.pnpm/v-contextmenu@3.2.0_vue@3.5.7_typescript@5.6.2_/node_modules/v-contextmenu/dist/index.esm.js
var bind = function bind2(el, binding) {
  var _binding$instance;
  var contextmenuKey = binding.arg;
  if (!contextmenuKey) {
    console.error("参数有误");
    return;
  }
  var contextmenuOptions = binding.value;
  var contextmenuInstance = isRef(contextmenuKey) ? contextmenuKey.value : (_binding$instance = binding.instance) === null || _binding$instance === void 0 ? void 0 : _binding$instance.$refs[contextmenuKey];
  if (!contextmenuInstance) {
    console.error("没有找到 ".concat(contextmenuKey, " 对应的实例"));
    return;
  }
  if (typeof contextmenuInstance.addReference !== "function") {
    console.error("".concat(contextmenuKey, " 对应的实例不是 VContextmenu"));
    return;
  }
  el.$contextmenuKey = contextmenuKey;
  contextmenuInstance.addReference(el, contextmenuOptions);
};
var unbind = function unbind2(el, binding) {
  var _binding$instance2;
  var contextmenuKey = el.$contextmenuKey;
  if (!contextmenuKey) return;
  var contextmenuInstance = isRef(contextmenuKey) ? contextmenuKey.value : (_binding$instance2 = binding.instance) === null || _binding$instance2 === void 0 ? void 0 : _binding$instance2.$refs[contextmenuKey];
  if (!contextmenuInstance) {
    console.error("没有找到 ".concat(contextmenuKey, " 对应的实例"));
    return;
  }
  if (typeof contextmenuInstance.removeReference !== "function") {
    console.error("".concat(contextmenuKey, " 对应的实例不是 VContextmenu"));
    return;
  }
  contextmenuInstance.removeReference(el);
};
var rebind = function rebind2(el, binding) {
  unbind(el, binding);
  bind(el, binding);
};
var contextmenuDirective = {
  mounted: bind,
  updated: rebind,
  beforeUnmount: unbind
};
var CLASSES = {
  contextmenu: "v-contextmenu",
  // 根元素
  contextmenuIcon: "v-contextmenu-icon",
  // icon
  contextmenuInner: "v-contextmenu-inner",
  // 菜单根元素
  contextmenuDivider: "v-contextmenu-divider",
  // 分割线
  contextmenuItem: "v-contextmenu-item",
  // 单个菜单
  contextmenuItemHover: "v-contextmenu-item--hover",
  // 单个菜单激活状态
  contextmenuItemDisabled: "v-contextmenu-item--disabled",
  // 单个菜单禁用状态
  contextmenuGroup: "v-contextmenu-group",
  // 按钮组根元素
  contextmenuGroupTitle: "v-contextmenu-group__title",
  // 按钮组标题
  contextmenuGroupMenus: "v-contextmenu-group__menus",
  // 按钮组按钮容器
  contextmenuSubmenu: "v-contextmenu-submenu",
  // 子菜单容器
  contextmenuSubmenuTitle: "v-contextmenu-submenu__title",
  // 子菜单标题
  contextmenuSubmenuMenus: "v-contextmenu-submenu__menus",
  // 子菜单
  contextmenuSubmenuMenusTop: "v-contextmenu-submenu__menus--top",
  // 子菜单 Top
  contextmenuSubmenuMenusRight: "v-contextmenu-submenu__menus--right",
  // 子菜单 Right
  contextmenuSubmenuMenusBottom: "v-contextmenu-submenu__menus--bottom",
  // 子菜 Bottom单
  contextmenuSubmenuMenusLeft: "v-contextmenu-submenu__menus--left",
  // 子菜单 Left
  contextmenuSubmenuArrow: "v-contextmenu-submenu__arrow"
  // 子菜单标题 icon
};
function _isSlot(s) {
  return typeof s === "function" || Object.prototype.toString.call(s) === "[object Object]" && !isVNode(s);
}
var DEFAULT_REFERENCE_OPTIONS = {
  trigger: ["contextmenu"]
};
var Contextmenu = defineComponent({
  name: "VContextmenu",
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    autoAdjustPlacement: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    teleport: {
      type: [String, Object],
      default: function _default() {
        return "body";
      }
    },
    preventContextmenu: {
      type: Boolean,
      default: true
    }
    // destroyOnHide: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  emits: ["show", "hide", "update:modelValue", "contextmenu"],
  setup: function setup(props, _ref) {
    var emit = _ref.emit;
    var contextmenuRef = ref(null);
    var visible = ref(props.modelValue || false);
    var toggle = function toggle2(value) {
      visible.value = value;
      emit("update:modelValue", value);
    };
    var position = ref({
      top: 0,
      left: 0
    });
    var style = computed(function() {
      return {
        top: "".concat(position.value.top, "px"),
        left: "".concat(position.value.left, "px")
      };
    });
    var currentOptions = ref(null);
    var show = function show2(evt, options) {
      var targetOptions = evt instanceof Event ? options : evt;
      var autoAdjustPlacement = (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.autoAdjustPlacement) || props.autoAdjustPlacement;
      var targetPosition = {
        top: (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.top) || 0,
        left: (targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.left) || 0
      };
      if (evt instanceof Event) {
        var _targetOptions$top, _targetOptions$left;
        evt.preventDefault();
        targetPosition.top = (_targetOptions$top = targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.top) !== null && _targetOptions$top !== void 0 ? _targetOptions$top : evt.pageY;
        targetPosition.left = (_targetOptions$left = targetOptions === null || targetOptions === void 0 ? void 0 : targetOptions.left) !== null && _targetOptions$left !== void 0 ? _targetOptions$left : evt.pageX;
      }
      toggle(true);
      nextTick(function() {
        if (autoAdjustPlacement) {
          var el = contextmenuRef.value;
          if (!el) return;
          var width = el.clientWidth;
          var height = el.clientHeight;
          if (height + targetPosition.top >= window.innerHeight + window.scrollY) {
            var targetTop = targetPosition.top - height;
            if (targetTop > window.scrollY) {
              targetPosition.top = targetTop;
            }
          }
          if (width + targetPosition.left >= window.innerWidth + window.scrollX) {
            var targetWidth = targetPosition.left - width;
            if (targetWidth > window.scrollX) {
              targetPosition.left = targetWidth;
            }
          }
        }
        position.value = targetPosition;
        emit("show");
      });
    };
    var hide = function hide2() {
      currentOptions.value = null;
      toggle(false);
      emit("hide");
    };
    var references = reactive(/* @__PURE__ */ new Map());
    var currentReference = ref();
    var currentReferenceOptions = computed(function() {
      return currentReference.value && references.get(currentReference.value);
    });
    var addReference = function addReference2(el, options) {
      var triggers = function() {
        if (options !== null && options !== void 0 && options.trigger) {
          return Array.isArray(options.trigger) ? options.trigger : [options.trigger];
        }
        return DEFAULT_REFERENCE_OPTIONS.trigger;
      }();
      var handler = function handler2(evt) {
        if (props.disabled) return;
        currentReference.value = el;
        show(evt, {});
      };
      triggers.forEach(function(eventType) {
        el.addEventListener(eventType, handler);
      });
      references.set(el, {
        triggers,
        handler
      });
    };
    var removeReference = function removeReference2(el) {
      var options = references.get(el);
      if (!options) return;
      options.triggers.forEach(function(eventType) {
        el.removeEventListener(eventType, options.handler);
      });
      references.delete(el);
    };
    var onBodyClick = function onBodyClick2(evt) {
      if (!evt.target || !contextmenuRef.value || !currentReference.value) return;
      var notOutside = contextmenuRef.value.contains(evt.target) || currentReferenceOptions.value && currentReferenceOptions.value.triggers.includes("click") && currentReference.value.contains(evt.target);
      if (!notOutside) {
        toggle(false);
      }
    };
    watch(visible, function(value) {
      if (value) {
        document.addEventListener("click", onBodyClick);
      } else {
        document.removeEventListener("click", onBodyClick);
      }
    });
    onBeforeUnmount(function() {
      document.removeEventListener("click", onBodyClick);
    });
    provide("visible", visible);
    provide("autoAdjustPlacement", props.autoAdjustPlacement);
    provide("show", show);
    provide("hide", hide);
    return {
      visible,
      style,
      currentReferenceOptions,
      currentOptions,
      contextmenuRef,
      addReference,
      removeReference,
      toggle,
      show,
      hide
    };
  },
  methods: {
    renderContent: function renderContent() {
      var _this = this, _this$$slots$default, _this$$slots;
      return withDirectives(createVNode("div", {
        "class": CLASSES.contextmenu,
        "ref": "contextmenuRef",
        "style": this.style,
        "onContextmenu": function onContextmenu(evt) {
          if (_this.$props.preventContextmenu) {
            evt.preventDefault();
          }
          _this.$emit("contextmenu", evt);
        }
      }, [createVNode("ul", {
        "class": CLASSES.contextmenuInner
      }, [(_this$$slots$default = (_this$$slots = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots, {
        triggerOptions: "currentReferenceOptions",
        options: "currentOptions"
      })])]), [[vShow, "visible"]]);
    }
  },
  render: function render() {
    var _slot;
    if (!this.visible) return null;
    return this.teleport ? createVNode(Teleport, {
      "to": this.teleport
    }, _isSlot(_slot = this.renderContent()) ? _slot : {
      default: function _default2() {
        return [_slot];
      }
    }) : this.renderContent();
  }
});
function _toPrimitive(t, r) {
  if ("object" != typeof t || !t) return t;
  var e = t[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r || "default");
    if ("object" != typeof i) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
function _toPropertyKey(t) {
  var i = _toPrimitive(t, "string");
  return "symbol" == typeof i ? i : String(i);
}
function _defineProperty(obj, key, value) {
  key = _toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}
var ContextmenuItem = defineComponent({
  name: "VContextmenuItem",
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    hideOnClick: {
      type: Boolean,
      default: true
    },
    contextmenuAsClick: {
      type: Boolean,
      default: true
    }
  },
  emits: ["click", "contextmenu", "mouseenter", "mouseleave"],
  setup: function setup2(props, _ref) {
    var emit = _ref.emit;
    var rootHide = inject("hide");
    var hover = ref(false);
    var classes = computed(function() {
      return _defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenuItem, true), CLASSES.contextmenuItemDisabled, props.disabled), CLASSES.contextmenuItemHover, hover.value);
    });
    var handleClick = function handleClick2(evt) {
      emit("click", evt);
      if (props.disabled) return;
      props.hideOnClick && (rootHide === null || rootHide === void 0 ? void 0 : rootHide());
    };
    var handleContextmenu = function handleContextmenu2(evt) {
      emit("contextmenu", evt);
      if (props.contextmenuAsClick) {
        if (props.disabled) return;
        props.hideOnClick && (rootHide === null || rootHide === void 0 ? void 0 : rootHide());
      }
    };
    var handleMouseenter = function handleMouseenter2(evt) {
      emit("mouseenter", evt);
      if (props.disabled) return;
      hover.value = true;
    };
    var handleMouseleave = function handleMouseleave2(evt) {
      emit("mouseleave", evt);
      if (props.disabled) return;
      hover.value = false;
    };
    return {
      classes,
      handleClick,
      handleContextmenu,
      handleMouseenter,
      handleMouseleave
    };
  },
  render: function render2() {
    var _this$$slots$default, _this$$slots;
    return createVNode("li", {
      "class": this.classes,
      "onClick": this.handleClick,
      "onContextmenu": this.handleContextmenu,
      "onMouseenter": this.handleMouseenter,
      "onMouseleave": this.handleMouseleave
    }, [(_this$$slots$default = (_this$$slots = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots)]);
  }
});
var ContextmenuDivider = defineComponent({
  name: "VContextmenuDivider",
  render: function render3() {
    return createVNode("li", {
      "class": CLASSES.contextmenuDivider
    }, null);
  }
});
var ContextmenuIcon = defineComponent({
  name: "VContextmenuIcon",
  props: {
    name: {
      type: String,
      required: true
    }
  },
  render: function render4() {
    return createVNode("i", {
      "class": [CLASSES.contextmenuIcon, "".concat(CLASSES.contextmenuIcon, "-").concat(this.name)]
    }, null);
  }
});
var ContextmenuSubmenu = defineComponent({
  name: "VContextmenuSubmenu",
  props: {
    title: {
      type: String,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ["mouseenter", "mouseleave"],
  setup: function setup3(props, _ref) {
    var emit = _ref.emit;
    var submenuRef = ref(null);
    var autoAdjustPlacement = inject("autoAdjustPlacement");
    var placements = ref(["top", "right"]);
    var hover = ref(false);
    var handleMouseenter = function handleMouseenter2(evt) {
      if (props.disabled) return;
      hover.value = true;
      emit("mouseenter", evt);
      nextTick(function() {
        var targetPlacements = [];
        if (autoAdjustPlacement) {
          var target = evt.target;
          var targetDimension = target.getBoundingClientRect();
          if (!submenuRef.value) return;
          var submenuWidth = submenuRef.value.clientWidth;
          var submenuHeight = submenuRef.value.clientHeight;
          if (targetDimension.right + submenuWidth >= window.innerWidth) {
            targetPlacements.push("left");
          } else {
            targetPlacements.push("right");
          }
          if (targetDimension.bottom + submenuHeight >= window.innerHeight) {
            targetPlacements.push("bottom");
          } else {
            targetPlacements.push("top");
          }
        }
        placements.value = targetPlacements;
      });
    };
    var handleMouseleave = function handleMouseleave2(evt) {
      if (props.disabled) return;
      hover.value = false;
      emit("mouseleave", evt);
    };
    var titleClasses = computed(function() {
      return _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenuItem, true), CLASSES.contextmenuSubmenuTitle, true), CLASSES.contextmenuItemHover, hover.value), CLASSES.contextmenuItemDisabled, props.disabled);
    });
    var menusClasses = computed(function() {
      return _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, CLASSES.contextmenu, true), CLASSES.contextmenuSubmenuMenus, true), CLASSES.contextmenuSubmenuMenusTop, placements.value.includes("top")), CLASSES.contextmenuSubmenuMenusRight, placements.value.includes("right")), CLASSES.contextmenuSubmenuMenusBottom, placements.value.includes("bottom")), CLASSES.contextmenuSubmenuMenusLeft, placements.value.includes("left"));
    });
    return {
      hover,
      submenuRef,
      titleClasses,
      menusClasses,
      handleMouseenter,
      handleMouseleave
    };
  },
  render: function render5() {
    var _this$$slots$title, _this$$slots, _this$$slots$default, _this$$slots2;
    return createVNode("li", {
      "class": CLASSES.contextmenuSubmenu,
      "onMouseenter": this.handleMouseenter,
      "onMouseleave": this.handleMouseleave
    }, [createVNode("div", {
      "class": this.titleClasses
    }, [((_this$$slots$title = (_this$$slots = this.$slots).title) === null || _this$$slots$title === void 0 ? void 0 : _this$$slots$title.call(_this$$slots)) || this.title, createVNode("span", {
      "class": CLASSES.contextmenuSubmenuArrow
    }, [createVNode(ContextmenuIcon, {
      "name": "right-arrow"
    }, null)])]), this.hover ? createVNode("div", {
      "ref": "submenuRef",
      "class": this.menusClasses
    }, [createVNode("ul", {
      "class": CLASSES.contextmenuInner
    }, [(_this$$slots$default = (_this$$slots2 = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots2)])]) : null]);
  }
});
var ContextmenuGroup = defineComponent({
  name: "VContextmenuGroup",
  props: {
    title: {
      type: String,
      default: void 0
    },
    maxWidth: {
      type: [Number, String],
      default: void 0
    }
  },
  setup: function setup4(props) {
    var style = computed(function() {
      if (!props.maxWidth) return;
      return {
        "max-width": typeof props.maxWidth === "number" ? "".concat(props.maxWidth, "px") : props.maxWidth,
        "overflow-x": "auto"
      };
    });
    return {
      style
    };
  },
  methods: {
    renderTitle: function renderTitle() {
      var _this$$slots$title, _this$$slots;
      var content = ((_this$$slots$title = (_this$$slots = this.$slots).title) === null || _this$$slots$title === void 0 ? void 0 : _this$$slots$title.call(_this$$slots)) || this.title;
      return content ? createVNode("div", {
        "class": CLASSES.contextmenuGroupTitle
      }, [content]) : null;
    }
  },
  render: function render6() {
    var _this$$slots$default, _this$$slots2;
    return createVNode("li", {
      "class": CLASSES.contextmenuGroup
    }, [this.renderTitle(), createVNode("ul", {
      "style": this.style,
      "class": CLASSES.contextmenuGroupMenus
    }, [(_this$$slots$default = (_this$$slots2 = this.$slots).default) === null || _this$$slots$default === void 0 ? void 0 : _this$$slots$default.call(_this$$slots2)])]);
  }
});
var version = "3.2.0";
var install = function install2(app) {
  app.directive("contextmenu", contextmenuDirective);
  app.component(Contextmenu.name, Contextmenu);
  app.component(ContextmenuItem.name, ContextmenuItem);
  app.component(ContextmenuDivider.name, ContextmenuDivider);
  app.component(ContextmenuSubmenu.name, ContextmenuSubmenu);
  app.component(ContextmenuGroup.name, ContextmenuGroup);
};
var VContextmenu = {
  install,
  version
};
export {
  Contextmenu,
  ContextmenuDivider,
  ContextmenuGroup,
  ContextmenuItem,
  ContextmenuSubmenu,
  VContextmenu as default,
  contextmenuDirective as directive,
  install,
  version
};
//# sourceMappingURL=v-contextmenu.js.map
