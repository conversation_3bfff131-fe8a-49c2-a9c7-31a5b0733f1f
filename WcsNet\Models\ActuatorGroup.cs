using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_actuator_group")]
public class ActuatorGroup : BaseModel
{
    [Required]
    [StringLength(128)]
    [Column("group_name")]
    public string GroupName { get; set; } = string.Empty;

    [Required]
    [StringLength(16)]
    [Column("group_type")]
    public string GroupType { get; set; } = string.Empty;

    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;

    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1正常 2禁用
}
