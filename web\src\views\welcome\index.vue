<script setup lang="ts">
import { ref, markRaw, onMounted, onBeforeUnmount } from "vue";
import ReCol from "@/components/ReCol";
import { useDark, randomGradient, dayjs } from "./utils";
import WelcomeTable from "./components/table/index.vue";
import { ReNormalCountTo } from "@/components/ReCountTo";
import { useRenderFlicker } from "@/components/ReFlicker";
import { ChartBar, ChartLine, ChartRound } from "./components/charts";
import Segmented, { type OptionsType } from "@/components/ReSegmented";
// import { barChartData } from "./data";
import { getAlarmList } from "@/api/alaram";
import { getStatTaskList, getWeekStat } from "@/api/stat";

import GroupLine from "@iconify-icons/ri/group-line";
import Question from "@iconify-icons/ri/question-answer-line";
import CheckLine from "@iconify-icons/ri/chat-check-line";
import Smile from "@iconify-icons/ri/star-smile-line";

defineOptions({
  name: "Welcome"
});

const { isDark } = useDark();

let curWeek = ref(1); // 0上周、1本周
const optionsBasis: Array<OptionsType> = [
  {
    label: "上周"
  },
  {
    label: "本周"
  }
];

let alarmList = ref([]);
let intervalId = ref(0);

let chartData = ref([]);

// 柱状图数据
let barData = ref([
  {
    requireData: [],
    questionData: []
  },
  {
    requireData: [],
    questionData: []
  }
]);

//最新动态
let latestNewsData = ref([]);

function startTimer() {
  intervalId.value = window.setInterval(() => getAlarm(), 10000);
}

function getAlarm() {
  getAlarmList({ page_size: 10 }).then(res => {
    if (res.code === 0 && res.data && res.data.list) {
      alarmList.value = res.data.list.map(item => {
        return {
          msg: item.remark ? item.remark : "错误编码:" + item.code,
          ts: dayjs(item.createdAt).format("MM-DD HH:mm")
        };
      });
    }
  });
}

function getStatistics() {
  let tasks = [];
  let succs = [];
  let total = [];
  getStatTaskList({ page_size: 7 }).then(res => {
    if (res.code !== 0 || !res.data || !res.data.list) {
      return;
    }
    res.data.list.forEach(item => {
      tasks.unshift(item.task_n);
      succs.unshift(item.succ_n);
      total.unshift(item.total_succ);
    });
    let bv = 100;
    if (tasks.length > 0) {
      bv = (succs[tasks.length - 1] / tasks[tasks.length - 1]) * 100;
    }
    chartData.value = [
      {
        icon: GroupLine,
        bgColor: "#effaff",
        color: "#41b6ff",
        duration: 2200,
        name: "WCS任务",
        value: tasks.reduce((acc, cur) => acc + cur, 0),
        percent: "+88%",
        data: tasks // 平滑折线图数据
      },
      {
        icon: Question,
        bgColor: "#fff5f4",
        color: "#e85f33",
        duration: 1600,
        name: "成功任务",
        value: succs.reduce((acc, cur) => acc + cur, 0),
        percent: "+90%",
        data: succs
      },
      {
        icon: CheckLine,
        bgColor: "#eff8f4",
        color: "#26ce83",
        duration: 1500,
        name: "已完成总任务",
        value: total[total.length - 1],
        percent: "+99%",
        data: total
      },
      {
        icon: Smile,
        bgColor: "#f6f4fe",
        color: "#7846e5",
        duration: 100,
        name: "成功率",
        value: bv,
        percent: "+100%",
        data: [bv]
      }
    ];
  });
}

//获取周任务统计
function weekStat(t: number) {
  if (barData.value[t].requireData.length < 1) {
    console.log("数据为空,请求接口");
    getWeekStat(t).then(res => {
      // console.log(res);
      if (res.code == 0) {
        let tasks = [];
        let succs = [];
        res.data.list.forEach(item => {
          tasks.unshift(item.task_n);
          succs.unshift(item.succ_n);
        });
        if (tasks.length < 7) {
          tasks.fill(0, tasks.length);
          succs.fill(0, succs.length);
        }
        console.log(tasks, succs);
        barData.value[t] = {
          requireData: tasks,
          questionData: succs
        };
      }
    });
  }
}

onMounted(() => {
  getAlarm();
  getStatistics();
  getStatTaskList({ page_size: 14 }).then(res => {
    if (res.code == 0) {
      latestNewsData.value = res.data.list.map(item => {
        item.d = dayjs(item.d).format("YYYY-MM-DD");
        return item;
      });
    }
  });
  //上周统计
  weekStat(0);
  //本周统计
  weekStat(1);
  startTimer();
});

onBeforeUnmount(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value);
  }
});
</script>

<template>
  <div>
    <el-row :gutter="24" justify="space-around">
      <re-col
        v-for="(item, index) in chartData"
        :key="index"
        v-motion
        class="mb-[18px]"
        :value="6"
        :md="12"
        :sm="12"
        :xs="24"
        :initial="{
          opacity: 0,
          y: 100
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 80 * (index + 1)
          }
        }"
      >
        <el-card class="line-card" shadow="never">
          <div class="flex justify-between">
            <span class="text-md font-medium">
              {{ item.name }}
            </span>
            <div
              class="w-8 h-8 flex justify-center items-center rounded-md"
              :style="{
                backgroundColor: isDark ? 'transparent' : item.bgColor
              }"
            >
              <IconifyIconOffline
                :icon="item.icon"
                :color="item.color"
                width="18"
              />
            </div>
          </div>
          <div class="flex justify-between items-start mt-3">
            <div class="w-1/2">
              <ReNormalCountTo
                :duration="item.duration"
                :fontSize="'1.6em'"
                :startVal="100"
                :endVal="item.value"
              />
              <!-- <p class="font-medium text-green-500">{{ item.percent }}</p> -->
            </div>
            <ChartLine
              v-if="item.data.length > 1"
              class="!w-1/2"
              :color="item.color"
              :data="item.data"
            />
            <ChartRound v-else class="!w-1/2" :data="item.data" />
          </div>
        </el-card>
      </re-col>

      <re-col
        v-motion
        class="mb-[18px]"
        :value="18"
        :xs="24"
        :initial="{
          opacity: 0,
          y: 100
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 400
          }
        }"
      >
        <el-card class="bar-card" shadow="never">
          <div class="flex justify-between">
            <span class="text-md font-medium">分析概览</span>
            <Segmented v-model="curWeek" :options="optionsBasis" />
          </div>
          <div class="flex justify-between items-start mt-3">
            <ChartBar
              v-if="barData[curWeek]"
              :requireData="barData[curWeek].requireData"
              :questionData="barData[curWeek].questionData"
            />
          </div>
        </el-card>
      </re-col>

      <re-col
        v-motion
        class="mb-[18px]"
        :value="6"
        :xs="24"
        :initial="{
          opacity: 0,
          y: 100
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 480
          }
        }"
      >
        <el-card shadow="never">
          <div class="flex justify-between">
            <span class="text-md font-medium">报警信息</span>
          </div>
          <div
            v-for="(item, index) in alarmList"
            :key="index"
            :class="[
              'flex',
              'justify-between',
              'items-start',
              'text-text_color_regular',
              'text-sm',
              index === 0 ? 'mt-5' : 'mt-[1.1rem]'
            ]"
          >
            {{ `${item.msg} 报警时间: ${item.ts}` }}
          </div>
          <!-- <div
            v-for="(item, index) in progressData"
            :key="index"
            :class="[
              'flex',
              'justify-between',
              'items-start',
              index === 0 ? 'mt-8' : 'mt-[2.15rem]'
            ]"
          >
            <el-progress
              :text-inside="true"
              :percentage="item.percentage"
              :stroke-width="21"
              :color="item.color"
              striped
              striped-flow
              :duration="item.duration"
            />
            <span class="text-nowrap ml-2 text-text_color_regular text-sm">
              {{ item.week }}
            </span>
          </div> -->
        </el-card>
      </re-col>

      <re-col
        v-motion
        class="mb-[18px]"
        :value="18"
        :xs="24"
        :initial="{
          opacity: 0,
          y: 100
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 560
          }
        }"
      >
        <el-card shadow="never" class="h-[580px]">
          <div class="flex justify-between">
            <span class="text-md font-medium">数据统计</span>
          </div>
          <WelcomeTable class="mt-3" />
        </el-card>
      </re-col>

      <re-col
        v-motion
        class="mb-[18px]"
        :value="6"
        :xs="24"
        :initial="{
          opacity: 0,
          y: 100
        }"
        :enter="{
          opacity: 1,
          y: 0,
          transition: {
            delay: 640
          }
        }"
      >
        <el-card shadow="never">
          <div class="flex justify-between">
            <span class="text-md font-medium">最新动态</span>
          </div>
          <el-scrollbar max-height="504" class="mt-3">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in latestNewsData"
                :key="index"
                center
                placement="top"
                :icon="
                  markRaw(
                    useRenderFlicker({
                      background: randomGradient({
                        randomizeHue: true
                      })
                    })
                  )
                "
                :timestamp="item.d"
              >
                <p class="text-text_color_regular text-sm">
                  {{
                    `新增 ${item.task_n} 条WCS任务，${item.total_succ} 条已解决`
                  }}
                </p>
              </el-timeline-item>
            </el-timeline>
          </el-scrollbar>
        </el-card>
      </re-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-card) {
  --el-card-border-color: none;

  /* 解决概率进度条宽度 */
  .el-progress--line {
    width: 85%;
  }

  /* 解决概率进度条字体大小 */
  .el-progress-bar__innerText {
    font-size: 15px;
  }

  /* 隐藏 el-scrollbar 滚动条 */
  .el-scrollbar__bar {
    display: none;
  }

  /* el-timeline 每一项上下、左右边距 */
  .el-timeline-item {
    margin: 0 6px;
  }
}

.main-content {
  margin: 20px 20px 0 !important;
}
</style>
