hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': public
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': public
  '@babel/code-frame@7.24.7':
    '@babel/code-frame': public
  '@babel/compat-data@7.25.4':
    '@babel/compat-data': public
  '@babel/core@7.25.2':
    '@babel/core': public
  '@babel/generator@7.25.6':
    '@babel/generator': public
  '@babel/helper-annotate-as-pure@7.24.7':
    '@babel/helper-annotate-as-pure': public
  '@babel/helper-compilation-targets@7.25.2':
    '@babel/helper-compilation-targets': public
  '@babel/helper-create-class-features-plugin@7.25.4(@babel/core@7.25.2)':
    '@babel/helper-create-class-features-plugin': public
  '@babel/helper-member-expression-to-functions@7.24.8':
    '@babel/helper-member-expression-to-functions': public
  '@babel/helper-module-imports@7.24.7':
    '@babel/helper-module-imports': public
  '@babel/helper-module-transforms@7.25.2(@babel/core@7.25.2)':
    '@babel/helper-module-transforms': public
  '@babel/helper-optimise-call-expression@7.24.7':
    '@babel/helper-optimise-call-expression': public
  '@babel/helper-plugin-utils@7.24.8':
    '@babel/helper-plugin-utils': public
  '@babel/helper-replace-supers@7.25.0(@babel/core@7.25.2)':
    '@babel/helper-replace-supers': public
  '@babel/helper-simple-access@7.24.7':
    '@babel/helper-simple-access': public
  '@babel/helper-skip-transparent-expression-wrappers@7.24.7':
    '@babel/helper-skip-transparent-expression-wrappers': public
  '@babel/helper-string-parser@7.24.8':
    '@babel/helper-string-parser': public
  '@babel/helper-validator-identifier@7.24.7':
    '@babel/helper-validator-identifier': public
  '@babel/helper-validator-option@7.24.8':
    '@babel/helper-validator-option': public
  '@babel/helpers@7.25.6':
    '@babel/helpers': public
  '@babel/highlight@7.24.7':
    '@babel/highlight': public
  '@babel/parser@7.25.6':
    '@babel/parser': public
  '@babel/plugin-proposal-decorators@7.24.7(@babel/core@7.25.2)':
    '@babel/plugin-proposal-decorators': public
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.25.2)':
    '@babel/plugin-syntax-async-generators': public
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-bigint': public
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.25.2)':
    '@babel/plugin-syntax-class-properties': public
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.25.2)':
    '@babel/plugin-syntax-class-static-block': public
  '@babel/plugin-syntax-decorators@7.24.7(@babel/core@7.25.2)':
    '@babel/plugin-syntax-decorators': public
  '@babel/plugin-syntax-import-attributes@7.25.6(@babel/core@7.25.2)':
    '@babel/plugin-syntax-import-attributes': public
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.25.2)':
    '@babel/plugin-syntax-import-meta': public
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-json-strings': public
  '@babel/plugin-syntax-jsx@7.24.7(@babel/core@7.25.2)':
    '@babel/plugin-syntax-jsx': public
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.25.2)':
    '@babel/plugin-syntax-logical-assignment-operators': public
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-nullish-coalescing-operator': public
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.25.2)':
    '@babel/plugin-syntax-numeric-separator': public
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-object-rest-spread': public
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-optional-catch-binding': public
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.25.2)':
    '@babel/plugin-syntax-optional-chaining': public
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.25.2)':
    '@babel/plugin-syntax-private-property-in-object': public
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.25.2)':
    '@babel/plugin-syntax-top-level-await': public
  '@babel/plugin-syntax-typescript@7.25.4(@babel/core@7.25.2)':
    '@babel/plugin-syntax-typescript': public
  '@babel/plugin-transform-typescript@7.25.2(@babel/core@7.25.2)':
    '@babel/plugin-transform-typescript': public
  '@babel/runtime@7.25.6':
    '@babel/runtime': public
  '@babel/standalone@7.25.6':
    '@babel/standalone': public
  '@babel/template@7.25.0':
    '@babel/template': public
  '@babel/traverse@7.25.6':
    '@babel/traverse': public
  '@babel/types@7.25.6':
    '@babel/types': public
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': public
  '@commitlint/config-validator@19.5.0':
    '@commitlint/config-validator': public
  '@commitlint/ensure@19.5.0':
    '@commitlint/ensure': public
  '@commitlint/execute-rule@19.5.0':
    '@commitlint/execute-rule': public
  '@commitlint/format@19.5.0':
    '@commitlint/format': public
  '@commitlint/is-ignored@19.5.0':
    '@commitlint/is-ignored': public
  '@commitlint/lint@19.5.0':
    '@commitlint/lint': public
  '@commitlint/load@19.5.0(@types/node@20.16.5)(typescript@5.6.2)':
    '@commitlint/load': public
  '@commitlint/message@19.5.0':
    '@commitlint/message': public
  '@commitlint/parse@19.5.0':
    '@commitlint/parse': public
  '@commitlint/read@19.5.0':
    '@commitlint/read': public
  '@commitlint/resolve-extends@19.5.0':
    '@commitlint/resolve-extends': public
  '@commitlint/rules@19.5.0':
    '@commitlint/rules': public
  '@commitlint/to-lines@19.5.0':
    '@commitlint/to-lines': public
  '@commitlint/top-level@19.5.0':
    '@commitlint/top-level': public
  '@csstools/css-parser-algorithms@3.0.1(@csstools/css-tokenizer@3.0.1)':
    '@csstools/css-parser-algorithms': public
  '@csstools/css-tokenizer@3.0.1':
    '@csstools/css-tokenizer': public
  '@csstools/media-query-list-parser@3.0.1(@csstools/css-parser-algorithms@3.0.1(@csstools/css-tokenizer@3.0.1))(@csstools/css-tokenizer@3.0.1)':
    '@csstools/media-query-list-parser': public
  '@csstools/selector-specificity@4.0.0(postcss-selector-parser@6.1.2)':
    '@csstools/selector-specificity': public
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': public
  '@dual-bundle/import-meta-resolve@4.1.0':
    '@dual-bundle/import-meta-resolve': public
  '@element-plus/icons-vue@2.3.1(vue@3.5.7(typescript@5.6.2))':
    '@element-plus/icons-vue': public
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': public
  '@eslint-community/eslint-utils@4.4.0(eslint@9.11.0(jiti@1.21.6))':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.11.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.18.0':
    '@eslint/config-array': public
  '@eslint/eslintrc@3.1.0':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.4':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.0':
    '@eslint/plugin-kit': public
  '@floating-ui/core@1.6.8':
    '@floating-ui/core': public
  '@floating-ui/dom@1.6.11':
    '@floating-ui/dom': public
  '@floating-ui/utils@0.2.8':
    '@floating-ui/utils': public
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': public
  '@humanwhocodes/retry@0.3.0':
    '@humanwhocodes/retry': public
  '@iconify/types@2.0.0':
    '@iconify/types': public
  '@intlify/bundle-utils@9.0.0-beta.0(vue-i18n@10.0.1(vue@3.5.7(typescript@5.6.2)))':
    '@intlify/bundle-utils': public
  '@intlify/core-base@10.0.1':
    '@intlify/core-base': public
  '@intlify/message-compiler@10.0.0':
    '@intlify/message-compiler': public
  '@intlify/message-compiler@12.0.0-alpha.2':
    '@intlify/message-compiler': public
  '@intlify/shared@10.0.0':
    '@intlify/shared': public
  '@intlify/vue-i18n-extensions@6.2.0(@intlify/shared@10.0.0)(@vue/compiler-dom@3.5.7)(vue-i18n@10.0.1(vue@3.5.7(typescript@5.6.2)))(vue@3.5.7(typescript@5.6.2))':
    '@intlify/vue-i18n-extensions': public
  '@intlify/vue-i18n-extensions@6.2.0(@intlify/shared@12.0.0-alpha.2)(@vue/compiler-dom@3.5.7)(vue-i18n@10.0.1(vue@3.5.7(typescript@5.6.2)))(vue@3.5.7(typescript@5.6.2))':
    '@intlify/vue-i18n-extensions': public
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': public
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': public
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': public
  '@jest/console@27.5.1':
    '@jest/console': public
  '@jest/core@27.5.1(canvas@2.11.2)':
    '@jest/core': public
  '@jest/environment@27.5.1':
    '@jest/environment': public
  '@jest/fake-timers@27.5.1':
    '@jest/fake-timers': public
  '@jest/globals@27.5.1':
    '@jest/globals': public
  '@jest/reporters@27.5.1':
    '@jest/reporters': public
  '@jest/source-map@27.5.1':
    '@jest/source-map': public
  '@jest/test-result@27.5.1':
    '@jest/test-result': public
  '@jest/test-sequencer@27.5.1':
    '@jest/test-sequencer': public
  '@jest/transform@27.5.1':
    '@jest/transform': public
  '@jest/types@27.5.1':
    '@jest/types': public
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': public
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': public
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': public
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': public
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': public
  '@mapbox/node-pre-gyp@1.0.11':
    '@mapbox/node-pre-gyp': public
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': public
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': public
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': public
  '@nuxt/kit@3.13.2(rollup@4.22.4)':
    '@nuxt/kit': public
  '@nuxt/schema@3.13.2(rollup@4.22.4)':
    '@nuxt/schema': public
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': public
  '@pkgr/core@0.1.1':
    '@pkgr/core': public
  '@rollup/pluginutils@5.1.0(rollup@4.22.4)':
    '@rollup/pluginutils': public
  '@rollup/rollup-win32-x64-msvc@4.22.4':
    '@rollup/rollup-win32-x64-msvc': public
  '@sindresorhus/merge-streams@2.3.0':
    '@sindresorhus/merge-streams': public
  '@sinonjs/commons@1.8.6':
    '@sinonjs/commons': public
  '@sinonjs/fake-timers@8.1.0':
    '@sinonjs/fake-timers': public
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': public
  '@tootallnate/once@1.1.2':
    '@tootallnate/once': public
  '@transloadit/prettier-bytes@0.0.7':
    '@transloadit/prettier-bytes': public
  '@trysound/sax@0.2.0':
    '@trysound/sax': public
  '@types/babel__core@7.20.5':
    '@types/babel__core': public
  '@types/babel__generator@7.6.8':
    '@types/babel__generator': public
  '@types/babel__template@7.4.4':
    '@types/babel__template': public
  '@types/babel__traverse@7.20.6':
    '@types/babel__traverse': public
  '@types/conventional-commits-parser@5.0.0':
    '@types/conventional-commits-parser': public
  '@types/estree@1.0.5':
    '@types/estree': public
  '@types/estree@1.0.6':
    '@types/estree': public
  '@types/event-emitter@0.3.5':
    '@types/event-emitter': public
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': public
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': public
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': public
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': public
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': public
  '@types/lodash@4.17.7':
    '@types/lodash': public
  '@types/mousetrap@1.6.15':
    '@types/mousetrap': public
  '@types/prettier@2.7.3':
    '@types/prettier': public
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': public
  '@types/tinycolor2@1.4.6':
    '@types/tinycolor2': public
  '@types/web-animations-js@2.2.16':
    '@types/web-animations-js': public
  '@types/web-bluetooth@0.0.20':
    '@types/web-bluetooth': public
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': public
  '@types/yargs@16.0.9':
    '@types/yargs': public
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@7.18.0(eslint@9.11.0(jiti@1.21.6))(typescript@5.6.2)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.6.2)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@7.18.0(eslint@9.11.0(jiti@1.21.6))(typescript@5.6.2)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': public
  '@uppy/companion-client@2.2.2':
    '@uppy/companion-client': public
  '@uppy/core@2.3.4':
    '@uppy/core': public
  '@uppy/store-default@2.1.1':
    '@uppy/store-default': public
  '@uppy/utils@4.1.3':
    '@uppy/utils': public
  '@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4)':
    '@uppy/xhr-upload': public
  '@volar/language-core@2.4.5':
    '@volar/language-core': public
  '@volar/source-map@2.4.5':
    '@volar/source-map': public
  '@volar/typescript@2.4.5':
    '@volar/typescript': public
  '@vue/babel-helper-vue-transform-on@1.2.5':
    '@vue/babel-helper-vue-transform-on': public
  '@vue/babel-plugin-jsx@1.2.5(@babel/core@7.25.2)':
    '@vue/babel-plugin-jsx': public
  '@vue/babel-plugin-resolve-type@1.2.5(@babel/core@7.25.2)':
    '@vue/babel-plugin-resolve-type': public
  '@vue/compiler-core@3.5.7':
    '@vue/compiler-core': public
  '@vue/compiler-dom@3.5.7':
    '@vue/compiler-dom': public
  '@vue/compiler-sfc@3.5.7':
    '@vue/compiler-sfc': public
  '@vue/compiler-ssr@3.5.7':
    '@vue/compiler-ssr': public
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': public
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': public
  '@vue/language-core@2.1.6(typescript@5.6.2)':
    '@vue/language-core': public
  '@vue/reactivity@3.5.7':
    '@vue/reactivity': public
  '@vue/runtime-core@3.5.7':
    '@vue/runtime-core': public
  '@vue/runtime-dom@3.5.7':
    '@vue/runtime-dom': public
  '@vue/server-renderer@3.5.7(vue@3.5.7(typescript@5.6.2))':
    '@vue/server-renderer': public
  '@vue/shared@3.5.7':
    '@vue/shared': public
  '@vueuse/metadata@11.1.0':
    '@vueuse/metadata': public
  '@vueuse/shared@11.1.0(vue@3.5.7(typescript@5.6.2))':
    '@vueuse/shared': public
  '@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/basic-modules': public
  '@wangeditor/code-highlight@1.0.3(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/code-highlight': public
  '@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/core': public
  '@wangeditor/list-module@1.0.5(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/list-module': public
  '@wangeditor/table-module@1.1.4(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/table-module': public
  '@wangeditor/upload-image-module@1.0.2(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/upload-image-module': public
  '@wangeditor/video-module@1.1.4(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(nanoid@3.3.7)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/video-module': public
  '@zougt/some-loader-utils@1.4.3':
    '@zougt/some-loader-utils': public
  JSONStream@1.3.5:
    JSONStream: public
  abab@2.0.6:
    abab: public
  abbrev@1.1.1:
    abbrev: public
  acorn-globals@6.0.0:
    acorn-globals: public
  acorn-jsx@5.3.2(acorn@8.12.1):
    acorn-jsx: public
  acorn-walk@7.2.0:
    acorn-walk: public
  acorn@8.12.1:
    acorn: public
  adler-32@1.3.1:
    adler-32: public
  agent-base@6.0.2:
    agent-base: public
  ajv@6.12.6:
    ajv: public
  ansi-align@3.0.1:
    ansi-align: public
  ansi-escapes@4.3.2:
    ansi-escapes: public
  ansi-regex@5.0.1:
    ansi-regex: public
  ansi-styles@4.3.0:
    ansi-styles: public
  any-promise@1.3.0:
    any-promise: public
  anymatch@3.1.3:
    anymatch: public
  aproba@2.0.0:
    aproba: public
  are-we-there-yet@2.0.0:
    are-we-there-yet: public
  arg@5.0.2:
    arg: public
  argparse@2.0.1:
    argparse: public
  array-ify@1.0.0:
    array-ify: public
  array-union@2.1.0:
    array-union: public
  astral-regex@2.0.0:
    astral-regex: public
  async-validator@4.2.5:
    async-validator: public
  asynckit@0.4.0:
    asynckit: public
  babel-jest@27.5.1(@babel/core@7.25.2):
    babel-jest: public
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: public
  babel-plugin-jest-hoist@27.5.1:
    babel-plugin-jest-hoist: public
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.25.2):
    babel-preset-current-node-syntax: public
  babel-preset-jest@27.5.1(@babel/core@7.25.2):
    babel-preset-jest: public
  balanced-match@2.0.0:
    balanced-match: public
  base64-js@1.5.1:
    base64-js: public
  binary-extensions@2.3.0:
    binary-extensions: public
  bl@4.1.0:
    bl: public
  boolbase@1.0.0:
    boolbase: public
  brace-expansion@1.1.11:
    brace-expansion: public
  braces@3.0.3:
    braces: public
  browser-process-hrtime@1.0.0:
    browser-process-hrtime: public
  browserslist@4.23.3:
    browserslist: public
  bser@2.1.1:
    bser: public
  buffer-from@1.1.2:
    buffer-from: public
  buffer@5.7.1:
    buffer: public
  bundle-import@0.0.1:
    bundle-import: public
  c12@1.11.2:
    c12: public
  cac@6.7.14:
    cac: public
  call-bind@1.0.7:
    call-bind: public
  callsites@3.1.0:
    callsites: public
  camelcase-css@2.0.1:
    camelcase-css: public
  camelcase@8.0.0:
    camelcase: public
  caniuse-api@3.0.0:
    caniuse-api: public
  caniuse-lite@1.0.30001662:
    caniuse-lite: public
  canvas@2.11.2:
    canvas: public
  cfb@1.2.2:
    cfb: public
  chalk@5.3.0:
    chalk: public
  char-regex@1.0.2:
    char-regex: public
  chokidar@3.6.0:
    chokidar: public
  chownr@2.0.0:
    chownr: public
  ci-info@3.9.0:
    ci-info: public
  citty@0.1.6:
    citty: public
  cjs-module-lexer@1.4.1:
    cjs-module-lexer: public
  cli-boxes@3.0.0:
    cli-boxes: public
  cli-cursor@5.0.0:
    cli-cursor: public
  cli-truncate@4.0.0:
    cli-truncate: public
  cliui@6.0.0:
    cliui: public
  co@4.6.0:
    co: public
  codepage@1.15.0:
    codepage: public
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: public
  color-convert@2.0.1:
    color-convert: public
  color-name@1.1.4:
    color-name: public
  color-string@1.9.1:
    color-string: public
  color-support@1.1.3:
    color-support: public
  color@4.2.3:
    color: public
  colord@2.9.3:
    colord: public
  colorette@2.0.20:
    colorette: public
  combined-stream@1.0.8:
    combined-stream: public
  commander@12.1.0:
    commander: public
  commist@1.1.0:
    commist: public
  compare-func@2.0.0:
    compare-func: public
  compatx@0.1.8:
    compatx: public
  compute-scroll-into-view@1.0.20:
    compute-scroll-into-view: public
  computeds@0.0.1:
    computeds: public
  concat-map@0.0.1:
    concat-map: public
  concat-stream@2.0.0:
    concat-stream: public
  confbox@0.1.7:
    confbox: public
  consola@3.2.3:
    consola: public
  console-control-strings@1.1.0:
    console-control-strings: public
  conventional-changelog-angular@7.0.0:
    conventional-changelog-angular: public
  conventional-changelog-conventionalcommits@7.0.2:
    conventional-changelog-conventionalcommits: public
  conventional-commits-parser@5.0.0:
    conventional-commits-parser: public
  convert-source-map@2.0.0:
    convert-source-map: public
  core-js@3.38.1:
    core-js: public
  cosmiconfig-typescript-loader@5.0.0(@types/node@20.16.5)(cosmiconfig@9.0.0(typescript@5.6.2))(typescript@5.6.2):
    cosmiconfig-typescript-loader: public
  cosmiconfig@9.0.0(typescript@5.6.2):
    cosmiconfig: public
  crc-32@1.2.2:
    crc-32: public
  cross-spawn@7.0.3:
    cross-spawn: public
  css-declaration-sorter@7.2.0(postcss@8.4.47):
    css-declaration-sorter: public
  css-functions-list@3.2.2:
    css-functions-list: public
  css-select@5.1.0:
    css-select: public
  css-tree@2.3.1:
    css-tree: public
  css-what@6.1.0:
    css-what: public
  cssesc@3.0.0:
    cssesc: public
  cssnano-preset-default@7.0.6(postcss@8.4.47):
    cssnano-preset-default: public
  cssnano-preset-lite@2.1.3(postcss@8.4.47):
    cssnano-preset-lite: public
  cssnano-utils@5.0.0(postcss@8.4.47):
    cssnano-utils: public
  csso@5.0.5:
    csso: public
  cssom@0.4.4:
    cssom: public
  cssstyle@2.3.0:
    cssstyle: public
  csstype@3.1.3:
    csstype: public
  d3-color@3.1.0:
    d3-color: public
  d3-dispatch@3.0.1:
    d3-dispatch: public
  d3-drag@3.0.0:
    d3-drag: public
  d3-ease@3.0.1:
    d3-ease: public
  d3-interpolate@3.0.1:
    d3-interpolate: public
  d3-selection@3.0.0:
    d3-selection: public
  d3-timer@3.0.1:
    d3-timer: public
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: public
  d3-zoom@3.0.0:
    d3-zoom: public
  d@1.0.2:
    d: public
  danmu.js@1.1.13:
    danmu.js: public
  dargs@8.1.0:
    dargs: public
  data-urls@2.0.0:
    data-urls: public
  de-indent@1.0.2:
    de-indent: public
  debug@4.3.7:
    debug: public
  decamelize@1.2.0:
    decamelize: public
  decimal.js@10.4.3:
    decimal.js: public
  decompress-response@4.2.1:
    decompress-response: public
  dedent@0.7.0:
    dedent: public
  deep-is@0.1.4:
    deep-is: public
  deepmerge@4.3.1:
    deepmerge: public
  define-data-property@1.1.4:
    define-data-property: public
  define-lazy-prop@2.0.0:
    define-lazy-prop: public
  defu@6.1.4:
    defu: public
  delayed-stream@1.0.0:
    delayed-stream: public
  delegate@3.2.0:
    delegate: public
  delegates@1.0.0:
    delegates: public
  destr@2.0.3:
    destr: public
  detect-libc@2.0.3:
    detect-libc: public
  detect-newline@3.1.0:
    detect-newline: public
  didyoumean@1.2.2:
    didyoumean: public
  diff-sequences@27.5.1:
    diff-sequences: public
  dijkstrajs@1.0.3:
    dijkstrajs: public
  dir-glob@3.0.1:
    dir-glob: public
  dlv@1.1.3:
    dlv: public
  dom-serializer@2.0.0:
    dom-serializer: public
  dom-zindex@1.0.6:
    dom-zindex: public
  dom7@3.0.0:
    dom7: public
  domelementtype@2.3.0:
    domelementtype: public
  domexception@2.0.1:
    domexception: public
  domhandler@5.0.3:
    domhandler: public
  domutils@3.1.0:
    domutils: public
  dot-prop@5.3.0:
    dot-prop: public
  dotenv@16.4.5:
    dotenv: public
  downloadjs@1.4.7:
    downloadjs: public
  duplexify@4.1.3:
    duplexify: public
  eastasianwidth@0.2.0:
    eastasianwidth: public
  electron-to-chromium@1.5.27:
    electron-to-chromium: public
  emittery@0.8.1:
    emittery: public
  emoji-regex@8.0.0:
    emoji-regex: public
  end-of-stream@1.4.4:
    end-of-stream: public
  entities@4.5.0:
    entities: public
  env-paths@2.2.1:
    env-paths: public
  environment@1.1.0:
    environment: public
  error-ex@1.3.2:
    error-ex: public
  es-define-property@1.0.0:
    es-define-property: public
  es-errors@1.3.0:
    es-errors: public
  es-module-lexer@0.4.1:
    es-module-lexer: public
  es5-ext@0.10.64:
    es5-ext: public
  es6-iterator@2.0.3:
    es6-iterator: public
  es6-symbol@3.1.4:
    es6-symbol: public
  esbuild@0.21.5:
    esbuild: public
  escalade@3.2.0:
    escalade: public
  escape-html@1.0.3:
    escape-html: public
  escape-string-regexp@4.0.0:
    escape-string-regexp: public
  escodegen@2.1.0:
    escodegen: public
  eslint-scope@8.0.2:
    eslint-scope: public
  eslint-visitor-keys@4.0.0:
    eslint-visitor-keys: public
  esniff@2.0.1:
    esniff: public
  espree@10.1.0:
    espree: public
  esprima@4.0.1:
    esprima: public
  esquery@1.6.0:
    esquery: public
  esrecurse@4.3.0:
    esrecurse: public
  estraverse@5.3.0:
    estraverse: public
  estree-walker@2.0.2:
    estree-walker: public
  esutils@2.0.3:
    esutils: public
  event-emitter@0.3.5:
    event-emitter: public
  eventemitter3@4.0.7:
    eventemitter3: public
  execa@8.0.1:
    execa: public
  exit@0.1.2:
    exit: public
  expect@27.5.1:
    expect: public
  ext@1.7.0:
    ext: public
  fast-deep-equal@3.1.3:
    fast-deep-equal: public
  fast-diff@1.3.0:
    fast-diff: public
  fast-glob@3.3.2:
    fast-glob: public
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: public
  fast-levenshtein@2.0.6:
    fast-levenshtein: public
  fast-uri@3.0.1:
    fast-uri: public
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: public
  fastq@1.17.1:
    fastq: public
  fb-watchman@2.0.2:
    fb-watchman: public
  file-entry-cache@8.0.0:
    file-entry-cache: public
  fill-range@7.1.1:
    fill-range: public
  find-up@5.0.0:
    find-up: public
  flat-cache@4.0.1:
    flat-cache: public
  flatted@3.3.1:
    flatted: public
  follow-redirects@1.15.9:
    follow-redirects: public
  foreground-child@3.3.0:
    foreground-child: public
  form-data@4.0.0:
    form-data: public
  frac@1.1.2:
    frac: public
  fraction.js@4.3.7:
    fraction.js: public
  framesync@6.1.2:
    framesync: public
  fs-extra@11.2.0:
    fs-extra: public
  fs-minipass@2.1.0:
    fs-minipass: public
  fs.realpath@1.0.0:
    fs.realpath: public
  function-bind@1.1.2:
    function-bind: public
  gauge@3.0.2:
    gauge: public
  gensync@1.0.0-beta.2:
    gensync: public
  get-caller-file@2.0.5:
    get-caller-file: public
  get-east-asian-width@1.2.0:
    get-east-asian-width: public
  get-intrinsic@1.2.4:
    get-intrinsic: public
  get-package-type@0.1.0:
    get-package-type: public
  get-stream@8.0.1:
    get-stream: public
  get-tsconfig@4.8.1:
    get-tsconfig: public
  giget@1.2.3:
    giget: public
  git-raw-commits@4.0.0:
    git-raw-commits: public
  glob-parent@6.0.2:
    glob-parent: public
  glob@11.0.0:
    glob: public
  global-directory@4.0.1:
    global-directory: public
  global-modules@2.0.0:
    global-modules: public
  global-prefix@3.0.0:
    global-prefix: public
  globals@13.24.0:
    globals: public
  globby@11.1.0:
    globby: public
  globjoin@0.1.4:
    globjoin: public
  gopd@1.0.1:
    gopd: public
  graceful-fs@4.2.11:
    graceful-fs: public
  graphemer@1.4.0:
    graphemer: public
  graphlib@2.1.8:
    graphlib: public
  has-flag@4.0.0:
    has-flag: public
  has-property-descriptors@1.0.2:
    has-property-descriptors: public
  has-proto@1.0.3:
    has-proto: public
  has-symbols@1.0.3:
    has-symbols: public
  has-unicode@2.0.1:
    has-unicode: public
  hash-sum@2.0.0:
    hash-sum: public
  hasown@2.0.2:
    hasown: public
  he@1.2.0:
    he: public
  help-me@3.0.0:
    help-me: public
  hey-listen@1.0.8:
    hey-listen: public
  hookable@5.5.3:
    hookable: public
  html-encoding-sniffer@2.0.1:
    html-encoding-sniffer: public
  html-escaper@2.0.2:
    html-escaper: public
  html-tags@3.3.1:
    html-tags: public
  html-void-elements@2.0.1:
    html-void-elements: public
  htmlparser2@8.0.2:
    htmlparser2: public
  http-proxy-agent@4.0.1:
    http-proxy-agent: public
  https-proxy-agent@5.0.1:
    https-proxy-agent: public
  human-signals@5.0.0:
    human-signals: public
  i18next@20.6.1:
    i18next: public
  iconv-lite@0.4.24:
    iconv-lite: public
  ieee754@1.2.1:
    ieee754: public
  ignore@5.3.2:
    ignore: public
  immediate@3.0.6:
    immediate: public
  immer@9.0.21:
    immer: public
  immutable@4.3.7:
    immutable: public
  import-fresh@3.3.0:
    import-fresh: public
  import-from-string@0.0.4:
    import-from-string: public
  import-local@3.2.0:
    import-local: public
  import-meta-resolve@4.1.0:
    import-meta-resolve: public
  imurmurhash@0.1.4:
    imurmurhash: public
  inflight@1.0.6:
    inflight: public
  inherits@2.0.4:
    inherits: public
  ini@1.3.8:
    ini: public
  is-arrayish@0.2.1:
    is-arrayish: public
  is-binary-path@2.1.0:
    is-binary-path: public
  is-core-module@2.15.1:
    is-core-module: public
  is-docker@2.2.1:
    is-docker: public
  is-extglob@2.1.1:
    is-extglob: public
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: public
  is-generator-fn@2.1.0:
    is-generator-fn: public
  is-glob@4.0.3:
    is-glob: public
  is-hotkey@0.2.0:
    is-hotkey: public
  is-number@7.0.0:
    is-number: public
  is-obj@2.0.0:
    is-obj: public
  is-path-inside@3.0.3:
    is-path-inside: public
  is-plain-object@5.0.0:
    is-plain-object: public
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: public
  is-reference@3.0.2:
    is-reference: public
  is-stream@3.0.0:
    is-stream: public
  is-text-path@2.0.0:
    is-text-path: public
  is-typedarray@1.0.0:
    is-typedarray: public
  is-url@1.2.4:
    is-url: public
  is-wsl@2.2.0:
    is-wsl: public
  isexe@2.0.0:
    isexe: public
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: public
  istanbul-lib-instrument@5.2.1:
    istanbul-lib-instrument: public
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: public
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: public
  istanbul-reports@3.1.7:
    istanbul-reports: public
  jackspeak@4.0.1:
    jackspeak: public
  jest-changed-files@27.5.1:
    jest-changed-files: public
  jest-circus@27.5.1:
    jest-circus: public
  jest-cli@27.5.1(canvas@2.11.2):
    jest-cli: public
  jest-config@27.5.1(canvas@2.11.2):
    jest-config: public
  jest-diff@27.5.1:
    jest-diff: public
  jest-docblock@27.5.1:
    jest-docblock: public
  jest-each@27.5.1:
    jest-each: public
  jest-environment-jsdom@27.5.1(canvas@2.11.2):
    jest-environment-jsdom: public
  jest-environment-node@27.5.1:
    jest-environment-node: public
  jest-get-type@27.5.1:
    jest-get-type: public
  jest-haste-map@27.5.1:
    jest-haste-map: public
  jest-jasmine2@27.5.1:
    jest-jasmine2: public
  jest-leak-detector@27.5.1:
    jest-leak-detector: public
  jest-matcher-utils@27.5.1:
    jest-matcher-utils: public
  jest-message-util@27.5.1:
    jest-message-util: public
  jest-mock@27.5.1:
    jest-mock: public
  jest-pnp-resolver@1.2.3(jest-resolve@27.5.1):
    jest-pnp-resolver: public
  jest-regex-util@27.5.1:
    jest-regex-util: public
  jest-resolve-dependencies@27.5.1:
    jest-resolve-dependencies: public
  jest-resolve@27.5.1:
    jest-resolve: public
  jest-runner@27.5.1(canvas@2.11.2):
    jest-runner: public
  jest-runtime@27.5.1:
    jest-runtime: public
  jest-serializer@27.5.1:
    jest-serializer: public
  jest-snapshot@27.5.1:
    jest-snapshot: public
  jest-util@27.5.1:
    jest-util: public
  jest-validate@27.5.1:
    jest-validate: public
  jest-watcher@27.5.1:
    jest-watcher: public
  jest-worker@27.5.1:
    jest-worker: public
  jest@27.5.1(canvas@2.11.2):
    jest: public
  jiti@1.21.6:
    jiti: public
  js-sdsl@4.3.0:
    js-sdsl: public
  js-tokens@9.0.0:
    js-tokens: public
  js-yaml@4.1.0:
    js-yaml: public
  jsdom@16.7.0(canvas@2.11.2):
    jsdom: public
  jsesc@2.5.2:
    jsesc: public
  json-buffer@3.0.1:
    json-buffer: public
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: public
  json-schema-traverse@0.4.1:
    json-schema-traverse: public
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: public
  json5@2.2.3:
    json5: public
  jsonc-eslint-parser@2.4.0:
    jsonc-eslint-parser: public
  jsonfile@6.1.0:
    jsonfile: public
  jsonparse@1.3.1:
    jsonparse: public
  keyv@4.5.4:
    keyv: public
  kind-of@6.0.3:
    kind-of: public
  kleur@3.0.3:
    kleur: public
  klona@2.0.6:
    klona: public
  knitwork@1.1.0:
    knitwork: public
  known-css-properties@0.34.0:
    known-css-properties: public
  kolorist@1.8.0:
    kolorist: public
  leven@2.1.0:
    leven: public
  levn@0.4.1:
    levn: public
  lie@3.1.1:
    lie: public
  lilconfig@3.1.2:
    lilconfig: public
  lines-and-columns@1.2.4:
    lines-and-columns: public
  listr2@8.2.4:
    listr2: public
  local-pkg@0.5.0:
    local-pkg: public
  locate-path@6.0.0:
    locate-path: public
  lodash-es@4.17.21:
    lodash-es: public
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: public
  lodash.camelcase@4.3.0:
    lodash.camelcase: public
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: public
  lodash.debounce@4.0.8:
    lodash.debounce: public
  lodash.foreach@4.5.0:
    lodash.foreach: public
  lodash.isequal@4.5.0:
    lodash.isequal: public
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: public
  lodash.kebabcase@4.1.1:
    lodash.kebabcase: public
  lodash.memoize@4.1.2:
    lodash.memoize: public
  lodash.merge@4.6.2:
    lodash.merge: public
  lodash.mergewith@4.6.2:
    lodash.mergewith: public
  lodash.snakecase@4.1.1:
    lodash.snakecase: public
  lodash.startcase@4.4.0:
    lodash.startcase: public
  lodash.throttle@4.1.1:
    lodash.throttle: public
  lodash.toarray@4.4.0:
    lodash.toarray: public
  lodash.truncate@4.4.2:
    lodash.truncate: public
  lodash.uniq@4.5.0:
    lodash.uniq: public
  lodash.upperfirst@4.3.1:
    lodash.upperfirst: public
  lodash@4.17.21:
    lodash: public
  log-update@6.1.0:
    log-update: public
  lru-cache@6.0.0:
    lru-cache: public
  magic-string@0.30.11:
    magic-string: public
  make-dir@3.1.0:
    make-dir: public
  make-dir@4.0.0:
    make-dir: public
  makeerror@1.0.12:
    makeerror: public
  mathml-tag-names@2.1.3:
    mathml-tag-names: public
  mdn-data@2.0.30:
    mdn-data: public
  memoize-one@6.0.0:
    memoize-one: public
  meow@13.2.0:
    meow: public
  merge-stream@2.0.0:
    merge-stream: public
  merge2@1.4.1:
    merge2: public
  micromatch@4.0.8:
    micromatch: public
  mime-db@1.52.0:
    mime-db: public
  mime-match@1.0.2:
    mime-match: public
  mime-types@2.1.35:
    mime-types: public
  mimic-fn@4.0.0:
    mimic-fn: public
  mimic-function@5.0.1:
    mimic-function: public
  mimic-response@2.1.0:
    mimic-response: public
  minimatch@3.1.2:
    minimatch: public
  minimist@1.2.8:
    minimist: public
  minipass@7.1.2:
    minipass: public
  minizlib@2.1.2:
    minizlib: public
  mkdirp@1.0.4:
    mkdirp: public
  mlly@1.7.1:
    mlly: public
  mousetrap@1.6.5:
    mousetrap: public
  mqtt-packet@6.10.0:
    mqtt-packet: public
  mri@1.2.0:
    mri: public
  ms@2.1.3:
    ms: public
  muggle-string@0.4.1:
    muggle-string: public
  mz@2.7.0:
    mz: public
  namespace-emitter@2.0.1:
    namespace-emitter: public
  nan@2.20.0:
    nan: public
  nanoid@3.3.7:
    nanoid: public
  natural-compare@1.4.0:
    natural-compare: public
  next-tick@1.1.0:
    next-tick: public
  node-fetch-native@1.6.4:
    node-fetch-native: public
  node-fetch@2.7.0:
    node-fetch: public
  node-int64@0.4.0:
    node-int64: public
  node-releases@2.0.18:
    node-releases: public
  nopt@5.0.0:
    nopt: public
  normalize-path@3.0.0:
    normalize-path: public
  normalize-range@0.1.2:
    normalize-range: public
  normalize-url@6.1.0:
    normalize-url: public
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: public
  npm-run-path@4.0.1:
    npm-run-path: public
  npmlog@5.0.1:
    npmlog: public
  nth-check@2.1.1:
    nth-check: public
  number-allocator@1.0.14:
    number-allocator: public
  nwsapi@2.2.12:
    nwsapi: public
  nypm@0.3.11:
    nypm: public
  object-assign@4.1.1:
    object-assign: public
  object-hash@3.0.0:
    object-hash: public
  object-inspect@1.13.2:
    object-inspect: public
  ohash@1.1.4:
    ohash: public
  once@1.4.0:
    once: public
  onetime@6.0.0:
    onetime: public
  open@8.4.2:
    open: public
  optionator@0.9.4:
    optionator: public
  p-limit@3.1.0:
    p-limit: public
  p-locate@5.0.0:
    p-locate: public
  p-try@2.2.0:
    p-try: public
  package-json-from-dist@1.0.0:
    package-json-from-dist: public
  parent-module@1.0.1:
    parent-module: public
  parse-json@5.2.0:
    parse-json: public
  parse5@6.0.1:
    parse5: public
  path-browserify@1.0.1:
    path-browserify: public
  path-exists@4.0.0:
    path-exists: public
  path-is-absolute@1.0.1:
    path-is-absolute: public
  path-key@3.1.1:
    path-key: public
  path-parse@1.0.7:
    path-parse: public
  path-scurry@2.0.0:
    path-scurry: public
  path-to-regexp@8.1.0:
    path-to-regexp: public
  path-type@4.0.0:
    path-type: public
  path2d@0.2.1:
    path2d: public
  pathe@1.1.2:
    pathe: public
  pdfjs-dist@4.6.82:
    pdfjs-dist: public
  perfect-debounce@1.0.0:
    perfect-debounce: public
  picocolors@1.1.0:
    picocolors: public
  picomatch@2.3.1:
    picomatch: public
  pidtree@0.6.0:
    pidtree: public
  pify@2.3.0:
    pify: public
  pirates@4.0.6:
    pirates: public
  pkg-dir@4.2.0:
    pkg-dir: public
  pkg-types@1.2.0:
    pkg-types: public
  pngjs@5.0.0:
    pngjs: public
  popmotion@11.0.5:
    popmotion: public
  postcss-calc@10.0.2(postcss@8.4.47):
    postcss-calc: public
  postcss-colormin@7.0.2(postcss@8.4.47):
    postcss-colormin: public
  postcss-convert-values@7.0.4(postcss@8.4.47):
    postcss-convert-values: public
  postcss-discard-comments@7.0.3(postcss@8.4.47):
    postcss-discard-comments: public
  postcss-discard-duplicates@7.0.1(postcss@8.4.47):
    postcss-discard-duplicates: public
  postcss-discard-empty@7.0.0(postcss@8.4.47):
    postcss-discard-empty: public
  postcss-discard-overridden@7.0.0(postcss@8.4.47):
    postcss-discard-overridden: public
  postcss-js@4.0.1(postcss@8.4.47):
    postcss-js: public
  postcss-load-config@4.0.2(postcss@8.4.47):
    postcss-load-config: public
  postcss-media-query-parser@0.2.3:
    postcss-media-query-parser: public
  postcss-merge-longhand@7.0.4(postcss@8.4.47):
    postcss-merge-longhand: public
  postcss-merge-rules@7.0.4(postcss@8.4.47):
    postcss-merge-rules: public
  postcss-minify-font-values@7.0.0(postcss@8.4.47):
    postcss-minify-font-values: public
  postcss-minify-gradients@7.0.0(postcss@8.4.47):
    postcss-minify-gradients: public
  postcss-minify-params@7.0.2(postcss@8.4.47):
    postcss-minify-params: public
  postcss-minify-selectors@7.0.4(postcss@8.4.47):
    postcss-minify-selectors: public
  postcss-nested@6.2.0(postcss@8.4.47):
    postcss-nested: public
  postcss-normalize-charset@7.0.0(postcss@8.4.47):
    postcss-normalize-charset: public
  postcss-normalize-display-values@7.0.0(postcss@8.4.47):
    postcss-normalize-display-values: public
  postcss-normalize-positions@7.0.0(postcss@8.4.47):
    postcss-normalize-positions: public
  postcss-normalize-repeat-style@7.0.0(postcss@8.4.47):
    postcss-normalize-repeat-style: public
  postcss-normalize-string@7.0.0(postcss@8.4.47):
    postcss-normalize-string: public
  postcss-normalize-timing-functions@7.0.0(postcss@8.4.47):
    postcss-normalize-timing-functions: public
  postcss-normalize-unicode@7.0.2(postcss@8.4.47):
    postcss-normalize-unicode: public
  postcss-normalize-url@7.0.0(postcss@8.4.47):
    postcss-normalize-url: public
  postcss-normalize-whitespace@7.0.0(postcss@8.4.47):
    postcss-normalize-whitespace: public
  postcss-ordered-values@7.0.1(postcss@8.4.47):
    postcss-ordered-values: public
  postcss-reduce-initial@7.0.2(postcss@8.4.47):
    postcss-reduce-initial: public
  postcss-reduce-transforms@7.0.0(postcss@8.4.47):
    postcss-reduce-transforms: public
  postcss-resolve-nested-selector@0.1.6:
    postcss-resolve-nested-selector: public
  postcss-safe-parser@6.0.0(postcss@8.4.47):
    postcss-safe-parser: public
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: public
  postcss-sorting@8.0.2(postcss@8.4.47):
    postcss-sorting: public
  postcss-svgo@7.0.1(postcss@8.4.47):
    postcss-svgo: public
  postcss-unique-selectors@7.0.3(postcss@8.4.47):
    postcss-unique-selectors: public
  postcss-value-parser@4.2.0:
    postcss-value-parser: public
  preact@10.24.0:
    preact: public
  prelude-ls@1.2.1:
    prelude-ls: public
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  pretty-format@27.5.1:
    pretty-format: public
  prismjs@1.29.0:
    prismjs: public
  process-nextick-args@2.0.1:
    process-nextick-args: public
  process@0.11.10:
    process: public
  prompts@2.4.2:
    prompts: public
  proxy-from-env@1.1.0:
    proxy-from-env: public
  psl@1.9.0:
    psl: public
  pump@3.0.2:
    pump: public
  punycode@2.3.1:
    punycode: public
  querystringify@2.2.0:
    querystringify: public
  queue-microtask@1.2.3:
    queue-microtask: public
  rc9@2.1.2:
    rc9: public
  react-is@17.0.2:
    react-is: public
  read-cache@1.0.0:
    read-cache: public
  readable-stream@3.6.2:
    readable-stream: public
  readdirp@3.6.0:
    readdirp: public
  regenerator-runtime@0.14.1:
    regenerator-runtime: public
  reinterval@1.1.0:
    reinterval: public
  require-directory@2.1.1:
    require-directory: public
  require-from-string@2.0.2:
    require-from-string: public
  require-main-filename@2.0.0:
    require-main-filename: public
  requires-port@1.0.0:
    requires-port: public
  resolve-cwd@3.0.0:
    resolve-cwd: public
  resolve-from@5.0.0:
    resolve-from: public
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: public
  resolve.exports@1.1.1:
    resolve.exports: public
  resolve@1.22.8:
    resolve: public
  restore-cursor@5.1.0:
    restore-cursor: public
  reusify@1.0.4:
    reusify: public
  rfdc@1.4.1:
    rfdc: public
  rollup-plugin-external-globals@0.10.0(rollup@4.22.4):
    rollup-plugin-external-globals: public
  rollup@4.22.4:
    rollup: public
  run-parallel@1.2.0:
    run-parallel: public
  safe-buffer@5.2.1:
    safe-buffer: public
  safer-buffer@2.1.2:
    safer-buffer: public
  saxes@5.0.1:
    saxes: public
  scroll-into-view-if-needed@2.2.31:
    scroll-into-view-if-needed: public
  scule@1.3.0:
    scule: public
  semver@6.3.1:
    semver: public
  semver@7.6.3:
    semver: public
  set-blocking@2.0.0:
    set-blocking: public
  set-function-length@1.2.2:
    set-function-length: public
  shebang-command@2.0.0:
    shebang-command: public
  shebang-regex@3.0.0:
    shebang-regex: public
  side-channel@1.0.6:
    side-channel: public
  signal-exit@4.1.0:
    signal-exit: public
  simple-concat@1.0.1:
    simple-concat: public
  simple-get@3.1.1:
    simple-get: public
  simple-swizzle@0.2.2:
    simple-swizzle: public
  sisteransi@1.0.5:
    sisteransi: public
  slash@3.0.0:
    slash: public
  slate-history@0.66.0(slate@0.72.8):
    slate-history: public
  slate@0.72.8:
    slate: public
  slice-ansi@4.0.0:
    slice-ansi: public
  snabbdom@3.6.2:
    snabbdom: public
  source-map-js@1.2.1:
    source-map-js: public
  source-map-support@0.5.21:
    source-map-support: public
  source-map@0.7.4:
    source-map: public
  sourcemap-codec@1.4.8:
    sourcemap-codec: public
  split2@3.2.2:
    split2: public
  sprintf-js@1.0.3:
    sprintf-js: public
  ssf@0.11.2:
    ssf: public
  ssr-window@3.0.0:
    ssr-window: public
  stable@0.1.8:
    stable: public
  stack-utils@2.0.6:
    stack-utils: public
  std-env@3.7.0:
    std-env: public
  stream-shift@1.0.3:
    stream-shift: public
  string-argv@0.3.2:
    string-argv: public
  string-hash@1.1.3:
    string-hash: public
  string-length@4.0.2:
    string-length: public
  string-width@4.2.3:
    string-width-cjs: public
  string-width@7.2.0:
    string-width: public
  string_decoder@1.3.0:
    string_decoder: public
  strip-ansi@6.0.1:
    strip-ansi: public
    strip-ansi-cjs: public
  strip-bom@4.0.0:
    strip-bom: public
  strip-final-newline@3.0.0:
    strip-final-newline: public
  strip-json-comments@3.1.1:
    strip-json-comments: public
  strip-literal@2.1.0:
    strip-literal: public
  style-value-types@5.1.2:
    style-value-types: public
  stylehacks@7.0.4(postcss@8.4.47):
    stylehacks: public
  stylelint-config-html@1.1.0(postcss-html@1.7.0)(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-config-html: public
  stylelint-config-recommended-scss@14.1.0(postcss@8.4.47)(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-config-recommended-scss: public
  stylelint-config-recommended@14.0.1(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-config-recommended: public
  stylelint-config-standard@36.0.1(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-config-standard: public
  stylelint-order@6.0.4(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-order: public
  stylelint-scss@6.7.0(stylelint@16.9.0(typescript@5.6.2)):
    stylelint-scss: public
  sucrase@3.35.0:
    sucrase: public
  supports-color@7.2.0:
    supports-color: public
  supports-hyperlinks@3.1.0:
    supports-hyperlinks: public
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: public
  svg-tags@1.0.0:
    svg-tags: public
  symbol-tree@3.2.4:
    symbol-tree: public
  synckit@0.9.1:
    synckit: public
  table@6.8.2:
    table: public
  tar@6.2.1:
    tar: public
  terminal-link@2.1.1:
    terminal-link: public
  test-exclude@6.0.0:
    test-exclude: public
  text-extensions@2.4.0:
    text-extensions: public
  text-table@0.2.0:
    text-table: public
  thenify-all@1.6.0:
    thenify-all: public
  thenify@3.3.1:
    thenify: public
  throat@6.0.2:
    throat: public
  through@2.3.8:
    through: public
  tiny-invariant@1.3.3:
    tiny-invariant: public
  tiny-warning@1.0.3:
    tiny-warning: public
  tinycolor2@1.6.0:
    tinycolor2: public
  tinyexec@0.3.0:
    tinyexec: public
  tinygradient@1.1.5:
    tinygradient: public
  tippy.js@6.3.7:
    tippy.js: public
  tmpl@1.0.5:
    tmpl: public
  to-fast-properties@2.0.0:
    to-fast-properties: public
  to-regex-range@5.0.1:
    to-regex-range: public
  tough-cookie@4.1.4:
    tough-cookie: public
  tr46@0.0.3:
    tr46: public
  tr46@2.1.0:
    tr46: public
  ts-api-utils@1.3.0(typescript@5.6.2):
    ts-api-utils: public
  ts-interface-checker@0.1.13:
    ts-interface-checker: public
  tslib@2.3.0:
    tslib: public
  type-check@0.4.0:
    type-check: public
  type-detect@4.0.8:
    type-detect: public
  type-fest@4.26.1:
    type-fest: public
  type@2.7.3:
    type: public
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: public
  typedarray@0.0.6:
    typedarray: public
  ufo@1.5.4:
    ufo: public
  uncrypto@0.1.3:
    uncrypto: public
  unctx@2.3.1:
    unctx: public
  undici-types@6.19.8:
    undici-types: public
  unicorn-magic@0.1.0:
    unicorn-magic: public
  unimport@3.12.0(rollup@4.22.4):
    unimport: public
  universalify@2.0.1:
    universalify: public
  unplugin@1.14.1:
    unplugin: public
  untyped@1.4.2:
    untyped: public
  update-browserslist-db@1.1.0(browserslist@4.23.3):
    update-browserslist-db: public
  uri-js@4.4.1:
    uri-js: public
  url-parse@1.5.10:
    url-parse: public
  util-deprecate@1.0.2:
    util-deprecate: public
  util@0.10.4:
    util: public
  uuid@8.3.2:
    uuid: public
  v8-to-istanbul@8.1.1:
    v8-to-istanbul: public
  vite-plugin-externals@0.6.2(vite@5.4.7(@types/node@20.16.5)(sass@1.77.8)):
    vite-plugin-externals: public
  vscode-jsonrpc@6.0.0:
    vscode-jsonrpc: public
  vscode-languageclient@7.0.0:
    vscode-languageclient: public
  vscode-languageserver-protocol@3.16.0:
    vscode-languageserver-protocol: public
  vscode-languageserver-textdocument@1.0.12:
    vscode-languageserver-textdocument: public
  vscode-languageserver-types@3.16.0:
    vscode-languageserver-types: public
  vscode-languageserver@7.0.0:
    vscode-languageserver: public
  vscode-uri@3.0.8:
    vscode-uri: public
  vue-demi@0.14.10(vue@3.5.7(typescript@5.6.2)):
    vue-demi: public
  vue-observe-visibility@2.0.0-alpha.1(vue@3.5.7(typescript@5.6.2)):
    vue-observe-visibility: public
  vue-resize@2.0.0-alpha.1(vue@3.5.7(typescript@5.6.2)):
    vue-resize: public
  w3c-hr-time@1.0.2:
    w3c-hr-time: public
  w3c-xmlserializer@2.0.0:
    w3c-xmlserializer: public
  walker@1.0.8:
    walker: public
  webidl-conversions@3.0.1:
    webidl-conversions: public
  webidl-conversions@6.1.0:
    webidl-conversions: public
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: public
  whatwg-encoding@1.0.5:
    whatwg-encoding: public
  whatwg-mimetype@2.3.0:
    whatwg-mimetype: public
  whatwg-url@5.0.0:
    whatwg-url: public
  whatwg-url@8.7.0:
    whatwg-url: public
  which-module@2.0.1:
    which-module: public
  which@2.0.2:
    which: public
  wide-align@1.1.5:
    wide-align: public
  widest-line@5.0.0:
    widest-line: public
  wildcard@1.1.2:
    wildcard: public
  wmf@1.0.2:
    wmf: public
  word-wrap@1.2.5:
    word-wrap: public
  word@0.3.0:
    word: public
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: public
  wrap-ansi@9.0.0:
    wrap-ansi: public
  wrappy@1.0.2:
    wrappy: public
  write-file-atomic@5.0.1:
    write-file-atomic: public
  ws@7.5.10:
    ws: public
  xe-utils@3.5.30:
    xe-utils: public
  xgplayer-subtitles@3.0.20(core-js@3.38.1):
    xgplayer-subtitles: public
  xml-name-validator@4.0.0:
    xml-name-validator: public
  xmlchars@2.2.0:
    xmlchars: public
  xtend@4.0.2:
    xtend: public
  y18n@4.0.3:
    y18n: public
  yallist@4.0.0:
    yallist: public
  yaml-eslint-parser@1.2.3:
    yaml-eslint-parser: public
  yaml@2.5.1:
    yaml: public
  yargs-parser@18.1.3:
    yargs-parser: public
  yargs@17.7.2:
    yargs: public
  yocto-queue@0.1.0:
    yocto-queue: public
  zrender@5.6.0:
    zrender: public
ignoredBuilds:
  - esbuild
  - vue-demi
  - canvas
  - es5-ext
  - core-js
  - typeit
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.2
pendingBuilds: []
prunedAt: Mon, 23 Jun 2025 05:50:43 GMT
publicHoistPattern:
  - '*'
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.19.12'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.19.12'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.19.12'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.19.12'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.19.12'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.19.12'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.19.12'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.19.12'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.19.12'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.19.12'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.19.12'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.19.12'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.19.12'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.19.12'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.19.12'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.19.12'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.19.12'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.19.12'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.19.12'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.19.12'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.19.12'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.19.12'
  - '@esbuild/win32-ia32@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.22.4'
  - '@rollup/rollup-android-arm64@4.22.4'
  - '@rollup/rollup-darwin-arm64@4.22.4'
  - '@rollup/rollup-darwin-x64@4.22.4'
  - '@rollup/rollup-linux-arm-gnueabihf@4.22.4'
  - '@rollup/rollup-linux-arm-musleabihf@4.22.4'
  - '@rollup/rollup-linux-arm64-gnu@4.22.4'
  - '@rollup/rollup-linux-arm64-musl@4.22.4'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.22.4'
  - '@rollup/rollup-linux-riscv64-gnu@4.22.4'
  - '@rollup/rollup-linux-s390x-gnu@4.22.4'
  - '@rollup/rollup-linux-x64-gnu@4.22.4'
  - '@rollup/rollup-linux-x64-musl@4.22.4'
  - '@rollup/rollup-win32-arm64-msvc@4.22.4'
  - '@rollup/rollup-win32-ia32-msvc@4.22.4'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\SourceCode\Test\TempTest\Ai\wcs\web\node_modules\.pnpm
virtualStoreDirMaxLength: 60
