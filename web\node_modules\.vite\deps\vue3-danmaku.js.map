{"version": 3, "sources": ["../../.pnpm/vue3-danmaku@1.6.1_vue@3.5.7_typescript@5.6.2_/node_modules/vue3-danmaku/dist/vue3-danmaku.esm.js"], "sourcesContent": ["import{computed as e,defineComponent as t,ref as n,reactive as a,onMounted as l,onBeforeUnmount as u,nextTick as s,createApp as o,h as i,openBlock as d,createElementBlock as r,createElementVNode as m,normalizeClass as v,renderSlot as c}from\"vue\";var f=t({name:\"vue3-danmaku\",components:{},props:{danmus:{type:Array,required:!0,default:()=>[]},channels:{type:Number,default:0},autoplay:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},useSlot:{type:<PERSON>olean,default:!1},debounce:{type:Number,default:100},speeds:{type:Number,default:200},randomChannel:{type:Boolean,default:!1},fontSize:{type:Number,default:18},top:{type:Number,default:4},right:{type:Number,default:0},isSuspend:{type:Boolean,default:!1},extraStyle:{type:String,default:\"\"}},emits:[\"list-end\",\"play-end\",\"dm-over\",\"dm-out\",\"update:danmus\"],setup(t,{emit:d,slots:r}){let m=n(document.createElement(\"div\")),v=n(document.createElement(\"div\"));const c=n(0),f=n(0);let p=0;const h=n(0),y=n(0),g=n(0),w=n(!1),b=n(!1),x=n({}),S=function(t,n,a=\"modelValue\",l){return e({get:()=>t[a],set:e=>{n(`update:${a}`,l?l(e):e)}})}(t,d,\"danmus\"),k=a({channels:e((()=>t.channels||h.value)),autoplay:e((()=>t.autoplay)),loop:e((()=>t.loop)),useSlot:e((()=>t.useSlot)),debounce:e((()=>t.debounce)),randomChannel:e((()=>t.randomChannel))}),C=a({height:e((()=>y.value)),fontSize:e((()=>t.fontSize)),speeds:e((()=>t.speeds)),top:e((()=>t.top)),right:e((()=>t.right))});function L(){E(),t.isSuspend&&function(){let e=[];v.value.addEventListener(\"mouseover\",(t=>{let n=t.target;n.className.includes(\"dm\")||(n=n.closest(\".dm\")||n),n.className.includes(\"dm\")&&(e.includes(n)||(d(\"dm-over\",{el:n}),n.classList.add(\"pause\"),e.push(n)))})),v.value.addEventListener(\"mouseout\",(t=>{let n=t.target;n.className.includes(\"dm\")||(n=n.closest(\".dm\")||n),n.className.includes(\"dm\")&&(d(\"dm-out\",{el:n}),n.classList.remove(\"pause\"),e.forEach((e=>{e.classList.remove(\"pause\")})),e=[])}))}(),k.autoplay&&N()}function E(){if(c.value=m.value.offsetWidth,f.value=m.value.offsetHeight,0===c.value||0===f.value)throw new Error(\"获取不到容器宽高\")}function N(){b.value=!1,p||(p=window.setInterval((()=>function(){if(!b.value&&S.value.length)if(g.value>S.value.length-1){const e=v.value.children.length;k.loop&&(e<g.value&&(d(\"list-end\"),g.value=0),B())}else B()}()),k.debounce))}function B(e){const n=k.loop?g.value%S.value.length:g.value,a=e||S.value[n];let l=document.createElement(\"div\");k.useSlot?l=function(e,t){const n=o({render:()=>i(\"div\",{},[r.dm&&r.dm({danmu:e,index:t})])});return n.mount(document.createElement(\"div\"))}(a,n).$el:(l.innerHTML=a,l.setAttribute(\"style\",t.extraStyle),l.style.fontSize=`${C.fontSize}px`,l.style.lineHeight=`${C.fontSize}px`),l.classList.add(\"dm\"),v.value.appendChild(l),l.style.opacity=\"0\",s((()=>{C.height||(y.value=l.offsetHeight),k.channels||(h.value=Math.floor(f.value/(C.height+C.top)));let e=function(e){let t=[...Array(k.channels).keys()];k.randomChannel&&(t=t.sort((()=>.5-Math.random())));for(let n of t){const t=x.value[n];if(!t||!t.length)return x.value[n]=[e],e.addEventListener(\"animationend\",(()=>x.value[n].splice(0,1))),n%k.channels;for(let a=0;a<t.length;a++){const l=$(t[a])-10;if(l<=.88*(e.offsetWidth-t[a].offsetWidth)||l<=0)break;if(a===t.length-1)return x.value[n].push(e),e.addEventListener(\"animationend\",(()=>x.value[n].splice(0,1))),n%k.channels}}return-1}(l);if(e>=0){const t=l.offsetWidth,a=C.height;l.classList.add(\"move\"),l.dataset.index=`${n}`,l.dataset.channel=e.toString(),l.style.opacity=\"1\",l.style.top=e*(a+C.top)+\"px\",l.style.width=t+C.right+\"px\",l.style.setProperty(\"--dm-scroll-width\",`-${c.value+t}px`),l.style.left=`${c.value}px`,l.style.animationDuration=c.value/C.speeds+\"s\",l.addEventListener(\"animationend\",(()=>{Number(l.dataset.index)!==S.value.length-1||k.loop||d(\"play-end\",l.dataset.index),v.value&&v.value.removeChild(l)})),g.value++}else v.value.removeChild(l)}))}function $(e){const t=e.offsetWidth||parseInt(e.style.width),n=e.getBoundingClientRect().right||v.value.getBoundingClientRect().right+t;return v.value.getBoundingClientRect().right-n}function z(){clearInterval(p),p=0,g.value=0}return l((()=>{L()})),u((()=>{z()})),{container:m,dmContainer:v,hidden:w,paused:b,danmuList:S,getPlayState:function(){return!b.value},resize:function(){E();const e=v.value.getElementsByClassName(\"dm\");for(let t=0;t<e.length;t++){const n=e[t];n.style.setProperty(\"--dm-scroll-width\",`-${c.value+n.offsetWidth}px`),n.style.left=`${c.value}px`,n.style.animationDuration=c.value/C.speeds+\"s\"}},play:N,pause:function(){b.value=!0},stop:function(){x.value={},v.value.innerHTML=\"\",b.value=!0,w.value=!1,z()},show:function(){w.value=!1},hide:function(){w.value=!0},reset:function(){y.value=0,L()},add:function(e){if(g.value===S.value.length)return S.value.push(e),S.value.length-1;{const t=g.value%S.value.length;return S.value.splice(t,0,e),t+1}},push:function(e){return S.value.push(e),S.value.length-1},insert:B}}});const p={ref:\"container\",class:\"vue-danmaku\"};!function(e,t){void 0===t&&(t={});var n=t.insertAt;if(e&&\"undefined\"!=typeof document){var a=document.head||document.getElementsByTagName(\"head\")[0],l=document.createElement(\"style\");l.type=\"text/css\",\"top\"===n&&a.firstChild?a.insertBefore(l,a.firstChild):a.appendChild(l),l.styleSheet?l.styleSheet.cssText=e:l.appendChild(document.createTextNode(e))}}(\".vue-danmaku {\\n  position: relative;\\n  overflow: hidden;\\n}\\n.vue-danmaku .danmus {\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  opacity: 0;\\n  -webkit-transition: all 0.3s;\\n  transition: all 0.3s;\\n}\\n.vue-danmaku .danmus.show {\\n  opacity: 1;\\n}\\n.vue-danmaku .danmus.paused .dm.move {\\n  animation-play-state: paused;\\n}\\n.vue-danmaku .danmus .dm {\\n  position: absolute;\\n  font-size: 20px;\\n  color: #ddd;\\n  white-space: pre;\\n  transform: translateX(0);\\n  transform-style: preserve-3d;\\n}\\n.vue-danmaku .danmus .dm.move {\\n  will-change: transform;\\n  animation-name: moveLeft;\\n  animation-timing-function: linear;\\n  animation-play-state: running;\\n}\\n.vue-danmaku .danmus .dm.pause {\\n  animation-play-state: paused;\\n  z-index: 100;\\n}\\n@keyframes moveLeft {\\n  from {\\n    transform: translateX(0);\\n  }\\n  to {\\n    transform: translateX(var(--dm-scroll-width));\\n  }\\n}\\n@-webkit-keyframes moveLeft {\\n  from {\\n    -webkit-transform: translateX(0);\\n  }\\n  to {\\n    -webkit-transform: translateX(var(--dm-scroll-width));\\n  }\\n}\"),f.render=function(e,t,n,a,l,u){return d(),r(\"div\",p,[m(\"div\",{ref:\"dmContainer\",class:v([\"danmus\",{show:!e.hidden},{paused:e.paused}])},null,2),c(e.$slots,\"default\")],512)},f.__file=\"src/lib/Danmaku.vue\";export{f as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAsP,IAAI,IAAE,gBAAE,EAAC,MAAK,gBAAe,YAAW,CAAC,GAAE,OAAM,EAAC,QAAO,EAAC,MAAK,OAAM,UAAS,MAAG,SAAQ,MAAI,CAAC,EAAC,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,UAAS,EAAC,MAAK,SAAQ,SAAQ,KAAE,GAAE,MAAK,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,SAAQ,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,QAAO,EAAC,MAAK,QAAO,SAAQ,IAAG,GAAE,eAAc,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,UAAS,EAAC,MAAK,QAAO,SAAQ,GAAE,GAAE,KAAI,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,OAAM,EAAC,MAAK,QAAO,SAAQ,EAAC,GAAE,WAAU,EAAC,MAAK,SAAQ,SAAQ,MAAE,GAAE,YAAW,EAAC,MAAK,QAAO,SAAQ,GAAE,EAAC,GAAE,OAAM,CAAC,YAAW,YAAW,WAAU,UAAS,eAAe,GAAE,MAAM,GAAE,EAAC,MAAK,GAAE,OAAM,EAAC,GAAE;AAAC,MAAI,IAAE,IAAE,SAAS,cAAc,KAAK,CAAC,GAAE,IAAE,IAAE,SAAS,cAAc,KAAK,CAAC;AAAE,QAAM,IAAE,IAAE,CAAC,GAAEA,KAAE,IAAE,CAAC;AAAE,MAAIC,KAAE;AAAE,QAAMC,KAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,CAAC,GAAE,IAAE,IAAE,KAAE,GAAE,IAAE,IAAE,KAAE,GAAE,IAAE,IAAE,CAAC,CAAC,GAAE,IAAE,SAASC,IAAE,GAAE,IAAE,cAAa,GAAE;AAAC,WAAO,SAAE,EAAC,KAAI,MAAIA,GAAE,CAAC,GAAE,KAAI,OAAG;AAAC,QAAE,UAAU,CAAC,IAAG,IAAE,EAAE,CAAC,IAAE,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC,EAAE,GAAE,GAAE,QAAQ,GAAE,IAAE,SAAE,EAAC,UAAS,SAAG,MAAI,EAAE,YAAUD,GAAE,KAAM,GAAE,UAAS,SAAG,MAAI,EAAE,QAAS,GAAE,MAAK,SAAG,MAAI,EAAE,IAAK,GAAE,SAAQ,SAAG,MAAI,EAAE,OAAQ,GAAE,UAAS,SAAG,MAAI,EAAE,QAAS,GAAE,eAAc,SAAG,MAAI,EAAE,aAAc,EAAC,CAAC,GAAE,IAAE,SAAE,EAAC,QAAO,SAAG,MAAI,EAAE,KAAM,GAAE,UAAS,SAAG,MAAI,EAAE,QAAS,GAAE,QAAO,SAAG,MAAI,EAAE,MAAO,GAAE,KAAI,SAAG,MAAI,EAAE,GAAI,GAAE,OAAM,SAAG,MAAI,EAAE,KAAM,EAAC,CAAC;AAAE,WAAS,IAAG;AAAC,MAAE,GAAE,EAAE,aAAW,WAAU;AAAC,UAAI,IAAE,CAAC;AAAE,QAAE,MAAM,iBAAiB,aAAa,CAAAC,OAAG;AAAC,YAAI,IAAEA,GAAE;AAAO,UAAE,UAAU,SAAS,IAAI,MAAI,IAAE,EAAE,QAAQ,KAAK,KAAG,IAAG,EAAE,UAAU,SAAS,IAAI,MAAI,EAAE,SAAS,CAAC,MAAI,EAAE,WAAU,EAAC,IAAG,EAAC,CAAC,GAAE,EAAE,UAAU,IAAI,OAAO,GAAE,EAAE,KAAK,CAAC;AAAA,MAAG,CAAE,GAAE,EAAE,MAAM,iBAAiB,YAAY,CAAAA,OAAG;AAAC,YAAI,IAAEA,GAAE;AAAO,UAAE,UAAU,SAAS,IAAI,MAAI,IAAE,EAAE,QAAQ,KAAK,KAAG,IAAG,EAAE,UAAU,SAAS,IAAI,MAAI,EAAE,UAAS,EAAC,IAAG,EAAC,CAAC,GAAE,EAAE,UAAU,OAAO,OAAO,GAAE,EAAE,QAAS,CAAAC,OAAG;AAAC,UAAAA,GAAE,UAAU,OAAO,OAAO;AAAA,QAAC,CAAE,GAAE,IAAE,CAAC;AAAA,MAAE,CAAE;AAAA,IAAC,EAAE,GAAE,EAAE,YAAU,EAAE;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,QAAG,EAAE,QAAM,EAAE,MAAM,aAAYJ,GAAE,QAAM,EAAE,MAAM,cAAa,MAAI,EAAE,SAAO,MAAIA,GAAE,MAAM,OAAM,IAAI,MAAM,UAAU;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,MAAE,QAAM,OAAGC,OAAIA,KAAE,OAAO,YAAa,MAAI,WAAU;AAAC,UAAG,CAAC,EAAE,SAAO,EAAE,MAAM,OAAO,KAAG,EAAE,QAAM,EAAE,MAAM,SAAO,GAAE;AAAC,cAAM,IAAE,EAAE,MAAM,SAAS;AAAO,UAAE,SAAO,IAAE,EAAE,UAAQ,EAAE,UAAU,GAAE,EAAE,QAAM,IAAG,EAAE;AAAA,MAAE,MAAM,GAAE;AAAA,IAAC,EAAE,GAAG,EAAE,QAAQ;AAAA,EAAE;AAAC,WAAS,EAAE,GAAE;AAAC,UAAM,IAAE,EAAE,OAAK,EAAE,QAAM,EAAE,MAAM,SAAO,EAAE,OAAM,IAAE,KAAG,EAAE,MAAM,CAAC;AAAE,QAAI,IAAE,SAAS,cAAc,KAAK;AAAE,MAAE,UAAQ,IAAE,SAASG,IAAED,IAAE;AAAC,YAAME,KAAE,UAAE,EAAC,QAAO,MAAI,EAAE,OAAM,CAAC,GAAE,CAAC,EAAE,MAAI,EAAE,GAAG,EAAC,OAAMD,IAAE,OAAMD,GAAC,CAAC,CAAC,CAAC,EAAC,CAAC;AAAE,aAAOE,GAAE,MAAM,SAAS,cAAc,KAAK,CAAC;AAAA,IAAC,EAAE,GAAE,CAAC,EAAE,OAAK,EAAE,YAAU,GAAE,EAAE,aAAa,SAAQ,EAAE,UAAU,GAAE,EAAE,MAAM,WAAS,GAAG,EAAE,QAAQ,MAAK,EAAE,MAAM,aAAW,GAAG,EAAE,QAAQ,OAAM,EAAE,UAAU,IAAI,IAAI,GAAE,EAAE,MAAM,YAAY,CAAC,GAAE,EAAE,MAAM,UAAQ,KAAI,SAAG,MAAI;AAAC,QAAE,WAAS,EAAE,QAAM,EAAE,eAAc,EAAE,aAAWH,GAAE,QAAM,KAAK,MAAMF,GAAE,SAAO,EAAE,SAAO,EAAE,IAAI;AAAG,UAAII,KAAE,SAASA,IAAE;AAAC,YAAID,KAAE,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC;AAAE,UAAE,kBAAgBA,KAAEA,GAAE,KAAM,MAAI,MAAG,KAAK,OAAO,CAAE;AAAG,iBAAQE,MAAKF,IAAE;AAAC,gBAAMA,KAAE,EAAE,MAAME,EAAC;AAAE,cAAG,CAACF,MAAG,CAACA,GAAE,OAAO,QAAO,EAAE,MAAME,EAAC,IAAE,CAACD,EAAC,GAAEA,GAAE,iBAAiB,gBAAgB,MAAI,EAAE,MAAMC,EAAC,EAAE,OAAO,GAAE,CAAC,CAAE,GAAEA,KAAE,EAAE;AAAS,mBAAQC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,MAAI;AAAC,kBAAMC,KAAE,EAAEJ,GAAEG,EAAC,CAAC,IAAE;AAAG,gBAAGC,MAAG,QAAKH,GAAE,cAAYD,GAAEG,EAAC,EAAE,gBAAcC,MAAG,EAAE;AAAM,gBAAGD,OAAIH,GAAE,SAAO,EAAE,QAAO,EAAE,MAAME,EAAC,EAAE,KAAKD,EAAC,GAAEA,GAAE,iBAAiB,gBAAgB,MAAI,EAAE,MAAMC,EAAC,EAAE,OAAO,GAAE,CAAC,CAAE,GAAEA,KAAE,EAAE;AAAA,UAAQ;AAAA,QAAC;AAAC,eAAM;AAAA,MAAE,EAAE,CAAC;AAAE,UAAGD,MAAG,GAAE;AAAC,cAAMD,KAAE,EAAE,aAAYG,KAAE,EAAE;AAAO,UAAE,UAAU,IAAI,MAAM,GAAE,EAAE,QAAQ,QAAM,GAAG,CAAC,IAAG,EAAE,QAAQ,UAAQF,GAAE,SAAS,GAAE,EAAE,MAAM,UAAQ,KAAI,EAAE,MAAM,MAAIA,MAAGE,KAAE,EAAE,OAAK,MAAK,EAAE,MAAM,QAAMH,KAAE,EAAE,QAAM,MAAK,EAAE,MAAM,YAAY,qBAAoB,IAAI,EAAE,QAAMA,EAAC,IAAI,GAAE,EAAE,MAAM,OAAK,GAAG,EAAE,KAAK,MAAK,EAAE,MAAM,oBAAkB,EAAE,QAAM,EAAE,SAAO,KAAI,EAAE,iBAAiB,gBAAgB,MAAI;AAAC,iBAAO,EAAE,QAAQ,KAAK,MAAI,EAAE,MAAM,SAAO,KAAG,EAAE,QAAM,EAAE,YAAW,EAAE,QAAQ,KAAK,GAAE,EAAE,SAAO,EAAE,MAAM,YAAY,CAAC;AAAA,QAAC,CAAE,GAAE,EAAE;AAAA,MAAO,MAAM,GAAE,MAAM,YAAY,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC,WAAS,EAAE,GAAE;AAAC,UAAMA,KAAE,EAAE,eAAa,SAAS,EAAE,MAAM,KAAK,GAAE,IAAE,EAAE,sBAAsB,EAAE,SAAO,EAAE,MAAM,sBAAsB,EAAE,QAAMA;AAAE,WAAO,EAAE,MAAM,sBAAsB,EAAE,QAAM;AAAA,EAAC;AAAC,WAAS,IAAG;AAAC,kBAAcF,EAAC,GAAEA,KAAE,GAAE,EAAE,QAAM;AAAA,EAAC;AAAC,SAAO,UAAG,MAAI;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,gBAAG,MAAI;AAAC,MAAE;AAAA,EAAC,CAAE,GAAE,EAAC,WAAU,GAAE,aAAY,GAAE,QAAO,GAAE,QAAO,GAAE,WAAU,GAAE,cAAa,WAAU;AAAC,WAAM,CAAC,EAAE;AAAA,EAAK,GAAE,QAAO,WAAU;AAAC,MAAE;AAAE,UAAM,IAAE,EAAE,MAAM,uBAAuB,IAAI;AAAE,aAAQE,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,YAAM,IAAE,EAAEA,EAAC;AAAE,QAAE,MAAM,YAAY,qBAAoB,IAAI,EAAE,QAAM,EAAE,WAAW,IAAI,GAAE,EAAE,MAAM,OAAK,GAAG,EAAE,KAAK,MAAK,EAAE,MAAM,oBAAkB,EAAE,QAAM,EAAE,SAAO;AAAA,IAAG;AAAA,EAAC,GAAE,MAAK,GAAE,OAAM,WAAU;AAAC,MAAE,QAAM;AAAA,EAAE,GAAE,MAAK,WAAU;AAAC,MAAE,QAAM,CAAC,GAAE,EAAE,MAAM,YAAU,IAAG,EAAE,QAAM,MAAG,EAAE,QAAM,OAAG,EAAE;AAAA,EAAC,GAAE,MAAK,WAAU;AAAC,MAAE,QAAM;AAAA,EAAE,GAAE,MAAK,WAAU;AAAC,MAAE,QAAM;AAAA,EAAE,GAAE,OAAM,WAAU;AAAC,MAAE,QAAM,GAAE,EAAE;AAAA,EAAC,GAAE,KAAI,SAAS,GAAE;AAAC,QAAG,EAAE,UAAQ,EAAE,MAAM,OAAO,QAAO,EAAE,MAAM,KAAK,CAAC,GAAE,EAAE,MAAM,SAAO;AAAE;AAAC,YAAMA,KAAE,EAAE,QAAM,EAAE,MAAM;AAAO,aAAO,EAAE,MAAM,OAAOA,IAAE,GAAE,CAAC,GAAEA,KAAE;AAAA,IAAC;AAAA,EAAC,GAAE,MAAK,SAAS,GAAE;AAAC,WAAO,EAAE,MAAM,KAAK,CAAC,GAAE,EAAE,MAAM,SAAO;AAAA,EAAC,GAAE,QAAO,EAAC;AAAC,EAAC,CAAC;AAAE,IAAM,IAAE,EAAC,KAAI,aAAY,OAAM,cAAa;AAAE,CAAC,SAAS,GAAE,GAAE;AAAC,aAAS,MAAI,IAAE,CAAC;AAAG,MAAI,IAAE,EAAE;AAAS,MAAG,KAAG,eAAa,OAAO,UAAS;AAAC,QAAI,IAAE,SAAS,QAAM,SAAS,qBAAqB,MAAM,EAAE,CAAC,GAAE,IAAE,SAAS,cAAc,OAAO;AAAE,MAAE,OAAK,YAAW,UAAQ,KAAG,EAAE,aAAW,EAAE,aAAa,GAAE,EAAE,UAAU,IAAE,EAAE,YAAY,CAAC,GAAE,EAAE,aAAW,EAAE,WAAW,UAAQ,IAAE,EAAE,YAAY,SAAS,eAAe,CAAC,CAAC;AAAA,EAAC;AAAC,EAAE,+jCAA+jC,GAAE,EAAE,SAAO,SAAS,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;AAAC,SAAO,UAAE,GAAE,mBAAE,OAAM,GAAE,CAAC,gBAAE,OAAM,EAAC,KAAI,eAAc,OAAM,eAAE,CAAC,UAAS,EAAC,MAAK,CAAC,EAAE,OAAM,GAAE,EAAC,QAAO,EAAE,OAAM,CAAC,CAAC,EAAC,GAAE,MAAK,CAAC,GAAE,WAAE,EAAE,QAAO,SAAS,CAAC,GAAE,GAAG;AAAC,GAAE,EAAE,SAAO;", "names": ["f", "p", "h", "t", "e", "n", "a", "l"]}