using Microsoft.EntityFrameworkCore;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 设备服务
/// </summary>
public class DeviceService : BaseService<Device>
{
    public DeviceService(WcsDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 根据设备编码查找设备
    /// </summary>
    /// <param name="deviceCode">设备编码</param>
    /// <returns>设备信息</returns>
    public async Task<Device?> FindByCodeAsync(string deviceCode)
    {
        return await _dbSet.FirstOrDefaultAsync(d => d.DeviceCode == deviceCode);
    }

    /// <summary>
    /// 根据巷道ID获取设备列表
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>设备列表</returns>
    public async Task<List<Device>> GetDevicesByLaneAsync(ulong laneId)
    {
        return await _dbSet
            // 暂时注释掉，因为Device模型已更改
            // .Where(d => d.LaneId == laneId && d.Status != 3) // 排除维护状态
            .ToListAsync();
    }

    /// <summary>
    /// 根据设备类型获取设备列表
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>设备列表</returns>
    public async Task<List<Device>> GetDevicesByTypeAsync(sbyte deviceType)
    {
        return await _dbSet
            .Where(d => d.DeviceType == deviceType)
            .ToListAsync();
    }

    /// <summary>
    /// 更新设备状态
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="status">状态</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateStatusAsync(uint deviceId, sbyte status)
    {
        return await UpdateAsync(deviceId, new Dictionary<string, object> { { "Status", status } });
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    public async Task<Dictionary<string, int>> GetDeviceStatisticsAsync()
    {
        var stats = await _dbSet
            .GroupBy(d => d.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToListAsync();

        var result = new Dictionary<string, int>
        {
            { "Normal", 0 },     // 正常
            { "Fault", 0 },      // 故障
            { "Maintenance", 0 } // 维护
        };

        foreach (var stat in stats)
        {
            switch (stat.Status)
            {
                case 1: result["Normal"] = stat.Count; break;
                case 2: result["Fault"] = stat.Count; break;
                case 3: result["Maintenance"] = stat.Count; break;
            }
        }

        return result;
    }

    /// <summary>
    /// 搜索设备
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    public async Task<(List<Device> Items, long Total)> SearchDevicesAsync(string keyword, int pageIndex, int pageSize)
    {
        return await GetPagedAsync(pageIndex, pageSize, query =>
            query.Where(d => d.DeviceCode.Contains(keyword) || 
                           d.DeviceName.Contains(keyword) || 
                           d.Addr.Contains(keyword))
                 .OrderBy(d => d.Id));
    }

    /// <summary>
    /// 检查设备编码是否已存在
    /// </summary>
    /// <param name="deviceCode">设备编码</param>
    /// <param name="excludeId">排除的设备ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsDeviceCodeExistsAsync(string deviceCode, ulong? excludeId = null)
    {
        var query = _dbSet.Where(d => d.DeviceCode == deviceCode);
        if (excludeId.HasValue)
        {
            query = query.Where(d => d.Id != excludeId.Value);
        }
        return await query.AnyAsync();
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="device">设备对象</param>
    /// <returns>创建的设备</returns>
    public override async Task<Device> CreateAsync(Device device)
    {
        // 检查设备编码是否已存在
        if (await IsDeviceCodeExistsAsync(device.DeviceCode))
        {
            throw new InvalidOperationException("设备编码已存在");
        }

        return await base.CreateAsync(device);
    }
}
