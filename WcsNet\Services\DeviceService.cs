using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 设备服务
/// </summary>
public class DeviceService
{
    private readonly WcsDbContext _context;

    public DeviceService(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取设备列表
    /// </summary>
    public async Task<PagedResponse<DeviceDto>> GetDeviceListAsync(DeviceListRequest request)
    {
        var query = _context.Devices.AsQueryable();

        // 条件筛选
        if (!string.IsNullOrEmpty(request.DeviceName))
        {
            query = query.Where(d => d.DeviceName.Contains(request.DeviceName));
        }

        if (!string.IsNullOrEmpty(request.DeviceCode))
        {
            query = query.Where(d => d.DeviceCode.Contains(request.DeviceCode));
        }

        if (request.DeviceType.HasValue)
        {
            query = query.Where(d => d.DeviceType == request.DeviceType.Value);
        }

        if (request.Status.HasValue)
        {
            query = query.Where(d => d.Status == request.Status.Value);
        }

        // 总数
        var total = await query.CountAsync();

        // 分页查询
        var devices = await query
            .OrderByDescending(d => d.CreatedAt)
            .Skip((request.PageNo - 1) * request.PageSize)
            .Take(request.PageSize)
            .Select(d => new DeviceDto
            {
                Id = d.Id,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceType = d.DeviceType,
                CommType = d.CommType,
                Addr = d.Addr,
                Port = d.Port,
                Online = d.Online,
                RunStatus = d.RunStatus,
                Status = d.Status,
                Position = d.Position,
                CreatedAt = d.CreatedAt,
                UpdatedAt = d.UpdatedAt
            })
            .ToListAsync();

        return new PagedResponse<DeviceDto>
        {
            Items = devices,
            Total = total
        };
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    public async Task<DeviceDto?> GetDeviceByIdAsync(ulong id)
    {
        var device = await _context.Devices
            .Where(d => d.Id == id)
            .Select(d => new DeviceDto
            {
                Id = d.Id,
                DeviceCode = d.DeviceCode,
                DeviceName = d.DeviceName,
                DeviceType = d.DeviceType,
                CommType = d.CommType,
                Addr = d.Addr,
                Port = d.Port,
                Online = d.Online,
                RunStatus = d.RunStatus,
                Status = d.Status,
                Position = d.Position,
                CreatedAt = d.CreatedAt,
                UpdatedAt = d.UpdatedAt
            })
            .FirstOrDefaultAsync();

        return device;
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    public async Task<DeviceDto> CreateDeviceAsync(DeviceRequest request, ulong userId)
    {
        // 检查设备编号是否已存在
        var exists = await _context.Devices.AnyAsync(d => d.DeviceCode == request.DeviceCode);
        if (exists)
        {
            throw new InvalidOperationException($"设备编号 {request.DeviceCode} 已存在");
        }

        var device = new Device
        {
            DeviceCode = request.DeviceCode,
            DeviceName = request.DeviceName,
            DeviceType = request.DeviceType,
            CommType = request.CommType,
            Addr = request.Addr,
            Port = request.Port,
            Status = request.Status,
            Position = request.Position,
            CreatedId = (long)userId,
            UpdatedId = (long)userId,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };

        _context.Devices.Add(device);
        await _context.SaveChangesAsync();

        return new DeviceDto
        {
            Id = device.Id,
            DeviceCode = device.DeviceCode,
            DeviceName = device.DeviceName,
            DeviceType = device.DeviceType,
            CommType = device.CommType,
            Addr = device.Addr,
            Port = device.Port,
            Online = device.Online,
            RunStatus = device.RunStatus,
            Status = device.Status,
            Position = device.Position,
            CreatedAt = device.CreatedAt,
            UpdatedAt = device.UpdatedAt
        };
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    public async Task<bool> UpdateDeviceAsync(ulong id, DeviceRequest request, ulong userId)
    {
        var device = await _context.Devices.FindAsync(id);
        if (device == null)
        {
            return false;
        }

        // 检查设备编号是否已被其他设备使用
        var exists = await _context.Devices.AnyAsync(d => d.DeviceCode == request.DeviceCode && d.Id != id);
        if (exists)
        {
            throw new InvalidOperationException($"设备编号 {request.DeviceCode} 已存在");
        }

        device.DeviceCode = request.DeviceCode;
        device.DeviceName = request.DeviceName;
        device.DeviceType = request.DeviceType;
        device.CommType = request.CommType;
        device.Addr = request.Addr;
        device.Port = request.Port;
        device.Status = request.Status;
        device.Position = request.Position;
        device.UpdatedId = (long)userId;
        device.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    public async Task<bool> DeleteDeviceAsync(ulong id)
    {
        var device = await _context.Devices.FindAsync(id);
        if (device == null)
        {
            return false;
        }

        // 删除相关的设备属性
        var properties = await _context.DeviceProperties.Where(p => p.DeviceId == id).ToListAsync();
        _context.DeviceProperties.RemoveRange(properties);

        _context.Devices.Remove(device);
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 获取设备属性列表
    /// </summary>
    public async Task<List<DevicePropertyDto>> GetDevicePropertiesAsync(ulong deviceId)
    {
        var properties = await _context.DeviceProperties
            .Where(p => p.DeviceId == deviceId)
            .OrderBy(p => p.CreatedAt)
            .Select(p => new DevicePropertyDto
            {
                Id = p.Id,
                DeviceId = p.DeviceId,
                PropCode = p.PropCode,
                PropName = p.PropName,
                Addr = p.Addr,
                Direction = p.Direction,
                ModbusType = p.ModbusType,
                PlcType = p.PlcType,
                PropLength = p.PropLength,
                Remark = "", // 数据库中暂时没有此字段，设置默认值
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            })
            .ToListAsync();

        return properties;
    }

    /// <summary>
    /// 根据ID获取设备属性
    /// </summary>
    public async Task<DevicePropertyDto?> GetDevicePropertyByIdAsync(ulong id)
    {
        var property = await _context.DeviceProperties
            .Where(p => p.Id == id)
            .Select(p => new DevicePropertyDto
            {
                Id = p.Id,
                DeviceId = p.DeviceId,
                PropCode = p.PropCode,
                PropName = p.PropName,
                Addr = p.Addr,
                Direction = p.Direction,
                ModbusType = p.ModbusType,
                PlcType = p.PlcType,
                PropLength = p.PropLength,
                Remark = "", // 数据库中暂时没有此字段，设置默认值
                CreatedAt = p.CreatedAt,
                UpdatedAt = p.UpdatedAt
            })
            .FirstOrDefaultAsync();

        return property;
    }

    /// <summary>
    /// 获取设备属性列表
    /// </summary>
    public async Task<List<DevicePropertyDto>> GetDevicePropertiesAsync()
    {
        var properties = await _context.DeviceProperties
            .OrderBy(p => p.Id)
            .ToListAsync();

        return properties.Select(p => new DevicePropertyDto
        {
            Id = p.Id,
            DeviceId = p.DeviceId,
            PropCode = p.PropCode,
            PropName = p.PropName,
            Addr = p.Addr,
            Direction = p.Direction,
            ModbusType = p.ModbusType,
            PlcType = p.PlcType,
            PropLength = p.PropLength,
            CreatedAt = p.CreatedAt ?? DateTime.Now,
            UpdatedAt = p.UpdatedAt ?? DateTime.Now
        }).ToList();
    }

    /// <summary>
    /// 创建设备属性
    /// </summary>
    public async Task<DevicePropertyDto> CreateDevicePropertyAsync(DevicePropertyRequest request, ulong userId)
    {
        var property = new DeviceProperty
        {
            DeviceId = request.DeviceId,
            PropCode = request.PropCode,
            PropName = request.PropName,
            Addr = request.Addr,
            Direction = request.Direction,
            ModbusType = request.ModbusType,
            PlcType = request.PlcType,
            PropLength = request.PropLength,
            // Remark = request.Remark, // 数据库中暂时没有此字段
            CreatedId = (long)userId,
            UpdatedId = (long)userId,
            CreatedAt = DateTime.Now,
            UpdatedAt = DateTime.Now
        };

        _context.DeviceProperties.Add(property);
        await _context.SaveChangesAsync();

        return new DevicePropertyDto
        {
            Id = property.Id,
            DeviceId = property.DeviceId,
            PropCode = property.PropCode,
            PropName = property.PropName,
            Addr = property.Addr,
            Direction = property.Direction,
            ModbusType = property.ModbusType,
            PlcType = property.PlcType,
            PropLength = property.PropLength,
            Remark = "", // 数据库中暂时没有此字段，设置默认值
            CreatedAt = property.CreatedAt,
            UpdatedAt = property.UpdatedAt
        };
    }

    /// <summary>
    /// 更新设备属性
    /// </summary>
    public async Task<bool> UpdateDevicePropertyAsync(ulong id, DevicePropertyRequest request, ulong userId)
    {
        var property = await _context.DeviceProperties.FindAsync(id);
        if (property == null)
        {
            return false;
        }

        property.PropCode = request.PropCode;
        property.PropName = request.PropName;
        property.Addr = request.Addr;
        property.Direction = request.Direction;
        property.ModbusType = request.ModbusType;
        property.PlcType = request.PlcType;
        property.PropLength = request.PropLength;
        // property.Remark = request.Remark; // 数据库中暂时没有此字段
        property.UpdatedId = (long)userId;
        property.UpdatedAt = DateTime.Now;

        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 删除设备属性
    /// </summary>
    public async Task<bool> DeleteDevicePropertyAsync(ulong id)
    {
        var property = await _context.DeviceProperties.FindAsync(id);
        if (property == null)
        {
            return false;
        }

        _context.DeviceProperties.Remove(property);
        await _context.SaveChangesAsync();
        return true;
    }
}
