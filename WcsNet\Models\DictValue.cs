﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_dict_value")]
public class DictValue : BaseModel
{
    [Column("dict_id")]
    public ulong DictId { get; set; }

    [Required]
    [StringLength(128)]
    [Column("dict_label")]
    public string DictLabel { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    [Column("dict_value")]
    public string DictValueText { get; set; } = string.Empty;

    [Column("sort_number")]
    public long SortNumber { get; set; } = 0;

    public sbyte Status { get; set; } = 1;
}
