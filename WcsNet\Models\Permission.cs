using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_permission")]
public class Permission : BaseModel
{
    [Column("parent_id")]
    public ulong ParentId { get; set; } = 0;

    [Required]
    [StringLength(64)]
    [Column("title")]
    public string Title { get; set; } = string.Empty;

    [StringLength(255)]
    [Column("route_path")]
    public string RoutePath { get; set; } = string.Empty;

    [StringLength(128)]
    [Column("name")]
    public string Name { get; set; } = string.Empty;

    [StringLength(255)]
    [Column("component")]
    public string Component { get; set; } = string.Empty;

    [StringLength(255)]
    [Column("redirect")]
    public string Redirect { get; set; } = string.Empty;

    [Column("rank")]
    public int Rank { get; set; } = 0;

    [StringLength(128)]
    [Column("icon")]
    public string Icon { get; set; } = string.Empty;

    [StringLength(128)]
    [Column("extra_icon")]
    public string ExtraIcon { get; set; } = string.Empty;

    [StringLength(128)]
    [Column("active_path")]
    public string ActivePath { get; set; } = string.Empty;

    [StringLength(191)]
    [Column("frame_src")]
    public string FrameSrc { get; set; } = string.Empty;

    [Column("frame_loading")]
    public sbyte FrameLoading { get; set; } = 1;

    [Column("show_link")]
    public sbyte ShowLink { get; set; } = 0;

    [Column("hide_tag")]
    public sbyte HideTag { get; set; } = 0;

    [Column("keepalive")]
    public sbyte Keepalive { get; set; } = 0;

    [Column("fixed_tag")]
    public sbyte FixedTag { get; set; } = 0;

    [Column("show_parent")]
    public sbyte ShowParent { get; set; } = 0;

    [StringLength(128)]
    [Column("perm_code")]
    public string PermCode { get; set; } = string.Empty;

    [Column("perm_type")]
    public sbyte PermType { get; set; } = 1;
}
