using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

/// <summary>
/// 权限/菜单实体（与Go版本Permission保持一致）
/// </summary>
[Table("sys_permission")]
public class Permission
{
    /// <summary>
    /// 权限ID
    /// </summary>
    [Key]
    public uint Id { get; set; }

    /// <summary>
    /// 权限类型 0:菜单 1:frame 2:外链 3:按钮
    /// </summary>
    [Column("perm_type")]
    public sbyte PermType { get; set; }

    /// <summary>
    /// 菜单名称
    /// </summary>
    [Column("title")]
    [StringLength(64)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 父级ID
    /// </summary>
    [Column("parent_id")]
    public uint ParentId { get; set; }

    /// <summary>
    /// 路由地址
    /// </summary>
    [Column("route_path")]
    [StringLength(255)]
    public string RoutePath { get; set; } = string.Empty;

    /// <summary>
    /// 组件名称
    /// </summary>
    [Column("name")]
    [StringLength(128)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 前端组件路径
    /// </summary>
    [Column("component")]
    [StringLength(255)]
    public string Component { get; set; } = string.Empty;

    /// <summary>
    /// 重定向地址
    /// </summary>
    [Column("redirect")]
    [StringLength(255)]
    public string Redirect { get; set; } = string.Empty;

    /// <summary>
    /// 排序值
    /// </summary>
    [Column("rank")]
    public int Rank { get; set; }

    /// <summary>
    /// 权限图标
    /// </summary>
    [Column("icon")]
    [StringLength(128)]
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 右侧图标
    /// </summary>
    [Column("extra_icon")]
    [StringLength(128)]
    public string ExtraIcon { get; set; } = string.Empty;

    /// <summary>
    /// 菜单激活
    /// </summary>
    [Column("active_path")]
    [StringLength(128)]
    public string ActivePath { get; set; } = string.Empty;

    /// <summary>
    /// iframe链接地址
    /// </summary>
    [Column("frame_src")]
    [StringLength(191)]
    public string FrameSrc { get; set; } = string.Empty;

    /// <summary>
    /// 首次加载动画 1有 0无
    /// </summary>
    [Column("frame_loading")]
    public sbyte FrameLoading { get; set; } = 1;

    /// <summary>
    /// 是否在菜单中显示 0否 1是
    /// </summary>
    [Column("show_link")]
    public sbyte ShowLink { get; set; }

    /// <summary>
    /// 是否隐藏 0否 1是
    /// </summary>
    [Column("hide_tag")]
    public sbyte HideTag { get; set; }

    /// <summary>
    /// 是否缓存 0不缓存 1缓存
    /// </summary>
    [Column("keepalive")]
    public sbyte Keepalive { get; set; }

    /// <summary>
    /// 固定标签 0不固定 1固定
    /// </summary>
    [Column("fixed_tag")]
    public sbyte FixedTag { get; set; }

    /// <summary>
    /// 显示父菜单 0不显示 1显示
    /// </summary>
    [Column("show_parent")]
    public sbyte ShowParent { get; set; }

    /// <summary>
    /// 权限标识，在按钮时使用
    /// </summary>
    [Column("perm_code")]
    [StringLength(128)]
    public string PermCode { get; set; } = string.Empty;
}
