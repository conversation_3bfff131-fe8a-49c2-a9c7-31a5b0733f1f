using Microsoft.EntityFrameworkCore;
using WcsNet.Models;
using WcsNet.Dispatch;

namespace WcsNet.Services;

/// <summary>
/// 工作任务服务
/// </summary>
public class WorkService : BaseService<Work>
{
    public WorkService(WcsDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 创建工作任务
    /// </summary>
    /// <param name="work">工作任务</param>
    /// <returns>创建的工作任务</returns>
    public override async Task<Work> CreateAsync(Work work)
    {
        // 检查箱子编码是否已存在
        var existingWork = await _dbSet.FirstOrDefaultAsync(w => w.ItemCode == work.ItemCode);
        if (existingWork != null)
        {
            throw new InvalidOperationException("箱子编码已存在");
        }

        return await base.CreateAsync(work);
    }

    /// <summary>
    /// 根据箱子编码查找任务
    /// </summary>
    /// <param name="itemCode">箱子编码</param>
    /// <returns>工作任务</returns>
    public async Task<Work?> FindByItemCodeAsync(string itemCode)
    {
        return await _dbSet.FirstOrDefaultAsync(w => w.ItemCode == itemCode);
    }

    /// <summary>
    /// 获取指定巷道的待执行任务
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>待执行任务列表</returns>
    public async Task<List<Work>> GetPendingWorksByLaneAsync(uint laneId)
    {
        return await _dbSet
            .Where(w => w.LaneId == laneId && w.Status == 1) // 1未执行
            .OrderBy(w => w.Id)
            .ToListAsync();
    }

    /// <summary>
    /// 获取正在执行的任务
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>正在执行的任务列表</returns>
    public async Task<List<Work>> GetRunningWorksByLaneAsync(uint laneId)
    {
        return await _dbSet
            .Where(w => w.LaneId == laneId && w.Status == 2) // 2执行中
            .ToListAsync();
    }

    /// <summary>
    /// 更新任务状态
    /// </summary>
    /// <param name="workId">任务ID</param>
    /// <param name="status">状态</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateStatusAsync(uint workId, sbyte status)
    {
        var updates = new Dictionary<string, object> { { "Status", status } };
        
        // 根据新的状态定义更新
        // 1未执行 2执行中 3执行失败 4执行成功

        return await UpdateAsync(workId, updates);
    }

    /// <summary>
    /// 根据任务类型获取任务
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>任务列表</returns>
    public async Task<(List<Work> Items, long Total)> GetWorksByTypeAsync(TaskType taskType, int pageIndex, int pageSize)
    {
        return await GetPagedAsync(pageIndex, pageSize, query =>
            query.Where(w => w.TaskType == (sbyte)taskType)
                 .OrderByDescending(w => w.Id));
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <returns>统计信息</returns>
    public async Task<Dictionary<string, int>> GetWorkStatisticsAsync(uint? laneId = null)
    {
        var query = _dbSet.AsQueryable();
        
        if (laneId.HasValue)
        {
            query = query.Where(w => w.LaneId == laneId.Value);
        }

        var stats = await query
            .GroupBy(w => w.Status)
            .Select(g => new { Status = g.Key, Count = g.Count() })
            .ToListAsync();

        var result = new Dictionary<string, int>
        {
            { "Pending", 0 },    // 未执行
            { "Running", 0 },    // 执行中
            { "Failed", 0 },     // 执行失败
            { "Completed", 0 }   // 执行成功
        };

        foreach (var stat in stats)
        {
            switch (stat.Status)
            {
                case 1: result["Pending"] = stat.Count; break;
                case 2: result["Running"] = stat.Count; break;
                case 3: result["Failed"] = stat.Count; break;
                case 4: result["Completed"] = stat.Count; break;
            }
        }

        return result;
    }

    /// <summary>
    /// 搜索任务
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    public async Task<(List<Work> Items, long Total)> SearchWorksAsync(string keyword, int pageIndex, int pageSize)
    {
        return await GetPagedAsync(pageIndex, pageSize, query =>
            query.Where(w => w.ItemCode.Contains(keyword) ||
                           w.Parameters.Contains(keyword))
                 .OrderByDescending(w => w.Id));
    }

    /// <summary>
    /// 获取任务的执行步骤
    /// </summary>
    /// <param name="workId">任务ID</param>
    /// <returns>执行步骤列表</returns>
    public async Task<List<WorkStep>> GetWorkStepsAsync(uint workId)
    {
        return await _context.WorkSteps
            .Where(ws => ws.WorkId == workId)
            .OrderBy(ws => ws.StepOrder)
            .ToListAsync();
    }

    /// <summary>
    /// 插入任务步骤
    /// </summary>
    /// <param name="steps">步骤列表</param>
    /// <returns>插入的步骤列表</returns>
    public async Task<List<WorkStep>> InsertStepsAsync(List<WorkStep> steps)
    {
        _context.WorkSteps.AddRange(steps);
        await _context.SaveChangesAsync();
        return steps;
    }

    /// <summary>
    /// 更新步骤状态
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <param name="status">状态</param>
    /// <param name="errorMsg">错误信息（可选）</param>
    /// <returns>是否更新成功</returns>
    public async Task<bool> UpdateStepStatusAsync(uint stepId, sbyte status, string? errorMsg = null)
    {
        var step = await _context.WorkSteps.FindAsync(stepId);
        if (step == null) return false;

        step.Status = status;

        if (status == 2) // 开始执行
        {
            step.StartTime = DateTime.UtcNow;
        }
        else if (status == 3 || status == 4) // 完成或失败
        {
            step.EndTime = DateTime.UtcNow;
        }

        if (!string.IsNullOrEmpty(errorMsg))
        {
            step.ErrorMsg = errorMsg;
        }

        await _context.SaveChangesAsync();
        return true;
    }
}
