{"Version": 1, "WorkspaceRootPath": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\configuration\\appconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\configuration\\appconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 28, "Children": [{"$type": "Bookmark", "Name": "ST:43:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:35:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:36:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:37:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:38:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:39:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:40:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:41:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:42:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:28:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:29:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:30:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:32:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:33:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:34:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:27:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "AppConfig.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Configuration\\AppConfig.cs", "RelativeDocumentMoniker": "WcsNet\\Configuration\\AppConfig.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Configuration\\AppConfig.cs", "RelativeToolTip": "WcsNet\\Configuration\\AppConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAswAUAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T07:00:54.986Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\appsettings.json", "RelativeDocumentMoniker": "WcsNet\\appsettings.json", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\appsettings.json", "RelativeToolTip": "WcsNet\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-23T07:00:52.175Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "UserController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\UserController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UserController.cs", "RelativeToolTip": "WcsNet\\Controllers\\UserController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T06:58:25.573Z", "EditorCaption": ""}]}]}]}