2025-06-23 09:53:15.467 +08:00 [INF] Executed DbCommand (102ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 09:53:15.568 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 09:53:15.619 +08:00 [ERR] Hosting failed to start
System.IO.IOException: Failed to bind to address http://[::]:8666: address already in use.
 ---> Microsoft.AspNetCore.Connections.AddressInUseException: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
 ---> System.Net.Sockets.SocketException (10048): 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
   at System.Net.Sockets.Socket.UpdateStatusAfterSocketErrorAndThrowException(SocketError error, Boolean disconnectOnFailure, String callerName)
   at System.Net.Sockets.Socket.DoBind(EndPoint endPointSnapshot, SocketAddress socketAddress)
   at System.Net.Sockets.Socket.Bind(EndPoint localEP)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportOptions.CreateDefaultBoundListenSocket(EndPoint endpoint)
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketConnectionListener.Bind()
   at Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.SocketTransportFactory.BindAsync(EndPoint endpoint, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.Infrastructure.TransportManager.BindAsync(EndPoint endPoint, ConnectionDelegate connectionDelegate, EndpointConfig endpointConfig, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.<>c__DisplayClass28_0`1.<<StartAsync>g__OnBind|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindEndpointAsync(ListenOptions endpoint, AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.ListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.AnyIPListenOptions.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.AddressesStrategy.BindAsync(AddressBindContext context, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.Internal.AddressBinder.BindAsync(ListenOptions[] listenOptions, AddressBindContext context, Func`2 useHttps, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.BindAsync(CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Server.Kestrel.Core.KestrelServerImpl.StartAsync[TContext](IHttpApplication`1 application, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Hosting.GenericWebHostService.StartAsync(CancellationToken cancellationToken)
   at Microsoft.Extensions.Hosting.Internal.Host.<StartAsync>b__15_1(IHostedService service, CancellationToken token)
   at Microsoft.Extensions.Hosting.Internal.Host.ForeachService[T](IEnumerable`1 services, CancellationToken token, Boolean concurrent, Boolean abortOnFirstException, List`1 exceptions, Func`3 operation)
2025-06-23 10:23:15.322 +08:00 [INF] 使用内存存储服务
2025-06-23 10:23:16.394 +08:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 10:24:40.183 +08:00 [INF] 使用内存存储服务
2025-06-23 10:24:41.245 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 10:24:41.323 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 10:24:41.360 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 10:24:41.362 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 10:24:41.363 +08:00 [INF] Hosting environment: Development
2025-06-23 10:24:41.364 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 10:25:18.674 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 10:25:18.706 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 10:25:18.711 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 10:25:18.713 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 10:25:18.729 +08:00 [INF] Route matched with {action = "GetApiInfo", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetApiInfo() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 10:25:18.744 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 10:25:18.810 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet) in 77.0643ms
2025-06-23 10:25:18.814 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 10:25:18.816 +08:00 [INF] 请求完成: GET / - 状态码: 200 - 耗时: 111ms - 响应大小: 386bytes
2025-06-23 10:25:18.824 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 200 null application/json; charset=utf-8 150.5293ms
2025-06-23 11:13:11.575 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/health - null null
2025-06-23 11:13:11.579 +08:00 [INF] 请求开始: GET /health - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 11:13:11.581 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:13:11.584 +08:00 [INF] Route matched with {action = "Health", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]] Health() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 11:13:11.586 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 11:13:11.597 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.Health (WcsNet) in 12.1943ms
2025-06-23 11:13:11.598 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:13:11.599 +08:00 [INF] 请求完成: GET /health - 状态码: 200 - 耗时: 19ms - 响应大小: 177bytes
2025-06-23 11:13:11.600 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/health - 200 null application/json; charset=utf-8 25.6778ms
2025-06-23 11:13:33.067 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 11:13:33.081 +08:00 [ERR] An unhandled exception has occurred while executing the request.
Microsoft.AspNetCore.Routing.Matching.AmbiguousMatchException: The request matched multiple endpoints. Matches: 

WcsNet.Controllers.SimpleUserController.Login (WcsNet)
WcsNet.Controllers.UserController.Login (WcsNet)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ReportAmbiguity(Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.ProcessFinalCandidates(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DefaultEndpointSelector.Select(HttpContext httpContext, Span`1 candidateState)
   at Microsoft.AspNetCore.Routing.Matching.DfaMatcher.MatchAsync(HttpContext httpContext)
   at Microsoft.AspNetCore.Routing.EndpointRoutingMiddleware.Invoke(HttpContext httpContext)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-23 11:13:33.127 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 500 null text/plain; charset=utf-8 59.6916ms
2025-06-23 11:16:08.199 +08:00 [INF] 使用内存存储服务
2025-06-23 11:16:09.459 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 11:16:09.525 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 11:16:09.565 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 11:16:09.567 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 11:16:09.568 +08:00 [INF] Hosting environment: Development
2025-06-23 11:16:09.569 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 11:16:40.384 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 11:16:40.439 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 11:16:40.453 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 11:16:40.457 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 11:16:40.493 +08:00 [INF] Route matched with {action = "GetApiInfo", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetApiInfo() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 11:16:40.514 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 11:16:40.605 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet) in 102.8817ms
2025-06-23 11:16:40.607 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 11:16:40.610 +08:00 [INF] 请求完成: GET / - 状态码: 200 - 耗时: 173ms - 响应大小: 386bytes
2025-06-23 11:16:40.621 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 200 null application/json; charset=utf-8 239.2128ms
2025-06-23 11:19:12.329 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/health - null null
2025-06-23 11:19:12.335 +08:00 [INF] CORS policy execution successful.
2025-06-23 11:19:12.336 +08:00 [INF] 请求开始: GET /health - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 11:19:12.338 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:19:12.340 +08:00 [INF] Route matched with {action = "Health", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]] Health() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 11:19:12.342 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 11:19:12.358 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.Health (WcsNet) in 16.6401ms
2025-06-23 11:19:12.359 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:19:12.360 +08:00 [INF] 请求完成: GET /health - 状态码: 200 - 耗时: 23ms - 响应大小: 177bytes
2025-06-23 11:19:12.363 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/health - 200 null application/json; charset=utf-8 33.8597ms
2025-06-23 11:19:14.645 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/health - null null
2025-06-23 11:19:14.647 +08:00 [INF] CORS policy execution successful.
2025-06-23 11:19:14.648 +08:00 [INF] 请求开始: GET /health - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 11:19:14.649 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:19:14.649 +08:00 [INF] Route matched with {action = "Health", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]] Health() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 11:19:14.650 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 11:19:14.651 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.Health (WcsNet) in 0.7662ms
2025-06-23 11:19:14.651 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 11:19:14.652 +08:00 [INF] 请求完成: GET /health - 状态码: 200 - 耗时: 4ms - 响应大小: 177bytes
2025-06-23 11:19:14.653 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/health - 200 null application/json; charset=utf-8 8.5247ms
2025-06-23 11:19:15.933 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 11:19:15.935 +08:00 [INF] CORS policy execution successful.
2025-06-23 11:19:15.936 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 11:19:15.937 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 11:19:15.938 +08:00 [INF] Route matched with {action = "GetApiInfo", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetApiInfo() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 11:19:15.939 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 11:19:15.940 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet) in 0.853ms
2025-06-23 11:19:15.940 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 11:19:15.941 +08:00 [INF] 请求完成: GET / - 状态码: 200 - 耗时: 5ms - 响应大小: 386bytes
2025-06-23 11:19:15.942 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 200 null application/json; charset=utf-8 9.0551ms
2025-06-23 11:19:17.883 +08:00 [INF] Request starting HTTP/1.1 OPTIONS http://localhost:8666/api/user/login - null null
2025-06-23 11:19:17.887 +08:00 [INF] CORS policy execution successful.
2025-06-23 11:19:17.897 +08:00 [INF] Request finished HTTP/1.1 OPTIONS http://localhost:8666/api/user/login - 204 null null 14.5728ms
2025-06-23 11:19:17.901 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 11:19:17.908 +08:00 [INF] CORS policy execution successful.
2025-06-23 11:19:17.909 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 11:19:17.941 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 11:19:17.968 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 11:19:18.986 +08:00 [ERR] Failed executing DbCommand (47ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Account`, `w`.`CreatedAt`, `w`.`CreatedId`, `w`.`DeptId`, `w`.`Email`, `w`.`Mobile`, `w`.`Password`, `w`.`Photo`, `w`.`Realname`, `w`.`Remark`, `w`.`RoleId`, `w`.`StaffCode`, `w`.`Status`, `w`.`UpdatedAt`, `w`.`UpdatedId`
FROM `wcs_user` AS `w`
WHERE ((`w`.`Account` = @__account_0) AND (`w`.`Password` = @__hashedPassword_1)) AND (`w`.`Status` = 1)
LIMIT 1
2025-06-23 11:19:19.024 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 11:19:19.041 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 11:19:19.054 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 1083.8588ms
2025-06-23 11:19:19.056 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 11:19:19.058 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 1148ms - 响应大小: 71bytes
2025-06-23 11:19:19.074 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 1173.293ms
2025-06-23 11:19:40.609 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 11:19:40.611 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 11:19:40.613 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 11:19:40.614 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 11:19:40.837 +08:00 [ERR] Failed executing DbCommand (34ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Account`, `w`.`CreatedAt`, `w`.`CreatedId`, `w`.`DeptId`, `w`.`Email`, `w`.`Mobile`, `w`.`Password`, `w`.`Photo`, `w`.`Realname`, `w`.`Remark`, `w`.`RoleId`, `w`.`StaffCode`, `w`.`Status`, `w`.`UpdatedAt`, `w`.`UpdatedId`
FROM `wcs_user` AS `w`
WHERE ((`w`.`Account` = @__account_0) AND (`w`.`Password` = @__hashedPassword_1)) AND (`w`.`Status` = 1)
LIMIT 1
2025-06-23 11:19:40.839 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 11:19:40.842 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 11:19:40.843 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 228.0961ms
2025-06-23 11:19:40.844 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 11:19:40.844 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 232ms - 响应大小: 71bytes
2025-06-23 11:19:40.846 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 236.3289ms
2025-06-23 11:23:47.764 +08:00 [INF] 使用内存存储服务
2025-06-23 11:23:48.761 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 11:23:48.877 +08:00 [ERR] Failed executing DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_user` AS `w`)
2025-06-23 11:23:48.894 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-23 11:23:48.899 +08:00 [ERR] 数据库初始化失败
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method9(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at Program.<Main>$(String[] args) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 81
2025-06-23 11:23:48.988 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 11:23:49.027 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 11:23:49.030 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 11:23:49.030 +08:00 [INF] Hosting environment: Development
2025-06-23 11:23:49.031 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 13:47:54.340 +08:00 [INF] 使用内存存储服务
2025-06-23 13:47:55.340 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 13:47:55.498 +08:00 [ERR] Failed executing DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_user` AS `w`)
2025-06-23 13:47:55.522 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-23 13:47:55.526 +08:00 [ERR] 数据库初始化失败
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method9(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at Program.<Main>$(String[] args) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 81
2025-06-23 13:47:55.611 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 13:47:55.656 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 13:47:55.658 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 13:47:55.659 +08:00 [INF] Hosting environment: Development
2025-06-23 13:47:55.659 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 13:50:01.562 +08:00 [INF] 使用内存存储服务
2025-06-23 13:50:02.770 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 13:50:02.947 +08:00 [ERR] Failed executing DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_user` AS `w`)
2025-06-23 13:50:02.963 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
2025-06-23 13:50:02.967 +08:00 [ERR] 数据库初始化失败
MySqlConnector.MySqlException (0x80004005): Table 'wcs.wcs_user' doesn't exist
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReader(CommandBehavior behavior) in /_/src/MySqlConnector/MySqlCommand.cs:line 290
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
   at System.Linq.Enumerable.TryGetSingle[TSource](IEnumerable`1 source, Boolean& found)
   at lambda_method9(Closure, QueryContext)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.Execute[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.Execute[TResult](Expression expression)
   at Program.<Main>$(String[] args) in E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet\Program.cs:line 81
2025-06-23 13:50:03.027 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 13:50:03.061 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 13:50:03.063 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 13:50:03.064 +08:00 [INF] Hosting environment: Development
2025-06-23 13:50:03.064 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 13:57:19.557 +08:00 [INF] 使用内存存储服务
2025-06-23 13:57:20.850 +08:00 [INF] Executed DbCommand (98ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 13:57:20.859 +08:00 [INF] 数据库和表创建成功
2025-06-23 13:57:21.007 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 13:57:21.014 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 13:57:21.069 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 13:57:21.102 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 13:57:21.105 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 13:57:21.105 +08:00 [INF] Hosting environment: Development
2025-06-23 13:57:21.106 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 13:57:57.941 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 13:57:57.997 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 13:57:58.004 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 13:57:58.007 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 13:57:58.023 +08:00 [INF] Route matched with {action = "GetApiInfo", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetApiInfo() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 13:57:58.040 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 13:57:58.108 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet) in 80.8322ms
2025-06-23 13:57:58.111 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 13:57:58.113 +08:00 [INF] 请求完成: GET / - 状态码: 200 - 耗时: 118ms - 响应大小: 386bytes
2025-06-23 13:57:58.122 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 200 null application/json; charset=utf-8 182.7205ms
2025-06-23 13:57:58.167 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/favicon.ico - null null
2025-06-23 13:57:58.174 +08:00 [INF] 请求开始: GET /favicon.ico - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 13:57:58.176 +08:00 [INF] 请求完成: GET /favicon.ico - 状态码: 404 - 耗时: 2ms - 响应大小: 0bytes
2025-06-23 13:57:58.178 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/favicon.ico - 404 0 null 11.4142ms
2025-06-23 13:57:58.181 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/favicon.ico, Response status code: 404
2025-06-23 13:59:54.479 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 13:59:54.484 +08:00 [INF] CORS policy execution successful.
2025-06-23 13:59:54.486 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 13:59:54.504 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 13:59:54.513 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 13:59:55.019 +08:00 [ERR] Failed executing DbCommand (73ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`CreatedAt`, `s`.`CreatedId`, `s`.`DeptId`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`RoleId`, `s`.`StaffCode`, `s`.`Status`, `s`.`UpdatedAt`, `s`.`UpdatedId`, `s`.`UserSource`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 13:59:55.036 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.CreatedAt' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.CreatedAt' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 13:59:55.050 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 13:59:55.061 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 546.6384ms
2025-06-23 13:59:55.062 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 13:59:55.063 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 577ms - 响应大小: 81bytes
2025-06-23 13:59:55.067 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 588.4724ms
2025-06-23 14:02:55.946 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/health - null null
2025-06-23 14:02:55.947 +08:00 [INF] 请求开始: GET /health - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 14:02:55.948 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 14:02:55.950 +08:00 [INF] Route matched with {action = "Health", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]] Health() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 14:02:55.951 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 14:02:55.956 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.Health (WcsNet) in 5.7289ms
2025-06-23 14:02:55.957 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 14:02:55.957 +08:00 [INF] 请求完成: GET /health - 状态码: 200 - 耗时: 9ms - 响应大小: 177bytes
2025-06-23 14:02:55.958 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/health - 200 null application/json; charset=utf-8 12.6152ms
2025-06-23 14:03:25.239 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 14:03:25.241 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 14:03:25.242 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:03:25.243 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:03:25.653 +08:00 [ERR] Failed executing DbCommand (9ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`CreatedAt`, `s`.`CreatedId`, `s`.`DeptId`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`RoleId`, `s`.`StaffCode`, `s`.`Status`, `s`.`UpdatedAt`, `s`.`UpdatedId`, `s`.`UserSource`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:03:25.658 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.CreatedAt' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.CreatedAt' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 14:03:25.663 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:03:25.664 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 419.5045ms
2025-06-23 14:03:25.664 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:03:25.665 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 423ms - 响应大小: 81bytes
2025-06-23 14:03:25.666 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 427.0588ms
2025-06-23 14:09:22.771 +08:00 [INF] 使用内存存储服务
2025-06-23 14:09:24.018 +08:00 [INF] Executed DbCommand (43ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 14:09:24.025 +08:00 [INF] 数据库和表创建成功
2025-06-23 14:09:24.169 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 14:09:24.174 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 14:09:24.235 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 14:09:24.269 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 14:09:24.271 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:09:24.272 +08:00 [INF] Hosting environment: Development
2025-06-23 14:09:24.272 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 14:11:20.375 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 14:11:20.401 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 14:11:20.411 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 14:11:20.413 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:11:20.427 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:11:20.665 +08:00 [ERR] Failed executing DbCommand (28ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`DeptId`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`RoleId`, `s`.`StaffCode`, `s`.`Status`, `s`.`UserSource`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:11:20.688 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.DeptId' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.DeptId' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 14:11:20.696 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:11:20.711 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 280.8358ms
2025-06-23 14:11:20.712 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:11:20.713 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 313ms - 响应大小: 78bytes
2025-06-23 14:11:20.719 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 344.1771ms
2025-06-23 14:12:28.610 +08:00 [INF] 使用内存存储服务
2025-06-23 14:12:29.808 +08:00 [INF] Executed DbCommand (87ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 14:12:29.816 +08:00 [INF] 数据库和表创建成功
2025-06-23 14:12:30.019 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 14:12:30.024 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 14:12:30.084 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 14:12:30.119 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 14:12:30.121 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:12:30.122 +08:00 [INF] Hosting environment: Development
2025-06-23 14:12:30.122 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 14:12:37.050 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:12:37.073 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:12:37.076 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:12:37.087 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 14:12:37.090 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:12:37.109 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:12:37.487 +08:00 [ERR] Failed executing DbCommand (75ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:12:37.503 +08:00 [ERR] An exception occurred while iterating over the results of a query for context type 'WcsNet.Models.WcsDbContext'.
MySqlConnector.MySqlException (0x80004005): Unknown column 's.source' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
MySqlConnector.MySqlException (0x80004005): Unknown column 's.source' in 'field list'
   at MySqlConnector.Core.ServerSession.ReceiveReplyAsync(IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/ServerSession.cs:line 894
   at MySqlConnector.Core.ResultSet.ReadResultSetHeaderAsync(IOBehavior ioBehavior) in /_/src/MySqlConnector/Core/ResultSet.cs:line 37
   at MySqlConnector.MySqlDataReader.ActivateResultSet(CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 130
   at MySqlConnector.MySqlDataReader.InitAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, IDictionary`2 cachedProcedures, IMySqlCommand command, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlDataReader.cs:line 483
   at MySqlConnector.Core.CommandExecutor.ExecuteReaderAsync(CommandListPosition commandListPosition, ICommandPayloadCreator payloadCreator, CommandBehavior behavior, Activity activity, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/Core/CommandExecutor.cs:line 56
   at MySqlConnector.MySqlCommand.ExecuteReaderAsync(CommandBehavior behavior, IOBehavior ioBehavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 357
   at MySqlConnector.MySqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken) in /_/src/MySqlConnector/MySqlCommand.cs:line 350
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-06-23 14:12:37.512 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:12:37.528 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 415.4116ms
2025-06-23 14:12:37.529 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:12:37.530 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 455ms - 响应大小: 78bytes
2025-06-23 14:12:37.538 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 488.6582ms
2025-06-23 14:14:44.796 +08:00 [INF] 使用内存存储服务
2025-06-23 14:14:45.913 +08:00 [INF] Executed DbCommand (91ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 14:14:45.920 +08:00 [INF] 数据库和表创建成功
2025-06-23 14:14:46.087 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 14:14:46.092 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 14:14:46.146 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 14:14:46.178 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 14:14:46.181 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:14:46.181 +08:00 [INF] Hosting environment: Development
2025-06-23 14:14:46.182 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 14:14:49.184 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:14:49.207 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:14:49.210 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:14:49.229 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 14:14:49.235 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:14:49.254 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:14:49.638 +08:00 [INF] Executed DbCommand (57ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:14:49.645 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:14:49.660 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 402.8585ms
2025-06-23 14:14:49.661 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:14:49.663 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 453ms - 响应大小: 58bytes
2025-06-23 14:14:49.670 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 487.5946ms
2025-06-23 14:15:05.271 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:15:05.276 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:15:05.278 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:15:05.284 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:05.285 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:15:05.744 +08:00 [INF] Executed DbCommand (39ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:15:05.747 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:15:05.749 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 462.9913ms
2025-06-23 14:15:05.750 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:05.751 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 473ms - 响应大小: 58bytes
2025-06-23 14:15:05.753 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 481.7724ms
2025-06-23 14:15:25.133 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 14:15:25.136 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 14:15:25.137 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:25.138 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:15:25.426 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:15:25.427 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:15:25.428 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 288.941ms
2025-06-23 14:15:25.428 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:25.429 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 293ms - 响应大小: 58bytes
2025-06-23 14:15:25.430 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 297.1069ms
2025-06-23 14:15:36.227 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:15:36.231 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:15:36.232 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:15:36.234 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:36.235 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:15:36.573 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:15:36.575 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:15:36.577 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 268.8779ms
2025-06-23 14:15:36.578 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:36.580 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 347ms - 响应大小: 58bytes
2025-06-23 14:15:36.585 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 358.1656ms
2025-06-23 14:15:49.917 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:15:49.919 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:15:49.920 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:15:49.921 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:49.922 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:15:50.110 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`dept_id`, `s`.`Email`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:15:50.111 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:15:50.112 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 188.8644ms
2025-06-23 14:15:50.113 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:15:50.113 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 193ms - 响应大小: 58bytes
2025-06-23 14:15:50.114 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 197.9405ms
2025-06-23 14:51:58.835 +08:00 [INF] 使用内存存储服务
2025-06-23 14:52:00.338 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 14:52:00.345 +08:00 [INF] 数据库和表创建成功
2025-06-23 14:52:00.521 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 14:52:00.526 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 14:52:00.581 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 14:52:00.616 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 14:52:00.618 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:52:00.619 +08:00 [INF] Hosting environment: Development
2025-06-23 14:52:00.619 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 14:52:31.137 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:52:31.185 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:52:31.191 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:52:31.204 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 14:52:31.209 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:52:31.269 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:52:31.722 +08:00 [INF] Executed DbCommand (86ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:52:31.735 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:52:31.763 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 456.5047ms
2025-06-23 14:52:31.767 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:52:31.769 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 580ms - 响应大小: 58bytes
2025-06-23 14:52:31.783 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 647.1638ms
2025-06-23 14:52:54.681 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:52:54.685 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:52:54.685 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:52:54.687 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:52:54.687 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:52:55.073 +08:00 [INF] Executed DbCommand (23ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:52:55.076 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:52:55.078 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 388.8858ms
2025-06-23 14:52:55.079 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:52:55.080 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 394ms - 响应大小: 58bytes
2025-06-23 14:52:55.082 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 401.0879ms
2025-06-23 14:55:03.463 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 14:55:03.469 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 14:55:03.474 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:55:03.475 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 14:55:03.672 +08:00 [INF] Executed DbCommand (53ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 14:55:03.675 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 14:55:03.676 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 199.1367ms
2025-06-23 14:55:03.677 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 14:55:03.678 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 209ms - 响应大小: 58bytes
2025-06-23 14:55:03.680 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 216.5937ms
2025-06-23 14:58:25.145 +08:00 [INF] 使用内存存储服务
2025-06-23 14:58:26.625 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 14:58:26.636 +08:00 [INF] 数据库和表创建成功
2025-06-23 14:58:26.859 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 14:58:26.865 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 14:58:26.992 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 14:58:27.119 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 14:58:27.198 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 14:58:27.205 +08:00 [INF] Hosting environment: Development
2025-06-23 14:58:27.206 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 14:58:35.010 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:58:35.061 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:58:35.065 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:58:35.082 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 307 - 耗时: 18ms - 响应大小: 0bytes
2025-06-23 14:58:35.087 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 307 0 null 84.6725ms
2025-06-23 14:59:02.469 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 14:59:02.474 +08:00 [INF] CORS policy execution successful.
2025-06-23 14:59:02.474 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:59:02.477 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 307 - 耗时: 2ms - 响应大小: 0bytes
2025-06-23 14:59:02.478 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 307 0 null 9.3475ms
2025-06-23 14:59:37.528 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 14:59:37.665 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 14:59:37.666 +08:00 [INF] 请求完成: GET / - 状态码: 307 - 耗时: 1ms - 响应大小: 0bytes
2025-06-23 14:59:37.668 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 307 0 null 140.0047ms
2025-06-23 15:00:08.591 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 15:00:08.604 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:00:08.608 +08:00 [INF] 请求完成: GET / - 状态码: 307 - 耗时: 4ms - 响应大小: 0bytes
2025-06-23 15:00:08.611 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 307 0 null 20.1164ms
2025-06-23 15:00:22.810 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 15:00:22.816 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:00:22.818 +08:00 [INF] 请求完成: GET / - 状态码: 307 - 耗时: 1ms - 响应大小: 0bytes
2025-06-23 15:00:22.820 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 307 0 null 10.0855ms
2025-06-23 15:01:13.273 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 15:01:13.284 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:01:13.293 +08:00 [INF] 请求完成: GET / - 状态码: 307 - 耗时: 9ms - 响应大小: 0bytes
2025-06-23 15:01:13.295 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 307 0 null 22.3627ms
2025-06-23 15:01:36.123 +08:00 [INF] 使用内存存储服务
2025-06-23 15:01:37.638 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:01:37.647 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:01:37.860 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:01:37.867 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:01:37.959 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:01:38.053 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:01:38.122 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:01:38.127 +08:00 [INF] Hosting environment: Development
2025-06-23 15:01:38.128 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:01:58.562 +08:00 [INF] 使用内存存储服务
2025-06-23 15:02:00.238 +08:00 [INF] Executed DbCommand (62ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:02:00.246 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:02:00.437 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:02:00.459 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:02:00.563 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:02:00.667 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:02:00.732 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:02:00.737 +08:00 [INF] Hosting environment: Development
2025-06-23 15:02:00.737 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:02:25.429 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/user/login - null null
2025-06-23 15:02:25.675 +08:00 [INF] 请求开始: GET /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:02:25.683 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:02:25.687 +08:00 [INF] Executing endpoint '405 HTTP Method Not Supported'
2025-06-23 15:02:25.690 +08:00 [INF] Executed endpoint '405 HTTP Method Not Supported'
2025-06-23 15:02:25.692 +08:00 [INF] 请求完成: GET /api/user/login - 状态码: 405 - 耗时: 18ms - 响应大小: 0bytes
2025-06-23 15:02:25.716 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/user/login - 405 0 null 296.2269ms
2025-06-23 15:02:30.747 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/ - null null
2025-06-23 15:02:30.759 +08:00 [INF] 请求开始: GET / - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:02:30.761 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 15:02:30.780 +08:00 [INF] Route matched with {action = "GetApiInfo", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[System.Object] GetApiInfo() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 15:02:30.791 +08:00 [INF] Executing OkObjectResult, writing value of type '<>f__AnonymousType2`6[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String[], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 15:02:30.804 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet) in 18.9256ms
2025-06-23 15:02:30.805 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.GetApiInfo (WcsNet)'
2025-06-23 15:02:30.807 +08:00 [INF] 请求完成: GET / - 状态码: 200 - 耗时: 47ms - 响应大小: 386bytes
2025-06-23 15:02:30.812 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/ - 200 null application/json; charset=utf-8 65.2089ms
2025-06-23 15:02:34.793 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 15:02:34.799 +08:00 [INF] CORS policy execution successful.
2025-06-23 15:02:34.801 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:02:34.810 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:02:34.823 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:02:37.352 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: sIKMDRVwxaRFGIvdO76XNAaIQGmvJGyfxfd5YcUSMNg=
2025-06-23 15:02:37.560 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:02:37.621 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:02:37.623 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:02:51.042 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: sIKMDRVwxaRFGIvdO76XNAaIQGmvJGyfxfd5YcUSMNg=
2025-06-23 15:02:51.174 +08:00 [INF] Executed DbCommand (93ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:02:51.176 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:02:51.176 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:02:51.178 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:02:51.185 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 16358.8745ms
2025-06-23 15:02:51.186 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:02:51.187 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 16386ms - 响应大小: 58bytes
2025-06-23 15:02:51.192 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 16399.0865ms
2025-06-23 15:02:53.645 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 15:02:53.647 +08:00 [INF] CORS policy execution successful.
2025-06-23 15:02:53.648 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:02:53.650 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:02:53.650 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:03:17.347 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: b3Xdyb+3EYjDEVIf05qNLSceMKS/rnYoFGdqmUZ5uwQ=
2025-06-23 15:03:17.429 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:03:17.433 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:03:17.434 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:03:26.919 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: b3Xdyb+3EYjDEVIf05qNLSceMKS/rnYoFGdqmUZ5uwQ=
2025-06-23 15:03:29.008 +08:00 [INF] Executed DbCommand (179ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:03:35.088 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:03:47.361 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:03:47.366 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:03:47.367 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 53715.3019ms
2025-06-23 15:03:47.368 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:03:47.369 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 53720ms - 响应大小: 58bytes
2025-06-23 15:03:47.371 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 53726.0706ms
2025-06-23 15:03:51.537 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 15:03:51.540 +08:00 [INF] CORS policy execution successful.
2025-06-23 15:03:51.540 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:03:51.542 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:03:51.543 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:03:54.799 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: sIKMDRVwxaRFGIvdO76XNAaIQGmvJGyfxfd5YcUSMNg=
2025-06-23 15:03:54.873 +08:00 [INF] Executed DbCommand (45ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:03:54.875 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:03:54.876 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:03:54.876 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:03:54.877 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 3332.9829ms
2025-06-23 15:03:54.878 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:03:54.878 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 3337ms - 响应大小: 58bytes
2025-06-23 15:03:54.879 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 3342.2946ms
2025-06-23 15:04:36.186 +08:00 [INF] 使用内存存储服务
2025-06-23 15:04:37.527 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:04:37.534 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:04:37.677 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:04:37.682 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:04:37.742 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:04:37.777 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:04:37.779 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:04:37.780 +08:00 [INF] Hosting environment: Development
2025-06-23 15:04:37.780 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:05:13.685 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:05:13.712 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:05:13.723 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:05:13.725 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:05:13.738 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:05:14.061 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: b3Xdyb+3EYjDEVIf05qNLSceMKS/rnYoFGdqmUZ5uwQ=
2025-06-23 15:05:14.260 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:05:14.299 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:05:14.299 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:05:14.304 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:05:14.320 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 578.9398ms
2025-06-23 15:05:14.322 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:05:14.323 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 612ms - 响应大小: 58bytes
2025-06-23 15:05:14.329 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 645.2114ms
2025-06-23 15:05:40.699 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 15:05:40.703 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:05:40.705 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:05:40.706 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:05:40.994 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: hGyAngscacZL4RjGlNfbesFFQAgjYSV+cYz/hLiFhFM=
2025-06-23 15:05:41.050 +08:00 [INF] Executed DbCommand (33ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:05:41.052 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:05:41.053 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:05:41.053 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:05:41.054 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 347.9015ms
2025-06-23 15:05:41.055 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:05:41.055 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 351ms - 响应大小: 58bytes
2025-06-23 15:05:41.057 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 357.5701ms
2025-06-23 15:08:17.678 +08:00 [INF] 使用内存存储服务
2025-06-23 15:08:18.931 +08:00 [INF] Executed DbCommand (166ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:08:18.939 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:08:19.125 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:08:19.132 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:08:19.193 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:08:19.228 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:08:19.230 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:08:19.231 +08:00 [INF] Hosting environment: Development
2025-06-23 15:08:19.231 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:09:16.867 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:09:16.892 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:09:16.902 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:09:16.904 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:16.918 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:09:17.138 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: bcd7571c793c1524dae60738c41305f32f483328349a1af9b86bc66d6aa25bc2
2025-06-23 15:09:17.284 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:09:17.324 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:09:17.326 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:09:17.330 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:09:17.345 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 424.6797ms
2025-06-23 15:09:17.347 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:17.348 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 456ms - 响应大小: 58bytes
2025-06-23 15:09:17.353 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 487.4571ms
2025-06-23 15:09:36.914 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 48
2025-06-23 15:09:36.918 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:09:36.920 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:36.920 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:09:37.082 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: 8fffe035559c4bd3f86af9c22e8dac9137cceddba4c08585c1fa4228945ef5ec
2025-06-23 15:09:37.191 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:09:37.193 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:09:37.193 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:09:37.194 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:09:37.195 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 273.068ms
2025-06-23 15:09:37.195 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:37.196 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 277ms - 响应大小: 58bytes
2025-06-23 15:09:37.197 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 282.2274ms
2025-06-23 15:09:51.336 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 47
2025-06-23 15:09:51.339 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:09:51.341 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:51.341 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:09:51.443 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: c02c95a1fad4f7b60ae31ca8526cd86780b7602f2b0ea4cc3cc18c936a4b9ea9
2025-06-23 15:09:51.486 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:09:51.488 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:09:51.488 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:09:51.489 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:09:51.491 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 148.2004ms
2025-06-23 15:09:51.491 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:09:51.492 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 152ms - 响应大小: 58bytes
2025-06-23 15:09:51.493 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 156.8588ms
2025-06-23 15:11:22.640 +08:00 [INF] 使用内存存储服务
2025-06-23 15:11:23.954 +08:00 [INF] Executed DbCommand (74ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:11:23.961 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:11:24.228 +08:00 [INF] Executed DbCommand (89ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:11:24.239 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:11:24.294 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:11:24.330 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:11:24.333 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:11:24.335 +08:00 [INF] Hosting environment: Development
2025-06-23 15:11:24.336 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:12:18.252 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:12:18.275 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:12:18.286 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:12:18.287 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:12:18.302 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:12:18.462 +08:00 [INF] 登录尝试 - 账号: admin, 哈希密码: bcd7571c793c1524dae60738c41305f32f483328349a1af9b86bc66d6aa25bc2
2025-06-23 15:12:18.554 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:12:18.592 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:12:18.593 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:12:18.597 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:12:18.613 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 307.8687ms
2025-06-23 15:12:18.614 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:12:18.615 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 341ms - 响应大小: 58bytes
2025-06-23 15:12:18.621 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 370.4782ms
2025-06-23 15:13:15.951 +08:00 [INF] 使用内存存储服务
2025-06-23 15:13:17.187 +08:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:13:17.194 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:13:17.404 +08:00 [INF] Executed DbCommand (57ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:13:17.414 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:13:17.468 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:13:17.503 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:13:17.505 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:13:17.506 +08:00 [INF] Hosting environment: Development
2025-06-23 15:13:17.506 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:14:15.730 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:14:15.755 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:14:15.765 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:14:15.767 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:14:15.781 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:14:15.987 +08:00 [INF] 登录尝试 - 账号: admin, 使用的Salt: Быстрый@84$!94jY4, 哈希密码: bcd7571c793c1524dae60738c41305f32f483328349a1af9b86bc66d6aa25bc2
2025-06-23 15:14:16.155 +08:00 [INF] Executed DbCommand (82ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:14:16.194 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:14:16.195 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:14:16.199 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:14:16.215 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 431.3155ms
2025-06-23 15:14:16.216 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:14:16.218 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 463ms - 响应大小: 58bytes
2025-06-23 15:14:16.224 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 495.7598ms
2025-06-23 15:16:04.233 +08:00 [INF] 使用内存存储服务
2025-06-23 15:16:05.472 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:16:05.480 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:16:05.655 +08:00 [INF] Executed DbCommand (35ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:16:05.660 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:16:05.726 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:16:05.759 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:16:05.768 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:16:05.769 +08:00 [INF] Hosting environment: Development
2025-06-23 15:16:05.769 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:16:44.215 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:16:44.241 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:16:44.251 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:16:44.252 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:16:44.267 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:16:44.432 +08:00 [INF] 登录尝试 - 账号: admin, 使用的Salt: Быстрый@84$!94jY4, 哈希密码: bcd7571c793c1524dae60738c41305f32f483328349a1af9b86bc66d6aa25bc2
2025-06-23 15:16:44.433 +08:00 [INF] 测试哈希 - a123456: bcd7571c793c1524dae60738c41305f32f483328349a1af9b86bc66d6aa25bc2, 123456: 8fffe035559c4bd3f86af9c22e8dac9137cceddba4c08585c1fa4228945ef5ec, admin: c02c95a1fad4f7b60ae31ca8526cd86780b7602f2b0ea4cc3cc18c936a4b9ea9
2025-06-23 15:16:44.621 +08:00 [INF] Executed DbCommand (81ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:16:44.661 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:16:44.662 +08:00 [WRN] 密码验证失败: admin
2025-06-23 15:16:44.666 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:16:44.682 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 412.5331ms
2025-06-23 15:16:44.684 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:16:44.685 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 445ms - 响应大小: 58bytes
2025-06-23 15:16:44.690 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 476.664ms
2025-06-23 15:18:01.751 +08:00 [INF] 使用内存存储服务
2025-06-23 15:18:02.828 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:18:02.835 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:18:02.960 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:18:02.965 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:18:03.020 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:18:03.054 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:18:03.056 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:18:03.057 +08:00 [INF] Hosting environment: Development
2025-06-23 15:18:03.057 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:18:53.276 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:18:53.301 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:18:53.311 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:18:53.313 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:18:53.326 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:18:53.558 +08:00 [INF] 登录尝试 - 账号: admin, 使用的Salt: Быстрый@84$!94jY4, 哈希密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:18:53.559 +08:00 [INF] 测试哈希 - a123456: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf, 123456: 19004ca49268714795411fba4853dedfa982099f05828661a4083e41d29499f3, admin: fbdf657d347e67b6fc1a30d37417a8c574ab552d1d986850401e7dba106a58de
2025-06-23 15:18:53.679 +08:00 [INF] Executed DbCommand (62ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:18:53.718 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:18:53.719 +08:00 [INF] 密码验证成功: admin
2025-06-23 15:18:53.735 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:18:53.743 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 414.7127ms
2025-06-23 15:18:53.745 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:18:53.746 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 447ms - 响应大小: 341bytes
2025-06-23 15:18:53.752 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 477.5944ms
2025-06-23 15:19:27.187 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:19:27.190 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:19:27.192 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:19:27.193 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:19:27.488 +08:00 [INF] 登录尝试 - 账号: admin, 使用的Salt: Быстрый@84$!94jY4, 哈希密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:19:27.489 +08:00 [INF] 测试哈希 - a123456: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf, 123456: 19004ca49268714795411fba4853dedfa982099f05828661a4083e41d29499f3, admin: fbdf657d347e67b6fc1a30d37417a8c574ab552d1d986850401e7dba106a58de
2025-06-23 15:19:27.519 +08:00 [INF] Executed DbCommand (22ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:19:27.520 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:19:27.521 +08:00 [INF] 密码验证成功: admin
2025-06-23 15:19:27.522 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:19:27.522 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 328.8058ms
2025-06-23 15:19:27.523 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:19:27.524 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 333ms - 响应大小: 341bytes
2025-06-23 15:19:27.525 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 338.1413ms
2025-06-23 15:19:27.528 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users - null null
2025-06-23 15:19:27.531 +08:00 [INF] 请求开始: GET /api/sys/users - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:19:27.532 +08:00 [INF] 请求完成: GET /api/sys/users - 状态码: 404 - 耗时: 1ms - 响应大小: 0bytes
2025-06-23 15:19:27.533 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users - 404 0 null 5.1513ms
2025-06-23 15:19:27.535 +08:00 [INF] Request reached the end of the middleware pipeline without being handled by application code. Request path: GET http://localhost:8666/api/sys/users, Response status code: 404
2025-06-23 15:20:07.313 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:20:07.319 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:20:07.324 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:20:07.326 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:20:07.447 +08:00 [INF] 登录尝试 - 账号: admin, 使用的Salt: Быстрый@84$!94jY4, 哈希密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:20:07.450 +08:00 [INF] 测试哈希 - a123456: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf, 123456: 19004ca49268714795411fba4853dedfa982099f05828661a4083e41d29499f3, admin: fbdf657d347e67b6fc1a30d37417a8c574ab552d1d986850401e7dba106a58de
2025-06-23 15:20:07.491 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:20:07.494 +08:00 [INF] 找到用户 - 账号: admin, 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-23 15:20:07.495 +08:00 [INF] 密码验证成功: admin
2025-06-23 15:20:07.496 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:20:07.498 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 169.8673ms
2025-06-23 15:20:07.500 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:20:07.501 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 181ms - 响应大小: 341bytes
2025-06-23 15:20:07.502 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 189.303ms
2025-06-23 15:20:07.505 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/user - null null
2025-06-23 15:20:07.513 +08:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-23 15:20:07.516 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: No authenticationScheme was specified, and there was no DefaultChallengeScheme found. The default schemes can be set using either AddAuthentication(string defaultScheme) or AddAuthentication(Action<AuthenticationOptions> configureOptions).
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-23 15:20:07.537 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/user - 500 null text/plain; charset=utf-8 31.8ms
2025-06-23 15:20:38.570 +08:00 [INF] 使用内存存储服务
2025-06-23 15:20:39.867 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:20:39.875 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:20:40.015 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:20:40.020 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:20:40.075 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:20:40.109 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:20:40.111 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:20:40.112 +08:00 [INF] Hosting environment: Development
2025-06-23 15:20:40.112 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-23 15:20:53.769 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 15:20:53.794 +08:00 [INF] CORS policy execution successful.
2025-06-23 15:20:53.797 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:20:53.808 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-23 15:20:53.809 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:20:53.825 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:20:54.123 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:20:54.129 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:20:54.147 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 319.1358ms
2025-06-23 15:20:54.149 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:20:54.150 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 354ms - 响应大小: 58bytes
2025-06-23 15:20:54.156 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 387.6734ms
2025-06-23 15:21:30.336 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 49
2025-06-23 15:21:30.340 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:21:30.341 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:21:30.342 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:21:30.548 +08:00 [INF] Executed DbCommand (51ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:21:30.591 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:21:30.593 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 249.8661ms
2025-06-23 15:21:30.593 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:21:30.594 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 253ms - 响应大小: 341bytes
2025-06-23 15:21:30.595 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 258.7186ms
2025-06-23 15:22:15.420 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-23 15:22:15.434 +08:00 [INF] CORS policy execution successful.
2025-06-23 15:22:15.435 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-23 15:22:15.437 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:22:15.438 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-23 15:22:15.588 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-23 15:22:15.592 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-23 15:22:15.594 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 153.9054ms
2025-06-23 15:22:15.595 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-23 15:22:15.596 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 160ms - 响应大小: 58bytes
2025-06-23 15:22:15.599 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 178.4754ms
2025-06-23 15:23:10.994 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/device - null null
2025-06-23 15:23:10.998 +08:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-23 15:23:11.000 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: No authenticationScheme was specified, and there was no DefaultChallengeScheme found. The default schemes can be set using either AddAuthentication(string defaultScheme) or AddAuthentication(Action<AuthenticationOptions> configureOptions).
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-23 15:23:11.010 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/device - 500 null text/plain; charset=utf-8 15.6365ms
2025-06-23 15:23:25.706 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/health - null null
2025-06-23 15:23:25.708 +08:00 [INF] 请求开始: GET /health - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-23 15:23:25.710 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 15:23:25.712 +08:00 [INF] Route matched with {action = "Health", controller = "SimpleUser"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Object]] Health() on controller WcsNet.Controllers.SimpleUserController (WcsNet).
2025-06-23 15:23:25.715 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-23 15:23:25.727 +08:00 [INF] Executed action WcsNet.Controllers.SimpleUserController.Health (WcsNet) in 12.8466ms
2025-06-23 15:23:25.729 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SimpleUserController.Health (WcsNet)'
2025-06-23 15:23:25.729 +08:00 [INF] 请求完成: GET /health - 状态码: 200 - 耗时: 21ms - 响应大小: 177bytes
2025-06-23 15:23:25.731 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/health - 200 null application/json; charset=utf-8 24.6615ms
2025-06-23 15:23:46.582 +08:00 [INF] Application is shutting down...
2025-06-23 15:23:54.588 +08:00 [INF] 使用内存存储服务
2025-06-23 15:23:56.201 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-23 15:23:56.209 +08:00 [INF] 数据库和表创建成功
2025-06-23 15:23:56.367 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-23 15:23:56.376 +08:00 [INF] 数据库已有 2 个用户
2025-06-23 15:23:56.467 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-23 15:23:56.555 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-23 15:23:56.621 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-23 15:23:56.628 +08:00 [INF] Hosting environment: Development
2025-06-23 15:23:56.629 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
