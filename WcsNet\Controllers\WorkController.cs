using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;
using WcsNet.Dispatch;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
[Route("api/works")] // 添加别名路由
public class WorkController : ControllerBase
{
    private readonly WorkService _workService;

    public WorkController(WorkService workService)
    {
        _workService = workService;
    }

    /// <summary>
    /// 获取工作任务列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>工作任务列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<object>>> GetWorks(
        string? task_type = null,
        sbyte? status = null,
        string? start_time = null,
        string? end_time = null,
        int page_size = 10,
        int page_no = 1)
    {
        try
        {
            var (items, total) = await _workService.GetPagedAsync(page_no, page_size, query =>
            {
                if (!string.IsNullOrEmpty(task_type) && sbyte.TryParse(task_type, out var taskTypeValue))
                    query = query.Where(w => w.TaskType == taskTypeValue);

                if (status.HasValue)
                    query = query.Where(w => w.Status == status.Value);

                if (!string.IsNullOrEmpty(start_time) && DateTime.TryParse(start_time, out var startDate))
                    query = query.Where(w => w.CreatedAt >= startDate);

                if (!string.IsNullOrEmpty(end_time) && DateTime.TryParse(end_time, out var endDate))
                    query = query.Where(w => w.CreatedAt <= endDate);

                return query.OrderByDescending(w => w.Id);
            });

            var response = new
            {
                list = items.Select(w => new
                {
                    w.Id,
                    TaskType = w.TaskType,
                    ItemCode = w.ItemCode ?? "",
                    Parameters = w.Parameters ?? "",
                    ErrMsg = w.ErrMsg ?? "",
                    w.Status,
                    w.Retries,
                    w.MaxRetries,
                    CreatedAt = w.CreatedAt ?? DateTime.Now,
                    UpdatedAt = w.UpdatedAt ?? DateTime.Now
                }),
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>工作任务</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<Work>>> GetWork(ulong id)
    {
        try
        {
            var work = await _workService.FindByIdAsync(id);
            if (work == null)
            {
                return Ok(ApiResponse<Work>.Error(404, "任务不存在"));
            }

            return Ok(ApiResponse<Work>.Success(work));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Work>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建工作任务
    /// </summary>
    /// <param name="work">工作任务</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<Work>>> CreateWork([FromBody] Work work)
    {
        try
        {
            // 确保ItemCode不为空
            if (string.IsNullOrEmpty(work.ItemCode))
            {
                work.ItemCode = $"WORK_{DateTime.Now:yyyyMMddHHmmss}_{Random.Shared.Next(1000, 9999)}";
            }

            var createdWork = await _workService.CreateAsync(work);
            return Ok(ApiResponse<Work>.Success(createdWork, "任务创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Work>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse>> UpdateWork(ulong id, [FromBody] Dictionary<string, object> updates)
    {
        try
        {
            var success = await _workService.UpdateAsync(id, updates);
            
            if (success)
            {
                return Ok(ApiResponse.Success("任务更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteWork(ulong id)
    {
        try
        {
            var success = await _workService.DeleteAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("任务删除成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务的执行步骤
    /// </summary>
    /// <param name="workId">任务ID</param>
    /// <returns>执行步骤列表</returns>
    [HttpGet("steps/{workId}")]
    public async Task<ActionResult<ApiResponse<List<WorkStep>>>> GetWorkSteps(ulong workId)
    {
        try
        {
            var steps = await _workService.GetWorkStepsAsync(workId);
            return Ok(ApiResponse<List<WorkStep>>.Success(steps));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<WorkStep>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新任务状态
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}/status")]
    public async Task<ActionResult<ApiResponse>> UpdateWorkStatus(ulong id, [FromBody] sbyte status)
    {
        try
        {
            var success = await _workService.UpdateStatusAsync(id, status);
            
            if (success)
            {
                return Ok(ApiResponse.Success("状态更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, int>>>> GetWorkStatistics(ulong? laneId = null)
    {
        try
        {
            var statistics = await _workService.GetWorkStatisticsAsync(laneId);
            return Ok(ApiResponse<Dictionary<string, int>>.Success(statistics));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Dictionary<string, int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 搜索任务
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Work>>>> SearchWorks(
        string keyword, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _workService.SearchWorksAsync(keyword, pageIndex, pageSize);

            var response = new PagedResponse<Work>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Work>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Work>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据任务类型获取任务
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>任务列表</returns>
    [HttpGet("type/{taskType}")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Work>>>> GetWorksByType(
        TaskType taskType, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _workService.GetWorksByTypeAsync(taskType, pageIndex, pageSize);

            var response = new PagedResponse<Work>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Work>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Work>>.Error(500, ex.Message));
        }
    }
}
