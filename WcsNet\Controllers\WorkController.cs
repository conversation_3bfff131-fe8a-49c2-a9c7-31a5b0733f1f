using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;
using WcsNet.Dispatch;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class WorkController : ControllerBase
{
    private readonly WorkService _workService;

    public WorkController(WorkService workService)
    {
        _workService = workService;
    }

    /// <summary>
    /// 获取工作任务列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>工作任务列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<Work>>>> GetWorks(
        int pageIndex = 1, 
        int pageSize = 20, 
        ulong? laneId = null,
        sbyte? status = null)
    {
        try
        {
            var (items, total) = await _workService.GetPagedAsync(pageIndex, pageSize, query =>
            {
                if (laneId.HasValue)
                    query = query.Where(w => w.LaneId == laneId.Value);

                if (status.HasValue)
                    query = query.Where(w => w.Status == status.Value);

                return query.OrderByDescending(w => w.Id);
            });

            var response = new PagedResponse<Work>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Work>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Work>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>工作任务</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<Work>>> GetWork(uint id)
    {
        try
        {
            var work = await _workService.FindByIdAsync(id);
            if (work == null)
            {
                return Ok(ApiResponse<Work>.Error(404, "任务不存在"));
            }

            return Ok(ApiResponse<Work>.Success(work));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Work>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建工作任务
    /// </summary>
    /// <param name="work">工作任务</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<Work>>> CreateWork([FromBody] Work work)
    {
        try
        {
            var createdWork = await _workService.CreateAsync(work);
            return Ok(ApiResponse<Work>.Success(createdWork, "任务创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Work>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse>> UpdateWork(uint id, [FromBody] Dictionary<string, object> updates)
    {
        try
        {
            var success = await _workService.UpdateAsync(id, updates);
            
            if (success)
            {
                return Ok(ApiResponse.Success("任务更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除工作任务
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteWork(uint id)
    {
        try
        {
            var success = await _workService.DeleteAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("任务删除成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务的执行步骤
    /// </summary>
    /// <param name="workId">任务ID</param>
    /// <returns>执行步骤列表</returns>
    [HttpGet("steps/{workId}")]
    public async Task<ActionResult<ApiResponse<List<WorkStep>>>> GetWorkSteps(uint workId)
    {
        try
        {
            var steps = await _workService.GetWorkStepsAsync(workId);
            return Ok(ApiResponse<List<WorkStep>>.Success(steps));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<WorkStep>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新任务状态
    /// </summary>
    /// <param name="id">任务ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}/status")]
    public async Task<ActionResult<ApiResponse>> UpdateWorkStatus(uint id, [FromBody] sbyte status)
    {
        try
        {
            var success = await _workService.UpdateStatusAsync(id, status);
            
            if (success)
            {
                return Ok(ApiResponse.Success("状态更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "任务不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, int>>>> GetWorkStatistics(uint? laneId = null)
    {
        try
        {
            var statistics = await _workService.GetWorkStatisticsAsync(laneId);
            return Ok(ApiResponse<Dictionary<string, int>>.Success(statistics));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Dictionary<string, int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 搜索任务
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Work>>>> SearchWorks(
        string keyword, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _workService.SearchWorksAsync(keyword, pageIndex, pageSize);

            var response = new PagedResponse<Work>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Work>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Work>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据任务类型获取任务
    /// </summary>
    /// <param name="taskType">任务类型</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>任务列表</returns>
    [HttpGet("type/{taskType}")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Work>>>> GetWorksByType(
        TaskType taskType, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _workService.GetWorksByTypeAsync(taskType, pageIndex, pageSize);

            var response = new PagedResponse<Work>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Work>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Work>>.Error(500, ex.Message));
        }
    }
}
