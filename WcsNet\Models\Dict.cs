using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

/// <summary>
/// 字典表
/// </summary>
[Table("sys_dict")]
public class Dict : BaseModel
{
    /// <summary>
    /// 字典编码
    /// </summary>
    [Required]
    [StringLength(64)]
    [Column("dict_code")]
    public string DictCode { get; set; } = string.Empty;

    /// <summary>
    /// 字典名称
    /// </summary>
    [Required]
    [StringLength(64)]
    [Column("dict_name")]
    public string DictName { get; set; } = string.Empty;

    /// <summary>
    /// 字典类型
    /// </summary>
    [StringLength(64)]
    [Column("dict_type")]
    public string DictType { get; set; } = string.Empty;

    /// <summary>
    /// 状态 1:正常 2:停用
    /// </summary>
    public sbyte Status { get; set; } = 1;

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    /// <summary>
    /// 更新人ID
    /// </summary>
    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }
}

/// <summary>
/// 字典值表
/// </summary>
[Table("sys_dict_value")]
public class DictValue : BaseModel
{
    /// <summary>
    /// 字典ID
    /// </summary>
    [Column("dict_id")]
    public ulong DictId { get; set; }

    /// <summary>
    /// 字典标签
    /// </summary>
    [Required]
    [StringLength(128)]
    [Column("dict_label")]
    public string DictLabel { get; set; } = string.Empty;

    /// <summary>
    /// 字典值
    /// </summary>
    [Required]
    [StringLength(128)]
    [Column("dict_value")]
    public string DictVal { get; set; } = string.Empty;

    /// <summary>
    /// 排序值
    /// </summary>
    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;

    /// <summary>
    /// 状态 1:正常 2:停用
    /// </summary>
    public sbyte Status { get; set; } = 1;

    /// <summary>
    /// 创建人ID
    /// </summary>
    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    /// <summary>
    /// 更新人ID
    /// </summary>
    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    /// <summary>
    /// 关联的字典
    /// </summary>
    [ForeignKey("DictId")]
    public virtual Dict? Dict { get; set; }
}
