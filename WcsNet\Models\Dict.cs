using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_dict")]
public class Dict : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("dict_code")]
    public string DictCode { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("dict_name")]
    public string DictName { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1;

    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;
}
