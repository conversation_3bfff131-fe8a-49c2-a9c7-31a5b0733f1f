/*!
  * message-compiler v12.0.0-alpha.2
  * (c) 2016-present ka<PERSON><PERSON> and contributors
  * Released under the MIT License.
  */
var IntlifyMessageCompiler=function(e){"use strict";const t=Object.assign,n=e=>"string"==typeof e;function r(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function c(e,t,n){return{line:e,column:t,offset:n}}function o(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const s={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,<PERSON>MPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},u={[s.EXPECTED_TOKEN]:"Expected token: '{0}'",[s.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[s.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[s.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[s.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[s.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[s.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[s.EMPTY_PLACEHOLDER]:"Empty placeholder",[s.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[s.INVALID_LINKED_FORMAT]:"Invalid linked format",[s.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[s.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[s.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[s.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[s.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[s.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function a(e,t,n={}){const{domain:r,messages:c,args:o}=n,s=new SyntaxError(String(e));return s.code=e,t&&(s.location=t),s.domain=r,s}function i(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?i(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const c=t.cases.length;for(let n=0;n<c&&(i(e,t.cases[n]),n!==c-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const c=t.items.length;for(let o=0;o<c&&(i(e,t.items[o]),o!==c-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),i(e,t.key),t.modifier?(e.push(", "),i(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function l(e){switch(e.t=e.type,e.type){case 0:{const t=e;l(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)l(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)l(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;l(t.key),t.k=t.key,delete t.key,t.modifier&&(l(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function E(e){const t=e.body;return 2===t.type?f(t):t.cases.forEach((e=>f(e))),e}function f(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=r(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}const L=" ",d="\r",N="\n",_=String.fromCharCode(8232),p=String.fromCharCode(8233);function C(e){const t=e;let n=0,r=1,c=1,o=0;const s=e=>t[e]===d&&t[e+1]===N,u=e=>t[e]===p,a=e=>t[e]===_,i=e=>s(e)||(e=>t[e]===N)(e)||u(e)||a(e),l=e=>s(e)||u(e)||a(e)?N:t[e];function E(){return o=0,i(n)&&(r++,c=0),s(n)&&n++,n++,c++,t[n]}return{index:()=>n,line:()=>r,column:()=>c,peekOffset:()=>o,charAt:l,currentChar:()=>l(n),currentPeek:()=>l(n+o),next:E,peek:function(){return s(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,c=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)E();o=0}}}const A=void 0,T="'";function k(e,t={}){const n=!1!==t.location,r=C(e),u=()=>r.index(),a=()=>c(r.line(),r.column(),r.index()),i=a(),l=u(),E={currentType:13,offset:l,startLoc:i,endLoc:i,lastType:13,lastOffset:l,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},f=()=>E,{onError:d}=t;function _(e,t,r){e.endLoc=a(),e.currentType=t;const c={type:t};return n&&(c.loc=o(e.startLoc,e.endLoc)),null!=r&&(c.value=r),c}const p=e=>_(e,13);function k(e,t){return e.currentChar()===t?(e.next(),t):(s.EXPECTED_TOKEN,a(),"")}function I(e){let t="";for(;e.currentPeek()===L||e.currentPeek()===N;)t+=e.currentPeek(),e.peek();return t}function h(e){const t=I(e);return e.skipToPeek(),t}function P(e){if(e===A)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function S(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=function(e){if(e===A)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function y(e){I(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function D(e,t=!0){const n=(t=!1,r="")=>{const c=e.currentPeek();return"{"===c?t:"@"!==c&&c?"|"===c?!(r===L||r===N):c===L?(e.peek(),n(!0,L)):c!==N||(e.peek(),n(!0,N)):t},r=n();return t&&e.resetPeek(),r}function O(e,t){const n=e.currentChar();return n===A?A:t(n)?(e.next(),n):null}function m(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function b(e){return O(e,m)}function U(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function x(e){return O(e,U)}function R(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function v(e){return O(e,R)}function M(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function g(e){return O(e,M)}function X(e){let t="",n="";for(;t=v(e);)n+=t;return n}function Y(e){return e!==T&&e!==N}function K(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return w(e,t,4);case"U":return w(e,t,6);default:return s.UNKNOWN_ESCAPE_SEQUENCE,a(),""}}function w(e,t,n){k(e,t);let r="";for(let c=0;c<n;c++){const t=g(e);if(!t){s.INVALID_UNICODE_ESCAPE_SEQUENCE,a(),e.currentChar();break}r+=t}return`\\${t}${r}`}function H(e){return"{"!==e&&"}"!==e&&e!==L&&e!==N}function G(e){h(e);const t=k(e,"|");return h(e),t}function $(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(s.NOT_ALLOW_NEST_PLACEHOLDER,a()),e.next(),n=_(t,2,"{"),h(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(s.EMPTY_PLACEHOLDER,a()),e.next(),n=_(t,3,"}"),t.braceNest--,t.braceNest>0&&h(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(s.UNTERMINATED_CLOSING_BRACE,a()),n=B(e,t)||p(t),t.braceNest=0,n;default:{let r=!0,c=!0,o=!0;if(y(e))return t.braceNest>0&&(s.UNTERMINATED_CLOSING_BRACE,a()),n=_(t,1,G(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(4===t.currentType||5===t.currentType||6===t.currentType))return s.UNTERMINATED_CLOSING_BRACE,a(),t.braceNest=0,V(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t))return n=_(t,4,function(e){h(e);let t="",n="";for(;t=x(e);)n+=t;return e.currentChar()===A&&(s.UNTERMINATED_CLOSING_BRACE,a()),n}(e)),h(e),n;if(c=S(e,t))return n=_(t,5,function(e){h(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${X(e)}`):t+=X(e),e.currentChar()===A&&(s.UNTERMINATED_CLOSING_BRACE,a()),t}(e)),h(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;I(e);const r=e.currentPeek()===T;return e.resetPeek(),r}(e,t))return n=_(t,6,function(e){h(e),k(e,"'");let t="",n="";for(;t=O(e,Y);)n+="\\"===t?K(e):t;const r=e.currentChar();return r===N||r===A?(s.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,a(),r===N&&(e.next(),k(e,"'")),n):(k(e,"'"),n)}(e)),h(e),n;if(!r&&!c&&!o)return n=_(t,12,function(e){h(e);let t="",n="";for(;t=O(e,H);)n+=t;return n}(e)),s.INVALID_TOKEN_IN_PLACEHOLDER,a(),n.value,h(e),n;break}}return n}function B(e,t){const{currentType:n}=t;let r=null;const c=e.currentChar();switch(7!==n&&8!==n&&11!==n&&9!==n||c!==N&&c!==L||(s.INVALID_LINKED_FORMAT,a()),c){case"@":return e.next(),r=_(t,7,"@"),t.inLinked=!0,r;case".":return h(e),e.next(),_(t,8,".");case":":return h(e),e.next(),_(t,9,":");default:return y(e)?(r=_(t,1,G(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(7!==n)return!1;I(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(7!==n&&11!==n)return!1;I(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(h(e),B(e,t)):function(e,t){const{currentType:n}=t;if(8!==n)return!1;I(e);const r=P(e.currentPeek());return e.resetPeek(),r}(e,t)?(h(e),_(t,11,function(e){let t="",n="";for(;t=b(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(9!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?P(e.peek()):!("@"===t||"|"===t||":"===t||"."===t||t===L||!t)&&(t===N?(e.peek(),r()):D(e,!1))},c=r();return e.resetPeek(),c}(e,t)?(h(e),"{"===c?$(e,t)||r:_(t,10,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===L?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(7===n&&(s.INVALID_LINKED_FORMAT,a()),t.braceNest=0,t.inLinked=!1,V(e,t))}}function V(e,t){let n={type:13};if(t.braceNest>0)return $(e,t)||p(t);if(t.inLinked)return B(e,t)||p(t);switch(e.currentChar()){case"{":return $(e,t)||p(t);case"}":return s.UNBALANCED_CLOSING_BRACE,a(),e.next(),_(t,3,"}");case"@":return B(e,t)||p(t);default:if(y(e))return n=_(t,1,G(e)),t.braceNest=0,t.inLinked=!1,n;if(D(e))return _(t,0,function(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if(n===L||n===N)if(D(e))t+=n,e.next();else{if(y(e))break;t+=n,e.next()}else t+=n,e.next()}return t}(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:c}=E;return E.lastType=e,E.lastOffset=t,E.lastStartLoc=n,E.lastEndLoc=c,E.offset=u(),E.startLoc=a(),r.currentChar()===A?_(E,13):V(r,E)},currentOffset:u,currentPosition:a,context:f}}const I="parser",h=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function P(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function S(e={}){const n=!1!==e.location,{onError:r}=e;function c(e,t,r){const c={type:e};return n&&(c.start=t,c.end=t,c.loc={start:r,end:r}),c}function o(e,t,r,c){n&&(e.end=t,e.loc&&(e.loc.end=r))}function u(e,t){const n=e.context(),r=c(3,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(5,r,s);return u.index=parseInt(t,10),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function i(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(4,r,s);return u.key=t,e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function l(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:s}=n,u=c(9,r,s);return u.value=t.replace(h,P),e.nextToken(),o(u,e.currentOffset(),e.currentPosition()),u}function E(e){const t=e.context(),n=c(6,t.offset,t.startLoc);let r=e.nextToken();if(8===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:u}=n,a=c(8,r,u);return 11!==t.type?(s.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",o(a,r,u),{nextConsumeToken:t,node:a}):(null==t.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,y(t)),a.value=t.value||"",o(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(9!==r.type&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 10:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(r)),n.key=function(e,t){const n=e.context(),r=c(7,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 4:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(r)),n.key=i(e,r.value||"");break;case 5:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(r)),n.key=a(e,r.value||"");break;case 6:null==r.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(r)),n.key=l(e,r.value||"");break;default:{s.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const u=e.context(),a=c(7,u.offset,u.startLoc);return a.value="",o(a,u.offset,u.startLoc),n.key=a,o(n,u.offset,u.startLoc),{nextConsumeToken:r,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function f(e){const t=e.context(),n=c(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null;do{const c=r||e.nextToken();switch(r=null,c.type){case 0:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(c)),n.items.push(u(e,c.value||""));break;case 5:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(c)),n.items.push(a(e,c.value||""));break;case 4:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(c)),n.items.push(i(e,c.value||""));break;case 6:null==c.value&&(s.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,y(c)),n.items.push(l(e,c.value||""));break;case 7:{const t=E(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(13!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function L(e){const t=e.context(),{offset:n,startLoc:r}=t,u=f(e);return 13===t.currentType?u:function(e,t,n,r){const u=e.context();let a=0===r.items.length;const i=c(1,t,n);i.cases=[],i.cases.push(r);do{const t=f(e);a||(a=0===t.items.length),i.cases.push(t)}while(13!==u.currentType);return a&&s.MUST_HAVE_MESSAGES_IN_PLURAL,o(i,e.currentOffset(),e.currentPosition()),i}(e,n,r,u)}return{parse:function(r){const u=k(r,t({},e)),a=u.context(),i=c(0,a.offset,a.startLoc);return n&&i.loc&&(i.loc.source=r),i.body=L(u),e.onCacheKey&&(i.cacheKey=e.onCacheKey(r)),13!==a.currentType&&(s.UNEXPECTED_LEXICAL_ANALYSIS,a.lastStartLoc,r[a.offset]),o(i,u.currentOffset(),u.currentPosition()),i}}}function y(e){if(13===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function D(e,t){for(let n=0;n<e.length;n++)O(e[n],t)}function O(e,t){switch(e.type){case 1:D(e.cases,t),t.helper("plural");break;case 2:D(e.items,t);break;case 6:O(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function m(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&O(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}const b=/<\/?[\w\s="/.':;#-\/]+>/;return e.COMPILE_ERROR_CODES_EXTEND_POINT=17,e.CompileErrorCodes=s,e.ERROR_DOMAIN=I,e.LOCATION_STUB={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}},e.baseCompile=function(e,c={}){const o=t({},c),s=!!o.jit,u=!!o.mangle,a=null==o.optimize||o.optimize,f=S(o).parse(e);return s?(a&&E(f),u&&l(f),{ast:f,code:""}):(m(f,o),((e,t={})=>{const c=n(t.mode)?t.mode:"normal",o=n(t.filename)?t.filename:"message.intl",s=!!t.sourceMap,u=null!=t.breakLineCode?t.breakLineCode:"arrow"===c?";":"\n",a=t.needIndent?t.needIndent:"arrow"!==c,l=e.helpers||[],E=function(e,t){const{sourceMap:n,filename:r,breakLineCode:c,needIndent:o}=t,s=!1!==t.location,u={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:c,needIndent:o,indentLevel:0};function a(e,t){u.code+=e}function i(e,t=!0){const n=t?c:"";a(o?n+"  ".repeat(e):n)}return s&&e.loc&&(u.source=e.loc.source),{context:()=>u,push:a,indent:function(e=!0){const t=++u.indentLevel;e&&i(t)},deindent:function(e=!0){const t=--u.indentLevel;e&&i(t)},newline:function(){i(u.indentLevel)},helper:e=>`_${e}`,needIndent:()=>u.needIndent}}(e,{mode:c,filename:o,sourceMap:s,breakLineCode:u,needIndent:a});E.push("normal"===c?"function __msg__ (ctx) {":"(ctx) => {"),E.indent(a),l.length>0&&(E.push(`const { ${r(l.map((e=>`${e}: _${e}`)),", ")} } = ctx`),E.newline()),E.push("return "),i(E,e),E.deindent(a),E.push("}"),delete e.helpers;const{code:f,map:L}=E.context();return{ast:e,code:f,map:L?L.toJSON():void 0}})(f,o))},e.createCompileError=a,e.createLocation=o,e.createParser=S,e.createPosition=c,e.defaultOnError=function(e){throw e},e.detectHtmlTag=e=>b.test(e),e.errorMessages=u,e.mangle=l,e.optimize=E,e}({});
