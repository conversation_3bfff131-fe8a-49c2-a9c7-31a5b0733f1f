{"name": "@intlify/message-compiler", "version": "12.0.0-alpha.2", "description": "@intlify/message-compiler", "keywords": ["compiler", "i18n", "internationalization", "intlify", "message-format"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n/tree/master/packages/message-compiler#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n.git", "directory": "packages/message-compiler"}, "bugs": {"url": "https://github.com/intlify/vue-i18n/issues"}, "files": ["dist"], "type": "module", "module": "dist/message-compiler.js", "unpkg": "dist/message-compiler.global.js", "jsdelivr": "dist/message-compiler.global.js", "types": "dist/message-compiler.d.ts", "dependencies": {"source-map-js": "^1.0.2", "@intlify/shared": "12.0.0-alpha.2"}, "engines": {"node": ">= 16"}, "buildOptions": {"name": "IntlifyMessageCompiler", "formats": ["mjs", "mjs-node", "browser", "global"], "enableFullBundleForEsmBrowser": true}, "exports": {".": {"types": "./dist/message-compiler.d.ts", "import": "./dist/message-compiler.js", "browser": "./dist/message-compiler.esm-browser.js", "node": {"import": {"production": "./dist/message-compiler.node.js", "development": "./dist/message-compiler.node.js", "default": "./dist/message-compiler.node.js"}}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "funding": "https://github.com/sponsors/kazupon", "publishConfig": {"access": "public"}, "sideEffects": false}