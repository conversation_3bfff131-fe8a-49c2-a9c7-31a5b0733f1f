export const browerPreprocessorOptions = {"outputDir":"","defaultScopeName":"","includeStyleWithColors":[],"extract":true,"themeLinkTagId":"theme-link-tag","themeLinkTagInjectTo":"head","removeCssScopeName":false,"customThemeCssFileName":null,"arbitraryMode":false,"defaultPrimaryColor":"","customThemeOutputPath":"E:/SourceCode/Test/TempTest/Ai/wcs/web/node_modules/.pnpm/@pureadmin+theme@3.2.0/node_modules/@pureadmin/theme/setCustomTheme.js","styleTagId":"custom-theme-tagid","InjectDefaultStyleTagToHtml":true,"hueDiffControls":{"low":0,"high":0},"multipleScopeVars":[{"scopeName":"layout-theme-light","varsContent":"\n        $subMenuActiveText: #000000d9 !default;\n        $menuBg: #fff !default;\n        $menuHover: #f6f6f6 !default;\n        $subMenuBg: #fff !default;\n        $subMenuActiveBg: #e0ebf6 !default;\n        $menuText: rgb(0 0 0 / 60%) !default;\n        $sidebarLogo: #fff !default;\n        $menuTitleHover: #000 !default;\n        $menuActiveBefore: #4091f7 !default;\n      "},{"scopeName":"layout-theme-default","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #001529 !default;\n        $menuHover: rgb(64 145 247 / 15%) !default;\n        $subMenuBg: #0f0303 !default;\n        $subMenuActiveBg: #4091f7 !default;\n        $menuText: rgb(254 254 254 / 65%) !default;\n        $sidebarLogo: #002140 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #4091f7 !default;\n      "},{"scopeName":"layout-theme-saucePurple","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #130824 !default;\n        $menuHover: rgb(105 58 201 / 15%) !default;\n        $subMenuBg: #000 !default;\n        $subMenuActiveBg: #693ac9 !default;\n        $menuText: #7a80b4 !default;\n        $sidebarLogo: #1f0c38 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #693ac9 !default;\n      "},{"scopeName":"layout-theme-pink","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #28081a !default;\n        $menuHover: rgb(216 68 147 / 15%) !default;\n        $subMenuBg: #000 !default;\n        $subMenuActiveBg: #d84493 !default;\n        $menuText: #7a80b4 !default;\n        $sidebarLogo: #3f0d29 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #d84493 !default;\n      "},{"scopeName":"layout-theme-dusk","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #2a0608 !default;\n        $menuHover: rgb(225 60 57 / 15%) !default;\n        $subMenuBg: #000 !default;\n        $subMenuActiveBg: #e13c39 !default;\n        $menuText: rgb(254 254 254 / 65.1%) !default;\n        $sidebarLogo: #42090c !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #e13c39 !default;\n      "},{"scopeName":"layout-theme-volcano","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #2b0e05 !default;\n        $menuHover: rgb(232 95 51 / 15%) !default;\n        $subMenuBg: #0f0603 !default;\n        $subMenuActiveBg: #e85f33 !default;\n        $menuText: rgb(254 254 254 / 65%) !default;\n        $sidebarLogo: #441708 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #e85f33 !default;\n      "},{"scopeName":"layout-theme-mingQing","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #032121 !default;\n        $menuHover: rgb(89 191 193 / 15%) !default;\n        $subMenuBg: #000 !default;\n        $subMenuActiveBg: #59bfc1 !default;\n        $menuText: #7a80b4 !default;\n        $sidebarLogo: #053434 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #59bfc1 !default;\n      "},{"scopeName":"layout-theme-auroraGreen","varsContent":"\n        $subMenuActiveText: #fff !default;\n        $menuBg: #0b1e15 !default;\n        $menuHover: rgb(96 172 128 / 15%) !default;\n        $subMenuBg: #000 !default;\n        $subMenuActiveBg: #60ac80 !default;\n        $menuText: #7a80b4 !default;\n        $sidebarLogo: #112f21 !default;\n        $menuTitleHover: #fff !default;\n        $menuActiveBefore: #60ac80 !default;\n      "}]};
export const basePath="/";
export const assetsDir="assets";
export const buildCommand="serve";
        