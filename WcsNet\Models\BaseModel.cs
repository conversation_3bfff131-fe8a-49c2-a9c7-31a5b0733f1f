using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

public abstract class BaseModel
{
    [Key]
    public ulong Id { get; set; }

    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
