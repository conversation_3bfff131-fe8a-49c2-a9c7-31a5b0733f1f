﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_operation")]
public class OperationLog : BaseModel
{
    [Required]
    [StringLength(32)]
    public string Oper { get; set; } = string.Empty;  // 操作人

    [StringLength(64)]
    public string Module { get; set; } = string.Empty;  // 模块

    [StringLength(128)]
    public string Uri { get; set; } = string.Empty;  // 请求地址

    [StringLength(15)]
    public string Ip { get; set; } = string.Empty;  // IP地址

    [StringLength(24)]
    public string Os { get; set; } = string.Empty;  // 操作系统

    [StringLength(32)]
    public string Browser { get; set; } = string.Empty;  // 浏览器

    public sbyte Status { get; set; } = 1;  // 状态 1成功 2失败
}
