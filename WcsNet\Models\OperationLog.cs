using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_operation")]
public class OperationLog : BaseModel
{
    [Required]
    [StringLength(64)]
    public string OperatorName { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    public string Username { get; set; } = string.Empty;

    [StringLength(64)]
    public string Module { get; set; } = string.Empty;

    [StringLength(64)]
    public string Operation { get; set; } = string.Empty;

    [StringLength(255)]
    public string Description { get; set; } = string.Empty;

    [StringLength(64)]
    public string Location { get; set; } = string.Empty;

    [StringLength(15)]
    public string OperationIp { get; set; } = string.Empty;

    [StringLength(15)]
    public string IpAddress { get; set; } = string.Empty;

    [StringLength(32)]
    public string OperationSystem { get; set; } = string.Empty;

    [StringLength(32)]
    public string BrowserType { get; set; } = string.Empty;

    [StringLength(255)]
    public string UserAgent { get; set; } = string.Empty;

    [StringLength(16)]
    public string OperationStatus { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1;

    public int ExecutionTime { get; set; } = 0;

    public DateTime? CreatedAt { get; set; }

    [NotMapped]
    public DateTime OperationTime => CreatedAt.HasValue ? CreatedAt.Value : DateTime.Now;
}
