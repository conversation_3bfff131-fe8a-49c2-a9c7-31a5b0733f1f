using System.Text.Json.Serialization;

namespace WcsNet.DTOs;

/// <summary>
/// 分页请求基类
/// </summary>
public class PagedRequest
{
    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [JsonPropertyName("page_no")]
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页大小
    /// </summary>
    [JsonPropertyName("page_size")]
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// API响应基类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    [JsonPropertyName("code")]
    public int Code { get; set; } = 0;

    [JsonPropertyName("msg")]
    public string Message { get; set; } = "Success";

    [JsonPropertyName("data")]
    public T? Data { get; set; }

    public static ApiResponse<T> Success(T data, string message = "Success")
    {
        return new ApiResponse<T>
        {
            Code = 0,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> Error(int code, string message)
    {
        return new ApiResponse<T>
        {
            Code = code,
            Message = message,
            Data = default
        };
    }
}

/// <summary>
/// 无数据的API响应
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    public static ApiResponse Success(string message = "Success")
    {
        return new ApiResponse
        {
            Code = 0,
            Message = message,
            Data = null
        };
    }

    public static new ApiResponse Error(int code, string message)
    {
        return new ApiResponse
        {
            Code = code,
            Message = message,
            Data = null
        };
    }
}

/// <summary>
/// 分页响应DTO
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResponse<T>
{
    /// <summary>
    /// 数据列表（前端期望的字段名为list）
    /// </summary>
    [JsonPropertyName("list")]
    public List<T> Items { get; set; } = new();

    public long Total { get; set; }
    public int PageIndex { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);
}
