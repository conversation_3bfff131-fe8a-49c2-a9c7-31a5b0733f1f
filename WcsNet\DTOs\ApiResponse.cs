using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;

namespace WcsNet.DTOs;

/// <summary>
/// 分页请求基类
/// </summary>
public class PagedRequest
{
    /// <summary>
    /// 页码（从1开始）
    /// </summary>
    [JsonPropertyName("page_no")]
    [FromQuery(Name = "page_no")]
    public int PageNo { get; set; } = 1;

    /// <summary>
    /// 每页大小
    /// </summary>
    [JsonPropertyName("page_size")]
    [FromQuery(Name = "page_size")]
    public int PageSize { get; set; } = 10;
}

/// <summary>
/// API响应基类
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class ApiResponse<T>
{
    [JsonPropertyName("code")]
    public int Code { get; set; } = 0;

    [JsonPropertyName("msg")]
    public string Message { get; set; } = "Success";

    [JsonPropertyName("data")]
    public T? Data { get; set; }

    public static ApiResponse<T> Success(T data, string message = "Success")
    {
        return new ApiResponse<T>
        {
            Code = 0,
            Message = message,
            Data = data
        };
    }

    public static ApiResponse<T> Error(int code, string message)
    {
        return new ApiResponse<T>
        {
            Code = code,
            Message = message,
            Data = default
        };
    }
}

/// <summary>
/// 无数据的API响应
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    public static ApiResponse Success(string message = "Success")
    {
        return new ApiResponse
        {
            Code = 0,
            Message = message,
            Data = null
        };
    }

    public static new ApiResponse Error(int code, string message)
    {
        return new ApiResponse
        {
            Code = code,
            Message = message,
            Data = null
        };
    }
}

/// <summary>
/// 分页响应DTO（匹配Go版本的格式）
/// </summary>
/// <typeparam name="T">数据类型</typeparam>
public class PagedResponse<T>
{
    /// <summary>
    /// 数据列表（前端期望的字段名为list）
    /// </summary>
    [JsonPropertyName("list")]
    public List<T> Items { get; set; } = new();

    /// <summary>
    /// 数据列表（为了兼容性，映射到Items）
    /// </summary>
    [JsonIgnore]
    public List<T> List
    {
        get => Items;
        set => Items = value;
    }

    /// <summary>
    /// 总数
    /// </summary>
    [JsonPropertyName("total")]
    public long Total { get; set; }

    /// <summary>
    /// 页码（为了兼容性保留，但不序列化）
    /// </summary>
    [JsonIgnore]
    public int PageIndex { get; set; }

    /// <summary>
    /// 页大小（为了兼容性保留，但不序列化）
    /// </summary>
    [JsonIgnore]
    public int PageSize { get; set; }

    /// <summary>
    /// 总页数（为了兼容性保留，但不序列化）
    /// </summary>
    [JsonIgnore]
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)Total / PageSize) : 0;
}
