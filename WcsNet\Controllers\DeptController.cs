using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 部门管理控制器
/// </summary>
[ApiController]
[Route("api/sys")]
public class DeptController : ControllerBase
{
    private readonly WcsDbContext _context;

    public DeptController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取部门列表（树形结构）
    /// </summary>
    /// <returns>部门树</returns>
    [HttpGet("depts")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetDepts()
    {
        try
        {
            var departments = await _context.Departments
                .OrderBy(d => d.SortNumber)
                .ThenBy(d => d.Id)
                .ToListAsync();

            var deptTree = BuildDeptTree(departments, 0);

            return Ok(ApiResponse<List<object>>.Success(deptTree));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取部门列表（简化版本）
    /// </summary>
    /// <returns>部门列表</returns>
    [HttpGet("deptlist")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetDeptList()
    {
        try
        {
            var departments = await _context.Departments
                .Where(d => d.Status == 1)
                .OrderBy(d => d.SortNumber)
                .ThenBy(d => d.Id)
                .Select(d => new
                {
                    d.Id,
                    d.DeptName,
                    d.ParentId
                })
                .ToListAsync();

            return Ok(ApiResponse<List<object>>.Success(departments.Cast<object>().ToList()));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建部门
    /// </summary>
    /// <param name="request">部门信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("depts")]
    public async Task<ActionResult<ApiResponse<string>>> CreateDept([FromBody] CreateDeptRequest request)
    {
        try
        {
            // 检查部门名称是否已存在（同一父级下）
            var existingDept = await _context.Departments
                .FirstOrDefaultAsync(d => d.DeptName == request.DeptName && d.ParentId == request.ParentId);
            if (existingDept != null)
            {
                return Ok(ApiResponse<string>.Error(400, "同一级别下部门名称已存在"));
            }

            var department = new Department
            {
                DeptName = request.DeptName,
                ParentId = request.ParentId,
                SortNumber = request.Sort ?? 0,
                Status = request.Status ?? 1,
                Brief = request.Remark ?? ""
            };

            _context.Departments.Add(department);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("部门创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <param name="request">部门信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("depts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDept(uint id, [FromBody] UpdateDeptRequest request)
    {
        try
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return Ok(ApiResponse<string>.Error(404, "部门不存在"));
            }

            // 检查部门名称是否被其他部门使用（同一父级下）
            if (!string.IsNullOrEmpty(request.DeptName) && request.DeptName != department.DeptName)
            {
                var parentId = request.ParentId ?? department.ParentId;
                var existingDept = await _context.Departments
                    .FirstOrDefaultAsync(d => d.DeptName == request.DeptName && d.ParentId == parentId && d.Id != id);
                if (existingDept != null)
                {
                    return Ok(ApiResponse<string>.Error(400, "同一级别下部门名称已被其他部门使用"));
                }
                department.DeptName = request.DeptName;
            }

            if (request.ParentId.HasValue) department.ParentId = request.ParentId.Value;
            if (request.Sort.HasValue) department.SortNumber = request.Sort.Value;
            if (request.Status.HasValue) department.Status = request.Status.Value;
            if (request.Remark != null) department.Brief = request.Remark;

            department.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("部门更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除部门
    /// </summary>
    /// <param name="id">部门ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("depts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDept(uint id)
    {
        try
        {
            var department = await _context.Departments.FindAsync(id);
            if (department == null)
            {
                return Ok(ApiResponse<string>.Error(404, "部门不存在"));
            }

            // 检查是否有子部门
            var childCount = await _context.Departments.CountAsync(d => d.ParentId == id);
            if (childCount > 0)
            {
                return Ok(ApiResponse<string>.Error(400, "该部门下还有子部门，无法删除"));
            }

            // 检查是否有用户属于此部门
            var userCount = await _context.Users.CountAsync(u => u.DeptId == id);
            if (userCount > 0)
            {
                return Ok(ApiResponse<string>.Error(400, $"该部门下还有 {userCount} 个用户，无法删除"));
            }

            _context.Departments.Remove(department);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("部门删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 构建部门树
    /// </summary>
    /// <param name="departments">部门列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>部门树</returns>
    private List<object> BuildDeptTree(List<Department> departments, uint parentId)
    {
        var result = new List<object>();

        foreach (var dept in departments.Where(d => d.ParentId == (ulong)parentId))
        {
            var children = BuildDeptTree(departments, (uint)dept.Id);

            var deptItem = new
            {
                id = dept.Id,
                name = dept.DeptName,  // 前端期望的字段名
                code = dept.DeptCode,  // 添加部门编码
                parent_id = dept.ParentId,
                sort = dept.SortNumber,
                status = dept.Status,
                brief = dept.Brief,
                created_at = dept.CreatedAt,
                updated_at = dept.UpdatedAt,
                children = children
            };

            result.Add(deptItem);
        }

        return result;
    }
}

/// <summary>
/// 创建部门请求
/// </summary>
public class CreateDeptRequest
{
    public string DeptName { get; set; } = string.Empty;
    public uint ParentId { get; set; }
    public int? Sort { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// 更新部门请求
/// </summary>
public class UpdateDeptRequest
{
    public string? DeptName { get; set; }
    public uint? ParentId { get; set; }
    public int? Sort { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}
