using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Microsoft.AspNetCore.Mvc;

namespace WcsNet.DTOs;

/// <summary>
/// 设备列表查询请求
/// </summary>
public class DeviceListRequest : PagedRequest
{
    /// <summary>
    /// 设备名称
    /// </summary>
    [JsonPropertyName("device_name")]
    [FromQuery(Name = "device_name")]
    public string? DeviceName { get; set; }

    /// <summary>
    /// 设备编号
    /// </summary>
    [JsonPropertyName("device_code")]
    [FromQuery(Name = "device_code")]
    public string? DeviceCode { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    [JsonPropertyName("device_type")]
    [FromQuery(Name = "device_type")]
    public sbyte? DeviceType { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public sbyte? Status { get; set; }
}

/// <summary>
/// 设备创建/更新请求
/// </summary>
public class DeviceRequest
{
    /// <summary>
    /// 设备编号
    /// </summary>
    [Required(ErrorMessage = "设备编号不能为空")]
    [StringLength(128, ErrorMessage = "设备编号长度不能超过128个字符")]
    [JsonPropertyName("device_code")]
    public string DeviceCode { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称
    /// </summary>
    [Required(ErrorMessage = "设备名称不能为空")]
    [StringLength(128, ErrorMessage = "设备名称长度不能超过128个字符")]
    [JsonPropertyName("device_name")]
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 设备类型
    /// </summary>
    [Required(ErrorMessage = "设备类型不能为空")]
    [JsonPropertyName("device_type")]
    public sbyte DeviceType { get; set; }

    /// <summary>
    /// 通信方式
    /// </summary>
    [Required(ErrorMessage = "通信方式不能为空")]
    [JsonPropertyName("comm_type")]
    public sbyte CommType { get; set; }

    /// <summary>
    /// 通信地址
    /// </summary>
    [StringLength(128, ErrorMessage = "通信地址长度不能超过128个字符")]
    public string Addr { get; set; } = string.Empty;

    /// <summary>
    /// 端口号
    /// </summary>
    [Range(0, 65535, ErrorMessage = "端口号必须在0-65535之间")]
    public long Port { get; set; } = 0;

    /// <summary>
    /// 状态：1启用 2停用
    /// </summary>
    [Range(1, 2, ErrorMessage = "状态值必须为1或2")]
    public sbyte Status { get; set; } = 1;

    /// <summary>
    /// X轴位置
    /// </summary>
    public int Position { get; set; } = 0;
}

/// <summary>
/// 设备响应DTO
/// </summary>
public class DeviceDto
{
    public ulong Id { get; set; }

    [JsonPropertyName("device_code")]
    public string DeviceCode { get; set; } = string.Empty;

    [JsonPropertyName("device_name")]
    public string DeviceName { get; set; } = string.Empty;

    [JsonPropertyName("device_type")]
    public sbyte DeviceType { get; set; }

    [JsonPropertyName("comm_type")]
    public sbyte CommType { get; set; }

    public string Addr { get; set; } = string.Empty;
    public long Port { get; set; }
    public sbyte Online { get; set; }

    [JsonPropertyName("run_status")]
    public sbyte RunStatus { get; set; }

    public sbyte Status { get; set; }
    public int Position { get; set; }

    [JsonPropertyName("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updatedAt")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// 设备属性列表查询请求
/// </summary>
public class DevicePropertyListRequest
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public ulong DeviceId { get; set; }

    /// <summary>
    /// 属性名称（可选，用于搜索）
    /// </summary>
    public string? PropName { get; set; }
}

/// <summary>
/// 设备属性创建/更新请求
/// </summary>
public class DevicePropertyRequest
{
    /// <summary>
    /// 设备ID
    /// </summary>
    [Required(ErrorMessage = "设备ID不能为空")]
    public ulong DeviceId { get; set; }

    /// <summary>
    /// 属性编码
    /// </summary>
    [Required(ErrorMessage = "属性编码不能为空")]
    [StringLength(64, ErrorMessage = "属性编码长度不能超过64个字符")]
    public string PropCode { get; set; } = string.Empty;

    /// <summary>
    /// 属性名称
    /// </summary>
    [Required(ErrorMessage = "属性名称不能为空")]
    [StringLength(128, ErrorMessage = "属性名称长度不能超过128个字符")]
    public string PropName { get; set; } = string.Empty;

    /// <summary>
    /// 属性地址
    /// </summary>
    [StringLength(64, ErrorMessage = "属性地址长度不能超过64个字符")]
    public string Addr { get; set; } = string.Empty;

    /// <summary>
    /// 方向
    /// </summary>
    public sbyte Direction { get; set; } = 0;

    /// <summary>
    /// Modbus类型
    /// </summary>
    public sbyte ModbusType { get; set; } = 0;

    /// <summary>
    /// PLC类型
    /// </summary>
    [StringLength(16, ErrorMessage = "PLC类型长度不能超过16个字符")]
    public string PlcType { get; set; } = string.Empty;

    /// <summary>
    /// 属性长度
    /// </summary>
    public sbyte PropLength { get; set; } = 0;

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(128, ErrorMessage = "备注长度不能超过128个字符")]
    public string Remark { get; set; } = string.Empty;
}

/// <summary>
/// 设备属性响应DTO
/// </summary>
public class DevicePropertyDto
{
    public ulong Id { get; set; }
    public ulong DeviceId { get; set; }
    public string PropCode { get; set; } = string.Empty;
    public string PropName { get; set; } = string.Empty;
    public string Addr { get; set; } = string.Empty;
    public sbyte Direction { get; set; }
    public sbyte ModbusType { get; set; }
    public string PlcType { get; set; } = string.Empty;
    public sbyte PropLength { get; set; }
    public string Remark { get; set; } = string.Empty;
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}
