using System.ComponentModel.DataAnnotations;

namespace WcsNet.DTOs;

/// <summary>
/// 登录请求DTO
/// </summary>
public class LoginRequest
{
    [Required(ErrorMessage = "账号不能为空")]
    public string Account { get; set; } = string.Empty;

    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = string.Empty;

    /// <summary>
    /// 登录有效天数，默认为0（2小时）
    /// </summary>
    public int Days { get; set; } = 0;
}

/// <summary>
/// 刷新Token请求DTO
/// </summary>
public class RefreshTokenRequest
{
    [Required(ErrorMessage = "刷新Token不能为空")]
    public string RefreshToken { get; set; } = string.Empty;
}
