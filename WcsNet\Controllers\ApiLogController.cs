using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api")]
public class ApiLogController : ControllerBase
{
    private readonly WcsDbContext _context;

    public ApiLogController(WcsDbContext context)
    {
        _context = context;
    }
    /// <summary>
    /// 获取API日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="api_name">API名称</param>
    /// <param name="status">状态</param>
    /// <returns>API日志列表</returns>
    [HttpGet("apilogs")]
    public async Task<ActionResult<ApiResponse<object>>> GetApiLogs(
        int page_size = 10,
        int page_no = 1,
        string? api_name = null,
        int? status = null)
    {
        try
        {
            // 从数据库获取API日志数据
            var query = _context.ApiLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(api_name))
            {
                query = query.Where(l => l.ApiName.Contains(api_name));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            var total = await query.CountAsync();
            var apiLogs = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new
                {
                    l.Id,
                    ApiName = l.ApiName,
                    CreatedAt = l.CreatedAt ?? DateTime.Now,
                    UpdatedAt = l.UpdatedAt ?? DateTime.Now,
                    l.Status
                })
                .ToListAsync();

            var response = new
            {
                list = apiLogs,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取API日志详情
    /// </summary>
    /// <param name="id">日志ID</param>
    /// <returns>API日志详情</returns>
    [HttpGet("apilogs/{id}")]
    public ActionResult<ApiResponse<ApiLogDto>> GetApiLog(int id)
    {
        try
        {
            var random = new Random();
            var methods = new[] { "GET", "POST", "PUT", "DELETE" };
            var urls = new[] { "/api/sys/users", "/api/sys/roles", "/api/work", "/api/devices" };
            var userAgents = new[] { "Chrome/91.0", "Firefox/89.0", "Safari/14.1", "Edge/91.0" };
            
            var method = methods[random.Next(methods.Length)];
            var url = urls[random.Next(urls.Length)];
            var statusCode = random.Next(0, 10) < 8 ? 200 : (random.Next(0, 2) == 0 ? 404 : 500);
            
            var apiLog = new ApiLogDto
            {
                Id = id,
                Method = method,
                Url = url,
                RequestBody = method == "POST" || method == "PUT" ? "{\"data\":\"test\"}" : "",
                ResponseBody = statusCode == 200 ? "{\"code\":0,\"msg\":\"Success\"}" : "{\"code\":1,\"msg\":\"Error\"}",
                StatusCode = statusCode,
                Duration = random.Next(10, 500),
                UserAgent = userAgents[random.Next(userAgents.Length)],
                ClientIp = $"192.168.1.{random.Next(1, 255)}",
                CreateTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
            };

            return Ok(ApiResponse<ApiLogDto>.Success(apiLog));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<ApiLogDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除API日志
    /// </summary>
    /// <param name="id">日志ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("apilogs/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteApiLog(ulong id)
    {
        try
        {
            var apiLog = await _context.ApiLogs.FindAsync(id);
            if (apiLog == null)
            {
                return Ok(ApiResponse<string>.Error(404, "API日志不存在"));
            }

            _context.ApiLogs.Remove(apiLog);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("API日志删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}
