using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api")]
public class ApiLogController : ControllerBase
{
    /// <summary>
    /// 获取API日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>API日志列表</returns>
    [HttpGet("apilogss")]
    public ActionResult<ApiResponse<PagedResponse<ApiLogDto>>> GetApiLogs(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟API日志数据
            var apiLogs = new List<ApiLogDto>();
            var random = new Random();
            var methods = new[] { "GET", "POST", "PUT", "DELETE" };
            var urls = new[] { "/api/sys/users", "/api/sys/roles", "/api/work", "/api/devices" };
            var userAgents = new[] { "Chrome/91.0", "Firefox/89.0", "Safari/14.1", "Edge/91.0" };
            
            for (int i = 1; i <= page_size; i++)
            {
                var method = methods[random.Next(methods.Length)];
                var url = urls[random.Next(urls.Length)];
                var statusCode = random.Next(0, 10) < 8 ? 200 : (random.Next(0, 2) == 0 ? 404 : 500);
                
                apiLogs.Add(new ApiLogDto
                {
                    Id = i,
                    Method = method,
                    Url = url,
                    RequestBody = method == "POST" || method == "PUT" ? "{\"data\":\"test\"}" : "",
                    ResponseBody = statusCode == 200 ? "{\"code\":0,\"msg\":\"Success\"}" : "{\"code\":1,\"msg\":\"Error\"}",
                    StatusCode = statusCode,
                    Duration = random.Next(10, 500),
                    UserAgent = userAgents[random.Next(userAgents.Length)],
                    ClientIp = $"192.168.1.{random.Next(1, 255)}",
                    CreateTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            var response = new PagedResponse<ApiLogDto>
            {
                List = apiLogs,
                Total = 500 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<ApiLogDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<ApiLogDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取API日志详情
    /// </summary>
    /// <param name="id">日志ID</param>
    /// <returns>API日志详情</returns>
    [HttpGet("apilogss/{id}")]
    public ActionResult<ApiResponse<ApiLogDto>> GetApiLog(int id)
    {
        try
        {
            var random = new Random();
            var methods = new[] { "GET", "POST", "PUT", "DELETE" };
            var urls = new[] { "/api/sys/users", "/api/sys/roles", "/api/work", "/api/devices" };
            var userAgents = new[] { "Chrome/91.0", "Firefox/89.0", "Safari/14.1", "Edge/91.0" };
            
            var method = methods[random.Next(methods.Length)];
            var url = urls[random.Next(urls.Length)];
            var statusCode = random.Next(0, 10) < 8 ? 200 : (random.Next(0, 2) == 0 ? 404 : 500);
            
            var apiLog = new ApiLogDto
            {
                Id = id,
                Method = method,
                Url = url,
                RequestBody = method == "POST" || method == "PUT" ? "{\"data\":\"test\"}" : "",
                ResponseBody = statusCode == 200 ? "{\"code\":0,\"msg\":\"Success\"}" : "{\"code\":1,\"msg\":\"Error\"}",
                StatusCode = statusCode,
                Duration = random.Next(10, 500),
                UserAgent = userAgents[random.Next(userAgents.Length)],
                ClientIp = $"192.168.1.{random.Next(1, 255)}",
                CreateTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
            };

            return Ok(ApiResponse<ApiLogDto>.Success(apiLog));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<ApiLogDto>.Error(500, ex.Message));
        }
    }
}
