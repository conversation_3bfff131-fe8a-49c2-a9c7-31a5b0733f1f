using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 权限/菜单服务（与Go版本Permission service保持一致）
/// </summary>
public class PermissionService
{
    private readonly WcsDbContext _context;

    public PermissionService(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 根据角色ID获取菜单列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>菜单列表</returns>
    public async Task<List<Permission>> GetRoleMenuListAsync(int roleId)
    {
        var permissions = new List<Permission>();

        if (roleId == 1) // 超级管理员，获取所有菜单
        {
            permissions = await _context.Permissions
                .Where(p => p.PermType < 3 && p.ShowLink == 1)
                .OrderBy(p => p.Rank)
                .ThenBy(p => p.Id)
                .ToListAsync();
        }
        else
        {
            // 获取角色的权限ID列表
            var role = await _context.Roles.FindAsync((uint)roleId);
            if (role == null || string.IsNullOrEmpty(role.Perms))
            {
                return permissions;
            }

            // 解析权限ID字符串
            var permIds = role.Perms.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => uint.Parse(id.Trim()))
                .ToList();

            if (permIds.Any())
            {
                permissions = await _context.Permissions
                    .Where(p => p.PermType < 3 && permIds.Contains((uint)p.Id))
                    .OrderBy(p => p.Rank)
                    .ThenBy(p => p.Id)
                    .ToListAsync();
            }
        }

        return permissions;
    }

    /// <summary>
    /// 将Permission实体转换为MenuDto
    /// </summary>
    /// <param name="permissions">权限列表</param>
    /// <returns>菜单DTO列表</returns>
    public List<MenuDto> ConvertToMenuDtos(List<Permission> permissions)
    {
        var menuList = new List<MenuDto>();

        foreach (var perm in permissions)
        {
            var meta = new MenuMeta
            {
                Title = perm.Title,
                Icon = perm.Icon,
                FrameSrc = perm.FrameSrc,
                KeepAlive = perm.Keepalive == 1,
                Rank = perm.Rank,
                ShowParent = perm.ShowParent == 1,
                ShowLink = perm.ShowLink == 1,
                ExtraIcon = perm.ExtraIcon,
                HideTag = perm.HideTag == 1,
                FixedTag = perm.FixedTag == 1
            };

            var menuDto = new MenuDto
            {
                Id = (int)perm.Id,
                ParentId = (int)perm.ParentId,
                Name = perm.Name,
                Path = perm.RoutePath,
                Component = perm.Component,
                Redirect = perm.Redirect,
                Meta = meta
            };

            menuList.Add(menuDto);
        }

        return menuList;
    }

    /// <summary>
    /// 构建菜单树结构
    /// </summary>
    /// <param name="menuList">菜单列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>菜单树</returns>
    public List<MenuDto> BuildMenuTree(List<MenuDto> menuList, int parentId = 0)
    {
        var result = new List<MenuDto>();

        foreach (var menu in menuList.Where(m => m.ParentId == parentId))
        {
            menu.Children = BuildMenuTree(menuList, menu.Id);
            result.Add(menu);
        }

        return result;
    }
}
