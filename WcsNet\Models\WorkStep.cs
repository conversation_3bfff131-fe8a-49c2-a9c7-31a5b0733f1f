using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_work_step")]
public class WorkStep : BaseModel
{
    [Column("work_id")]
    public ulong WorkId { get; set; } = 0;
    
    [Required]
    [StringLength(64)]
    public string StepName { get; set; } = string.Empty;
    
    public short OpType { get; set; } = 0; // 操作类型
    
    [StringLength(1024)]
    public string Params { get; set; } = string.Empty; // JSON参数
    
    public int StepOrder { get; set; } = 0; // 步骤顺序
    public sbyte Status { get; set; } = 1; // 1待执行 2执行中 3已完成 4失败
    
    [StringLength(255)]
    public string ErrorMsg { get; set; } = string.Empty;
    
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    
    // 导航属性
    [ForeignKey("WorkId")]
    public virtual Work? Work { get; set; }
}
