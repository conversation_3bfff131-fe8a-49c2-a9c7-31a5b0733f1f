﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_work_step")]
public class WorkStep : BaseModel
{
    [Column("work_id")]
    public ulong WorkId { get; set; }

    [Column("step_number")]
    public short StepNumber { get; set; } = 1;

    [Column("actuator_id")]
    public ulong ActuatorId { get; set; } = 0;

    [Column("device_type")]
    public sbyte DeviceType { get; set; } = 0;

    [Column("op_type")]
    public short OpType { get; set; } = 0;

    [Column(TypeName = "text")]
    public string Params { get; set; } = string.Empty;

    [Column("end_time")]
    public DateTime? EndTime { get; set; }

    public sbyte Status { get; set; } = 1; // 1待执行 2执行中 3失败 4成功

    [StringLength(128)]
    public string Remark { get; set; } = string.Empty;
}
