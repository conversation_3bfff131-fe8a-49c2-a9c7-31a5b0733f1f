/*
 Navicat Premium Dump SQL

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80026 (8.0.26)
 Source Host           : *************:3306
 Source Schema         : wcs

 Target Server Type    : MySQL
 Target Server Version : 80026 (8.0.26)
 File Encoding         : 65001

 Date: 23/06/2025 14:13:47
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for log_alarm
-- ----------------------------
DROP TABLE IF EXISTS `log_alarm`;
CREATE TABLE `log_alarm`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `alarm_type` bigint NOT NULL DEFAULT 1 COMMENT '报警类型',
  `category` int NOT NULL DEFAULT 0 COMMENT '报警分类',
  `code` bigint NOT NULL DEFAULT 0 COMMENT '报警码',
  `device_id` int NOT NULL DEFAULT 0 COMMENT '设备ID',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '报警说明',
  `created_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8947 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for log_login
-- ----------------------------
DROP TABLE IF EXISTS `log_login`;
CREATE TABLE `log_login`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '账号',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '登录ip',
  `os` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作系统',
  `browser` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '浏览器',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '登录状态 1成功 2失败',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for log_operation
-- ----------------------------
DROP TABLE IF EXISTS `log_operation`;
CREATE TABLE `log_operation`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `oper` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作人',
  `module` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块',
  `uri` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '登录ip',
  `os` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作系统',
  `browser` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '浏览器',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '登录状态 1成功 2失败',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_module`(`module` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3108 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for stat_task
-- ----------------------------
DROP TABLE IF EXISTS `stat_task`;
CREATE TABLE `stat_task`  (
  `d` date NOT NULL DEFAULT (curdate()) COMMENT '日期',
  `task_n` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务数',
  `succ_n` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '成功数量',
  `total_succ` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '总完成数',
  PRIMARY KEY (`d`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_department
-- ----------------------------
DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '上级部门id',
  `ancestors` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `dept_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门编号',
  `leader` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门负责人',
  `sort_number` int NOT NULL DEFAULT 0 COMMENT '排序值',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1正常 2停用',
  `brief` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门简介',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_dept`(`parent_id` ASC, `dept_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict`;
CREATE TABLE `sys_dict`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `dict_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典编码',
  `dict_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典名称',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '字典类型 1:正常 2:停用',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_sys_dict_dict_code`(`dict_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_dict_value
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_value`;
CREATE TABLE `sys_dict_value`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `dict_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '字典id',
  `dict_label` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字典值',
  `sort_number` bigint NOT NULL DEFAULT 0 COMMENT '排序值',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:停用',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `dict_code_idx`(`dict_id` ASC, `dict_value` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_parameter
-- ----------------------------
DROP TABLE IF EXISTS `sys_parameter`;
CREATE TABLE `sys_parameter`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `conf_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '参数名称',
  `conf_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数名',
  `conf_value` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '参数值',
  `conf_type` tinyint NOT NULL DEFAULT 2 COMMENT '参数类型 1系统内置 2用户定义',
  `remark` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sys_parameter_key`(`conf_key` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `perm_type` tinyint NOT NULL DEFAULT 0 COMMENT '0:菜单 1:frame 2:外链 3:按钮',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '菜单名称',
  `parent_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '父id',
  `route_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '路由地址',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '组件名称',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '前端组件路径',
  `redirect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '重定向地址',
  `rank` int NOT NULL DEFAULT 0 COMMENT '排序值',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '权限图标',
  `extra_icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '右侧图标',
  `active_path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '菜单激活',
  `frame_src` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'iframe链接地址',
  `frame_loading` tinyint NOT NULL DEFAULT 1 COMMENT '首次加载动画 1有 0无',
  `show_link` tinyint NOT NULL DEFAULT 0 COMMENT '是否在菜单中显示 0否 1是',
  `hide_tag` tinyint NOT NULL DEFAULT 0 COMMENT '是否隐藏 0否 1是',
  `keepalive` tinyint NOT NULL DEFAULT 0 COMMENT '是否缓存 0不缓存 1缓存',
  `fixed_tag` tinyint NOT NULL DEFAULT 0 COMMENT '固定标签 0不固定 1固定',
  `show_parent` tinyint NOT NULL DEFAULT 0 COMMENT '显示父菜单 0不显示 1显示',
  `perm_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '权限标识，在按钮时使用',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 353 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `role_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `role_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '角色编码',
  `role_type` tinyint NULL DEFAULT 3 COMMENT '角色类型 1 系统管理员 2 系统普通角色 3普通角色',
  `sort_number` int NOT NULL DEFAULT 0 COMMENT '排序值',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1启用 2停用',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `perms` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '权限id,用半角逗号分隔',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_sys_role_role_code`(`role_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `account` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '登录账号',
  `password` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '登录密码',
  `realname` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
  `staff_code` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '员工编号',
  `photo` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '头像',
  `dept_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '部门id',
  `role_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '角色id',
  `mobile` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `gender` tinyint NOT NULL DEFAULT 3 COMMENT '性别 1男 2女 3未知',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1启用 2停用',
  `user_source` tinyint NOT NULL DEFAULT 1 COMMENT '用户来源 1内部 2外部',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_sys_user_account`(`account` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_actuator
-- ----------------------------
DROP TABLE IF EXISTS `wcs_actuator`;
CREATE TABLE `wcs_actuator`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `act_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '执行器名称',
  `group_id` bigint NOT NULL DEFAULT 0 COMMENT '所属分组id',
  `class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '类名',
  `method_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '方法名称',
  `act_type` tinyint NOT NULL DEFAULT 0 COMMENT '类型 1成功回调 2执行函数 3成功条件 4执行条件',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '优先级',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '执行器说明',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_actuator_group
-- ----------------------------
DROP TABLE IF EXISTS `wcs_actuator_group`;
CREATE TABLE `wcs_actuator_group`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `group_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '执行器分组名称',
  `group_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分组类型',
  `sort_number` int NOT NULL DEFAULT 0 COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分组描述',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1正常 2禁用',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_api_log
-- ----------------------------
DROP TABLE IF EXISTS `wcs_api_log`;
CREATE TABLE `wcs_api_log`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `work_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务id',
  `step_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '执行步骤id',
  `api_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '接口名称',
  `req_param` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求参数',
  `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '返回数据',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1成功 2失败',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21053 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_device
-- ----------------------------
DROP TABLE IF EXISTS `wcs_device`;
CREATE TABLE `wcs_device`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `device_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '设备编号',
  `device_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '设备名称',
  `device_type` tinyint NOT NULL DEFAULT 0 COMMENT '设备类型',
  `comm_type` tinyint NOT NULL DEFAULT 0 COMMENT '通信方式',
  `addr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通信地址',
  `port` bigint NOT NULL DEFAULT 0 COMMENT '端口号',
  `online` tinyint NOT NULL DEFAULT 1 COMMENT '1未知 2在线 3离线',
  `run_status` tinyint NOT NULL DEFAULT 1 COMMENT '1未知 2运行 3停止 4报警',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1启用 2停用',
  `position` int NOT NULL DEFAULT 0 COMMENT 'x轴位置',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uni_wcs_device_device_code`(`device_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_device_property
-- ----------------------------
DROP TABLE IF EXISTS `wcs_device_property`;
CREATE TABLE `wcs_device_property`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `device_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属设备id',
  `prop_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '属性编码',
  `prop_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '属性名称',
  `addr` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '属性地址',
  `direction` tinyint NOT NULL DEFAULT 0 COMMENT '方向',
  `modbus_type` tinyint NOT NULL DEFAULT 0 COMMENT 'modbus类型',
  `plc_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'plc类型',
  `prop_length` tinyint NOT NULL DEFAULT 0 COMMENT '属性长度',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_wcs_device_property_device_id`(`device_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_lane
-- ----------------------------
DROP TABLE IF EXISTS `wcs_lane`;
CREATE TABLE `wcs_lane`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `lane_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '巷道名称',
  `lane_code` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '巷道编号',
  `repo_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属仓库id',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_lane_device
-- ----------------------------
DROP TABLE IF EXISTS `wcs_lane_device`;
CREATE TABLE `wcs_lane_device`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `lane_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '巷道id',
  `device_id` bigint NOT NULL DEFAULT 0 COMMENT '设备id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uidx_lane_device`(`lane_id` ASC, `device_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '巷道与设备的绑定关系' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_path_define
-- ----------------------------
DROP TABLE IF EXISTS `wcs_path_define`;
CREATE TABLE `wcs_path_define`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `path_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '路径名称',
  `start_point` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '起点',
  `end_point` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '终点',
  `transit_time` bigint NULL DEFAULT NULL,
  `block_time` bigint NULL DEFAULT NULL,
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_stock
-- ----------------------------
DROP TABLE IF EXISTS `wcs_stock`;
CREATE TABLE `wcs_stock`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `stock_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '库位名称',
  `stock_code` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '库位编码',
  `lane_code` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '巷道编码',
  `shelves` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '货架编码',
  `x` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'x轴坐标',
  `y` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'y轴坐标',
  `z` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'z轴坐标',
  `layer` tinyint NOT NULL DEFAULT 0 COMMENT '所在层数',
  `deep` tinyint NOT NULL DEFAULT 1 COMMENT '货位深度 1或2',
  `side` tinyint NOT NULL DEFAULT 1 COMMENT '所处巷道位置 1左侧 2右侧',
  `abc_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'abc类型',
  `goods_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '货物编码',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '1无任务 2入库中 3出库中 4禁用',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_lane_code`(`lane_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1921 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_task_type
-- ----------------------------
DROP TABLE IF EXISTS `wcs_task_type`;
CREATE TABLE `wcs_task_type`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务类型名',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务类型编码',
  `depo_id` bigint NOT NULL DEFAULT 0 COMMENT '仓库id',
  `level` tinyint NOT NULL DEFAULT 1 COMMENT '优先级',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_work
-- ----------------------------
DROP TABLE IF EXISTS `wcs_work`;
CREATE TABLE `wcs_work`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `task_type` tinyint NOT NULL DEFAULT 0 COMMENT '任务类型',
  `lane_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属巷道id',
  `item_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '箱子编码',
  `parameters` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '任务参数',
  `retries` tinyint NOT NULL DEFAULT 0 COMMENT '重试次数',
  `max_retries` tinyint NOT NULL DEFAULT 3 COMMENT '最大重试次数',
  `err_msg` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '错误消息',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1未执行 2执行中 3执行失败 4执行成功',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3521 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_work_bak
-- ----------------------------
DROP TABLE IF EXISTS `wcs_work_bak`;
CREATE TABLE `wcs_work_bak`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `repo_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '仓库id',
  `task_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务类型',
  `tray_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '托盘号',
  `path_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '路径id',
  `start_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '起始位置',
  `target_position` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '目标位置',
  `end_time` datetime(3) NULL DEFAULT NULL COMMENT '任务结束时间',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1未执行 2执行中 3执行失败 4执行成功',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_work_step
-- ----------------------------
DROP TABLE IF EXISTS `wcs_work_step`;
CREATE TABLE `wcs_work_step`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `work_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务id',
  `step_number` smallint NOT NULL DEFAULT 1 COMMENT '步骤序号',
  `actuator_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '执行器id',
  `device_type` tinyint NOT NULL DEFAULT 0 COMMENT '设备类型',
  `op_type` smallint NOT NULL DEFAULT 0 COMMENT '操作类型',
  `params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '参数',
  `end_time` datetime(3) NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1待执行 2执行中 3失败 4成功',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_work_id`(`work_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 722390278841438214 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for wcs_work_step_bak
-- ----------------------------
DROP TABLE IF EXISTS `wcs_work_step_bak`;
CREATE TABLE `wcs_work_step_bak`  (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `work_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '任务id',
  `step_number` smallint NOT NULL DEFAULT 1 COMMENT '步骤序号',
  `actuator_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '执行器id',
  `end_time` datetime(3) NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 1待执行 2执行中 3失败 4成功',
  `created_id` bigint NOT NULL DEFAULT 0 COMMENT '创建人id',
  `updated_id` bigint NOT NULL DEFAULT 0 COMMENT '更新人id',
  `created_at` datetime(3) NULL DEFAULT NULL,
  `updated_at` datetime(3) NULL DEFAULT NULL,
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_work_id`(`work_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
