using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using System.Diagnostics;

namespace WcsNet.Controllers;

/// <summary>
/// API测试和调试控制器
/// </summary>
[ApiController]
[Route("api/test")]
public class ApiTestController : ControllerBase
{
    /// <summary>
    /// 获取所有可用的API接口列表
    /// </summary>
    /// <returns>API接口列表</returns>
    [HttpGet("endpoints")]
    public ActionResult<ApiResponse<List<object>>> GetEndpoints()
    {
        try
        {
            var endpoints = new List<object>
            {
                // 认证相关
                new { Method = "POST", Path = "/api/auth/login", Description = "用户登录" },
                new { Method = "POST", Path = "/api/auth/logout", Description = "用户登出" },
                new { Method = "GET", Path = "/api/auth/userinfo", Description = "获取用户信息" },

                // 系统管理
                new { Method = "GET", Path = "/api/sys/rolemenus", Description = "获取角色菜单" },
                new { Method = "GET", Path = "/api/sys/users", Description = "获取用户列表" },
                new { Method = "POST", Path = "/api/sys/users", Description = "创建用户" },
                new { Method = "PUT", Path = "/api/sys/users/{id}", Description = "更新用户" },
                new { Method = "DELETE", Path = "/api/sys/users/{id}", Description = "删除用户" },
                new { Method = "POST", Path = "/api/sys/users/status", Description = "修改用户状态" },
                new { Method = "POST", Path = "/api/sys/users/resetpassword", Description = "重置用户密码" },

                new { Method = "GET", Path = "/api/sys/roles", Description = "获取角色列表" },
                new { Method = "GET", Path = "/api/sys/roles/all", Description = "获取所有角色" },
                new { Method = "POST", Path = "/api/sys/roles", Description = "创建角色" },
                new { Method = "PUT", Path = "/api/sys/roles/{id}", Description = "更新角色" },
                new { Method = "DELETE", Path = "/api/sys/roles/{id}", Description = "删除角色" },
                new { Method = "GET", Path = "/api/sys/roles/menus/{id}", Description = "获取角色菜单权限" },
                new { Method = "PUT", Path = "/api/sys/roles/menus/{id}", Description = "设置角色菜单权限" },

                new { Method = "GET", Path = "/api/sys/menus", Description = "获取菜单列表" },
                new { Method = "GET", Path = "/api/sys/menulist", Description = "获取菜单列表(简化)" },
                new { Method = "POST", Path = "/api/sys/menus", Description = "创建菜单" },
                new { Method = "PUT", Path = "/api/sys/menus/{id}", Description = "更新菜单" },
                new { Method = "DELETE", Path = "/api/sys/menus/{id}", Description = "删除菜单" },

                new { Method = "GET", Path = "/api/sys/depts", Description = "获取部门列表" },
                new { Method = "GET", Path = "/api/sys/deptlist", Description = "获取部门列表(简化)" },
                new { Method = "POST", Path = "/api/sys/depts", Description = "创建部门" },
                new { Method = "PUT", Path = "/api/sys/depts/{id}", Description = "更新部门" },
                new { Method = "DELETE", Path = "/api/sys/depts/{id}", Description = "删除部门" },

                new { Method = "GET", Path = "/api/sys/dicts", Description = "获取字典列表" },
                new { Method = "POST", Path = "/api/sys/dicts", Description = "创建字典" },
                new { Method = "PUT", Path = "/api/sys/dicts/{id}", Description = "更新字典" },
                new { Method = "DELETE", Path = "/api/sys/dicts/{id}", Description = "删除字典" },
                new { Method = "GET", Path = "/api/sys/dictvalues/{dictId}", Description = "获取字典值列表" },
                new { Method = "POST", Path = "/api/sys/dictvalues", Description = "创建字典值" },
                new { Method = "PUT", Path = "/api/sys/dictvalues/{id}", Description = "更新字典值" },
                new { Method = "DELETE", Path = "/api/sys/dictvalues/{id}", Description = "删除字典值" },

                // 数据库初始化
                new { Method = "GET", Path = "/api/init/status", Description = "获取数据库状态" },
                new { Method = "POST", Path = "/api/init/database", Description = "初始化数据库" },
                new { Method = "POST", Path = "/api/init/reset", Description = "重置数据库" },

                // 统计数据
                new { Method = "GET", Path = "/api/stat/tasks", Description = "获取任务统计" },
                new { Method = "GET", Path = "/api/stat/weektasks/{type}", Description = "获取周任务统计" },

                // 告警管理
                new { Method = "GET", Path = "/api/alarms", Description = "获取告警列表" },

                // 设备管理
                new { Method = "GET", Path = "/api/devices", Description = "获取设备列表" },
                new { Method = "POST", Path = "/api/devices", Description = "创建设备" },
                new { Method = "PUT", Path = "/api/devices/{id}", Description = "更新设备" },
                new { Method = "DELETE", Path = "/api/devices/{id}", Description = "删除设备" },

                // 任务管理
                new { Method = "GET", Path = "/api/works", Description = "获取任务列表" },
                new { Method = "POST", Path = "/api/works", Description = "创建任务" },
                new { Method = "PUT", Path = "/api/works/{id}", Description = "更新任务" },
                new { Method = "DELETE", Path = "/api/works/{id}", Description = "删除任务" },

                // 库存管理
                new { Method = "GET", Path = "/api/stocks", Description = "获取库存列表" },
                new { Method = "POST", Path = "/api/stocks", Description = "创建库存" },
                new { Method = "PUT", Path = "/api/stocks/{id}", Description = "更新库存" },
                new { Method = "DELETE", Path = "/api/stocks/{id}", Description = "删除库存" },

                // 文件上传
                new { Method = "POST", Path = "/api/upload", Description = "文件上传" }
            };

            return Ok(ApiResponse<List<object>>.Success(endpoints));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 测试API接口连通性
    /// </summary>
    /// <param name="path">API路径</param>
    /// <param name="method">HTTP方法</param>
    /// <returns>测试结果</returns>
    [HttpPost("connectivity")]
    public ActionResult<ApiResponse<object>> TestConnectivity([FromBody] TestConnectivityRequest request)
    {
        try
        {
            var result = new
            {
                Path = request.Path,
                Method = request.Method,
                Status = "Available",
                Timestamp = DateTime.Now,
                Message = "API接口可用"
            };

            return Ok(ApiResponse<object>.Success(result));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取系统状态信息
    /// </summary>
    /// <returns>系统状态</returns>
    [HttpGet("status")]
    public ActionResult<ApiResponse<object>> GetSystemStatus()
    {
        try
        {
            var status = new
            {
                ServerTime = DateTime.Now,
                Version = "1.0.0",
                Environment = "Development",
                Database = "Connected",
                Memory = GC.GetTotalMemory(false),
                Uptime = DateTime.Now - Process.GetCurrentProcess().StartTime
            };

            return Ok(ApiResponse<object>.Success(status));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 生成测试数据
    /// </summary>
    /// <param name="type">数据类型</param>
    /// <param name="count">数据数量</param>
    /// <returns>测试数据</returns>
    [HttpGet("data/{type}")]
    public ActionResult<ApiResponse<List<object>>> GenerateTestData(string type, int count = 10)
    {
        try
        {
            var data = new List<object>();

            switch (type.ToLower())
            {
                case "users":
                    for (int i = 1; i <= count; i++)
                    {
                        data.Add(new
                        {
                            Id = i,
                            Account = $"user{i:D3}",
                            Realname = $"用户{i}",
                            Email = $"user{i}@example.com",
                            Mobile = $"138{i:D8}",
                            Status = 1,
                            RoleId = (i % 3) + 1,
                            DeptId = (i % 5) + 1
                        });
                    }
                    break;

                case "roles":
                    for (int i = 1; i <= count; i++)
                    {
                        data.Add(new
                        {
                            Id = i,
                            RoleName = $"角色{i}",
                            RoleCode = $"role{i}",
                            RoleType = 3,
                            Status = 1,
                            Remark = $"测试角色{i}"
                        });
                    }
                    break;

                case "devices":
                    for (int i = 1; i <= count; i++)
                    {
                        data.Add(new
                        {
                            Id = i,
                            DeviceName = $"设备{i:D3}",
                            DeviceCode = $"DEV{i:D3}",
                            DeviceType = (i % 3) + 1,
                            Status = 1,
                            Location = $"位置{i}"
                        });
                    }
                    break;

                default:
                    return Ok(ApiResponse<List<object>>.Error(400, "不支持的数据类型"));
            }

            return Ok(ApiResponse<List<object>>.Success(data));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 测试连通性请求
/// </summary>
public class TestConnectivityRequest
{
    public string Path { get; set; } = string.Empty;
    public string Method { get; set; } = string.Empty;
}
