using Microsoft.EntityFrameworkCore;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 服务层基础类
/// </summary>
/// <typeparam name="T">实体类型</typeparam>
public abstract class BaseService<T> where T : BaseModel
{
    protected readonly WcsDbContext _context;
    protected readonly DbSet<T> _dbSet;

    protected BaseService(WcsDbContext context)
    {
        _context = context;
        _dbSet = context.Set<T>();
    }

    /// <summary>
    /// 创建实体
    /// </summary>
    /// <param name="entity">实体对象</param>
    /// <returns>创建的实体</returns>
    public virtual async Task<T> CreateAsync(T entity)
    {
        _dbSet.Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }

    /// <summary>
    /// 根据ID查找实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>实体对象，如果不存在则返回null</returns>
    public virtual async Task<T?> FindByIdAsync(uint id)
    {
        return await _dbSet.FindAsync(id);
    }

    /// <summary>
    /// 更新实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <param name="updates">更新字段字典</param>
    /// <param name="operatorId">操作者ID</param>
    /// <returns>是否更新成功</returns>
    public virtual async Task<bool> UpdateAsync(uint id, Dictionary<string, object> updates, int? operatorId = null)
    {
        var entity = await FindByIdAsync(id);
        if (entity == null) return false;

        if (operatorId.HasValue)
        {
            updates["UpdatedId"] = operatorId.Value;
        }

        foreach (var update in updates)
        {
            var property = typeof(T).GetProperty(update.Key);
            if (property != null && property.CanWrite)
            {
                property.SetValue(entity, update.Value);
            }
        }

        // 移除UpdatedAt字段更新
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 删除实体
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>是否删除成功</returns>
    public virtual async Task<bool> DeleteAsync(uint id)
    {
        var entity = await FindByIdAsync(id);
        if (entity == null) return false;

        _dbSet.Remove(entity);
        await _context.SaveChangesAsync();
        return true;
    }

    /// <summary>
    /// 分页查询
    /// </summary>
    /// <param name="pageIndex">页码（从1开始）</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="predicate">查询条件</param>
    /// <returns>分页结果</returns>
    public virtual async Task<(List<T> Items, long Total)> GetPagedAsync(
        int pageIndex, 
        int pageSize, 
        Func<IQueryable<T>, IQueryable<T>>? predicate = null)
    {
        var query = _dbSet.AsQueryable();
        
        if (predicate != null)
        {
            query = predicate(query);
        }

        var total = await query.CountAsync();
        var items = await query
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (items, total);
    }

    /// <summary>
    /// 获取所有实体
    /// </summary>
    /// <param name="predicate">查询条件</param>
    /// <returns>实体列表</returns>
    public virtual async Task<List<T>> GetAllAsync(Func<IQueryable<T>, IQueryable<T>>? predicate = null)
    {
        var query = _dbSet.AsQueryable();
        
        if (predicate != null)
        {
            query = predicate(query);
        }

        return await query.ToListAsync();
    }

    /// <summary>
    /// 检查实体是否存在
    /// </summary>
    /// <param name="id">实体ID</param>
    /// <returns>是否存在</returns>
    public virtual async Task<bool> ExistsAsync(uint id)
    {
        return await _dbSet.AnyAsync(e => e.Id == id);
    }

    /// <summary>
    /// 批量创建实体
    /// </summary>
    /// <param name="entities">实体列表</param>
    /// <returns>创建的实体列表</returns>
    public virtual async Task<List<T>> CreateBatchAsync(List<T> entities)
    {
        _dbSet.AddRange(entities);
        await _context.SaveChangesAsync();
        return entities;
    }

    /// <summary>
    /// 批量删除实体
    /// </summary>
    /// <param name="ids">实体ID列表</param>
    /// <returns>删除的数量</returns>
    public virtual async Task<int> DeleteBatchAsync(List<uint> ids)
    {
        var entities = await _dbSet.Where(e => ids.Contains(e.Id)).ToListAsync();
        _dbSet.RemoveRange(entities);
        await _context.SaveChangesAsync();
        return entities.Count;
    }
}
