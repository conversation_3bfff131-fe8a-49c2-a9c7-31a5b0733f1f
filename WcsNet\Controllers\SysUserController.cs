using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;

namespace WcsNet.Controllers;

/// <summary>
/// 系统用户管理控制器
/// </summary>
[ApiController]
[Route("api/sys/users")]
public class SysUserController : ControllerBase
{
    private readonly WcsDbContext _context;
    private readonly UserService _userService;

    public SysUserController(WcsDbContext context, UserService userService)
    {
        _context = context;
        _userService = userService;
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="keyword">搜索关键词</param>
    /// <returns>用户列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<object>>>> GetUsers(
        int page = 1, 
        int pageSize = 10, 
        string? keyword = null)
    {
        try
        {
            var query = _context.Users.AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(u => u.Account.Contains(keyword) || 
                                        u.Realname.Contains(keyword) || 
                                        u.Email.Contains(keyword));
            }

            var total = await query.CountAsync();
            var users = await query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(u => new
                {
                    u.Id,
                    u.Account,
                    u.Realname,
                    u.Email,
                    u.Mobile,
                    u.Status,
                    u.RoleId,
                    u.DeptId,
                    u.CreatedAt,
                    u.UpdatedAt
                })
                .ToListAsync();

            var response = new PagedResponse<object>
            {
                Items = users.Cast<object>().ToList(),
                Total = total,
                PageIndex = page,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建用户
    /// </summary>
    /// <param name="request">用户信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<string>>> CreateUser([FromBody] CreateUserRequest request)
    {
        try
        {
            // 检查账号是否已存在
            var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Account == request.Account);
            if (existingUser != null)
            {
                return Ok(ApiResponse<string>.Error(400, "账号已存在"));
            }

            var user = new User
            {
                Account = request.Account,
                Password = request.Password, // 密码将在CreateAsync中被哈希
                Realname = request.Realname,
                Email = request.Email ?? "",
                Mobile = request.Mobile ?? "",
                Status = request.Status ?? 1,
                RoleId = request.RoleId ?? 1,
                DeptId = request.DeptId ?? 1
            };

            await _userService.CreateAsync(user);



            return Ok(ApiResponse<string>.Success("用户创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <param name="request">用户信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateUser(uint id, [FromBody] UpdateUserRequest request)
    {
        try
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return Ok(ApiResponse<string>.Error(404, "用户不存在"));
            }

            // 检查账号是否被其他用户使用
            if (!string.IsNullOrEmpty(request.Account) && request.Account != user.Account)
            {
                var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Account == request.Account && u.Id != id);
                if (existingUser != null)
                {
                    return Ok(ApiResponse<string>.Error(400, "账号已被其他用户使用"));
                }
                user.Account = request.Account;
            }

            if (!string.IsNullOrEmpty(request.Realname)) user.Realname = request.Realname;
            if (!string.IsNullOrEmpty(request.Email)) user.Email = request.Email;
            if (!string.IsNullOrEmpty(request.Mobile)) user.Mobile = request.Mobile;
            if (request.Status.HasValue) user.Status = request.Status.Value;
            if (request.RoleId.HasValue) user.RoleId = request.RoleId.Value;
            if (request.DeptId.HasValue) user.DeptId = request.DeptId.Value;

            user.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("用户更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="id">用户ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteUser(uint id)
    {
        try
        {
            var user = await _context.Users.FindAsync(id);
            if (user == null)
            {
                return Ok(ApiResponse<string>.Error(404, "用户不存在"));
            }

            _context.Users.Remove(user);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("用户删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 修改用户状态
    /// </summary>
    /// <param name="request">状态修改请求</param>
    /// <returns>修改结果</returns>
    [HttpPost("status")]
    public async Task<ActionResult<ApiResponse<string>>> ChangeUserStatus([FromBody] ChangeStatusRequest request)
    {
        try
        {
            var user = await _context.Users.FindAsync((uint)request.Id);
            if (user == null)
            {
                return Ok(ApiResponse<string>.Error(404, "用户不存在"));
            }

            user.Status = request.Status;
            user.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("用户状态修改成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 重置用户密码
    /// </summary>
    /// <param name="request">密码重置请求</param>
    /// <returns>重置结果</returns>
    [HttpPost("resetpassword")]
    public async Task<ActionResult<ApiResponse<string>>> ResetPassword([FromBody] ResetPasswordRequest request)
    {
        try
        {
            var user = await _context.Users.FindAsync((uint)request.Id);
            if (user == null)
            {
                return Ok(ApiResponse<string>.Error(404, "用户不存在"));
            }

            await _userService.UpdatePasswordAsync((uint)user.Id, request.Password);
            user.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("密码重置成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 创建用户请求
/// </summary>
public class CreateUserRequest
{
    public string Account { get; set; } = string.Empty;
    public string Password { get; set; } = string.Empty;
    public string Realname { get; set; } = string.Empty;
    public string? Email { get; set; }
    public string? Mobile { get; set; }
    public sbyte? Status { get; set; }
    public uint? RoleId { get; set; }
    public uint? DeptId { get; set; }
}

/// <summary>
/// 更新用户请求
/// </summary>
public class UpdateUserRequest
{
    public string? Account { get; set; }
    public string? Realname { get; set; }
    public string? Email { get; set; }
    public string? Mobile { get; set; }
    public sbyte? Status { get; set; }
    public uint? RoleId { get; set; }
    public uint? DeptId { get; set; }
}

/// <summary>
/// 修改状态请求
/// </summary>
public class ChangeStatusRequest
{
    public int Id { get; set; }
    public sbyte Status { get; set; }
}

/// <summary>
/// 重置密码请求
/// </summary>
public class ResetPasswordRequest
{
    public int Id { get; set; }
    public string Password { get; set; } = string.Empty;
}
