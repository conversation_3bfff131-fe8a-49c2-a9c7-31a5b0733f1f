/*!
  * shared v12.0.0-alpha.2
  * (c) 2016-present ka<PERSON><PERSON> and contributors
  * Released under the MIT License.
  */
const inBrowser="undefined"!=typeof window;let mark,measure;const RE_ARGS=/\{([0-9a-zA-Z]+)\}/g;function format(e,...t){return 1===t.length&&isObject(t[0])&&(t=t[0]),t&&t.hasOwnProperty||(t={}),e.replace(RE_ARGS,((e,n)=>t.hasOwnProperty(n)?t[n]:""))}const makeSymbol=(e,t=!1)=>t?Symbol.for(e):Symbol(e),generateFormatCacheKey=(e,t,n)=>friendlyJSONstringify({l:e,k:t,s:n}),friendlyJSONstringify=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),isNumber=e=>"number"==typeof e&&isFinite(e),isDate=e=>"[object Date]"===toTypeString(e),isRegExp=e=>"[object RegExp]"===toTypeString(e),isEmptyObject=e=>isPlainObject(e)&&0===Object.keys(e).length,assign=Object.assign,_create=Object.create,create=(e=null)=>_create(e);let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:create());function escapeHtml(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const hasOwnProperty=Object.prototype.hasOwnProperty;function hasOwn(e,t){return hasOwnProperty.call(e,t)}const isArray=Array.isArray,isFunction=e=>"function"==typeof e,isString=e=>"string"==typeof e,isBoolean=e=>"boolean"==typeof e,isSymbol=e=>"symbol"==typeof e,isObject=e=>null!==e&&"object"==typeof e,isPromise=e=>isObject(e)&&isFunction(e.then)&&isFunction(e.catch),objectToString=Object.prototype.toString,toTypeString=e=>objectToString.call(e),isPlainObject=e=>"[object Object]"===toTypeString(e),toDisplayString=e=>null==e?"":isArray(e)||isPlainObject(e)&&e.toString===objectToString?JSON.stringify(e,null,2):String(e);function join(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const RANGE=2;function generateCodeFrame(e,t=0,n=e.length){const r=e.split(/\r?\n/);let o=0;const i=[];for(let s=0;s<r.length;s++)if(o+=r[s].length+1,o>=t){for(let e=s-2;e<=s+2||n>o;e++){if(e<0||e>=r.length)continue;const a=e+1;i.push(`${a}${" ".repeat(3-String(a).length)}|  ${r[e]}`);const c=r[e].length;if(e===s){const e=t-(o-c)+1,r=Math.max(1,n>o?c-e:n-t);i.push("   |  "+" ".repeat(e)+"^".repeat(r))}else if(e>s){if(n>o){const e=Math.max(Math.min(n-o,c),1);i.push("   |  "+"^".repeat(e))}o+=c+1}}break}return i.join("\n")}function warn(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const hasWarned={};function warnOnce(e){hasWarned[e]||(hasWarned[e]=!0,warn(e))}function createEmitter(){const e=new Map;return{events:e,on(t,n){const r=e.get(t);r&&r.push(n)||e.set(t,[n])},off(t,n){const r=e.get(t);r&&r.splice(r.indexOf(n)>>>0,1)},emit(t,n){(e.get(t)||[]).slice().map((e=>e(n))),(e.get("*")||[]).slice().map((e=>e(t,n)))}}}const isNotObjectOrIsArray=e=>!isObject(e)||isArray(e);function deepCopy(e,t){if(isNotObjectOrIsArray(e)||isNotObjectOrIsArray(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(isObject(e[r])&&!isObject(t[r])&&(t[r]=Array.isArray(e[r])?[]:create()),isNotObjectOrIsArray(t[r])||isNotObjectOrIsArray(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}export{assign,create,createEmitter,deepCopy,escapeHtml,format,friendlyJSONstringify,generateCodeFrame,generateFormatCacheKey,getGlobalThis,hasOwn,inBrowser,isArray,isBoolean,isDate,isEmptyObject,isFunction,isNumber,isObject,isPlainObject,isPromise,isRegExp,isString,isSymbol,join,makeSymbol,mark,measure,objectToString,toDisplayString,toTypeString,warn,warnOnce};
