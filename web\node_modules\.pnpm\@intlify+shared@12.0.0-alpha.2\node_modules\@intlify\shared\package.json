{"name": "@intlify/shared", "version": "12.0.0-alpha.2", "description": "@intlify/shared", "keywords": ["i18n", "internationalization", "intlify", "utitlity"], "license": "MIT", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "homepage": "https://github.com/intlify/vue-i18n/tree/master/packages/shared#readme", "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n.git", "directory": "packages/shared"}, "bugs": {"url": "https://github.com/intlify/vue-i18n/issues"}, "files": ["dist"], "type": "module", "module": "dist/shared.js", "types": "dist/shared.d.ts", "engines": {"node": ">= 16"}, "buildOptions": {"name": "IntlifyShared", "formats": ["mjs", "browser"]}, "exports": {".": {"types": "./dist/shared.d.ts", "import": "./dist/shared.js", "browser": "./dist/shared.esm-browser.js", "node": {"import": {"development": "./dist/shared.js", "default": "./dist/shared.js"}}}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "funding": "https://github.com/sponsors/kazupon", "publishConfig": {"access": "public"}, "sideEffects": false}