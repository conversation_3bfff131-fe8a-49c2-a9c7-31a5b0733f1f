namespace WcsNet.DTOs;

/// <summary>
/// 菜单DTO
/// </summary>
public class MenuDto
{
    /// <summary>
    /// 菜单ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 父级菜单ID
    /// </summary>
    public int ParentId { get; set; }

    /// <summary>
    /// 路由路径
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// 路由名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 组件路径
    /// </summary>
    public string? Component { get; set; }

    /// <summary>
    /// 重定向路径
    /// </summary>
    public string? Redirect { get; set; }

    /// <summary>
    /// 菜单标题（用于权限设置）
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 是否显示链接
    /// </summary>
    public bool ShowLink { get; set; } = true;

    /// <summary>
    /// 菜单元数据
    /// </summary>
    public MenuMeta? Meta { get; set; }

    /// <summary>
    /// 子菜单
    /// </summary>
    public List<MenuDto>? Children { get; set; }
}

/// <summary>
/// 菜单元数据
/// </summary>
public class MenuMeta
{
    /// <summary>
    /// 菜单标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 菜单图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 是否显示链接
    /// </summary>
    public bool ShowLink { get; set; } = true;

    /// <summary>
    /// 排序
    /// </summary>
    public int Rank { get; set; }

    /// <summary>
    /// 权限列表
    /// </summary>
    public List<string>? Auths { get; set; }

    /// <summary>
    /// 是否隐藏菜单
    /// </summary>
    public bool HiddenTag { get; set; }

    /// <summary>
    /// 是否固定标签
    /// </summary>
    public bool FixedTag { get; set; }

    /// <summary>
    /// 是否显示父级菜单
    /// </summary>
    public bool ShowParent { get; set; }

    /// <summary>
    /// 额外图标
    /// </summary>
    public string? ExtraIcon { get; set; }

    /// <summary>
    /// 进入动画
    /// </summary>
    public string? EnterTransition { get; set; }

    /// <summary>
    /// 离开动画
    /// </summary>
    public string? LeaveTransition { get; set; }

    /// <summary>
    /// 激活路径
    /// </summary>
    public string? ActivePath { get; set; }
}
