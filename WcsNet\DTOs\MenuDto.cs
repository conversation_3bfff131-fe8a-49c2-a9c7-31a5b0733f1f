using System.Text.Json.Serialization;

namespace WcsNet.DTOs;

/// <summary>
/// 菜单DTO（与Go版本MenuList保持一致）
/// </summary>
public class MenuDto
{
    /// <summary>
    /// 菜单ID（不序列化到JSON）
    /// </summary>
    [JsonIgnore]
    public int Id { get; set; }

    /// <summary>
    /// 父级菜单ID（不序列化到JSON）
    /// </summary>
    [JsonIgnore]
    public int ParentId { get; set; }

    /// <summary>
    /// 路由路径
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// 重定向路径
    /// </summary>
    public string? Redirect { get; set; }

    /// <summary>
    /// 路由名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 组件路径
    /// </summary>
    public string? Component { get; set; }

    /// <summary>
    /// 菜单元数据
    /// </summary>
    public MenuMeta Meta { get; set; } = new();

    /// <summary>
    /// 子菜单
    /// </summary>
    public List<MenuDto>? Children { get; set; }
}

/// <summary>
/// 菜单元数据（与Go版本MenuMeta保持一致）
/// </summary>
public class MenuMeta
{
    /// <summary>
    /// 菜单图标
    /// </summary>
    public string Icon { get; set; } = string.Empty;

    /// <summary>
    /// 菜单标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 框架源地址
    /// </summary>
    public string? FrameSrc { get; set; }

    /// <summary>
    /// 是否保持活跃
    /// </summary>
    public bool KeepAlive { get; set; }

    /// <summary>
    /// 排序（不序列化到JSON）
    /// </summary>
    [JsonIgnore]
    public int Rank { get; set; }

    /// <summary>
    /// 是否显示父级菜单
    /// </summary>
    public bool ShowParent { get; set; }

    /// <summary>
    /// 是否显示链接
    /// </summary>
    public bool ShowLink { get; set; } = true;

    /// <summary>
    /// 额外图标
    /// </summary>
    public string? ExtraIcon { get; set; }

    /// <summary>
    /// 是否隐藏标签
    /// </summary>
    [JsonPropertyName("hide_tag")]
    public bool HideTag { get; set; }

    /// <summary>
    /// 是否固定标签
    /// </summary>
    [JsonPropertyName("fixed_tag")]
    public bool FixedTag { get; set; }

    /// <summary>
    /// 权限列表（临时添加，用于兼容现有代码）
    /// </summary>
    [JsonIgnore]
    public List<string>? Auths { get; set; }
}
