using System.Text.Json.Serialization;

namespace WcsNet.DTOs;

/// <summary>
/// 统计任务DTO（与Go版本StatTask保持一致）
/// </summary>
public class StatTaskDto
{
    /// <summary>
    /// 日期
    /// </summary>
    [JsonPropertyName("d")]
    public DateTime Date { get; set; }

    /// <summary>
    /// 任务数
    /// </summary>
    [JsonPropertyName("task_n")]
    public uint TaskCount { get; set; }

    /// <summary>
    /// 成功数量
    /// </summary>
    [JsonPropertyName("succ_n")]
    public uint SuccessCount { get; set; }

    /// <summary>
    /// 总完成数
    /// </summary>
    [JsonPropertyName("total_succ")]
    public uint TotalSuccess { get; set; }
}

/// <summary>
/// 周任务统计DTO
/// </summary>
public class WeekTaskStatDto
{
    /// <summary>
    /// 周ID
    /// </summary>
    public int WeekId { get; set; }

    /// <summary>
    /// 周开始时间
    /// </summary>
    public DateTime WeekStart { get; set; }

    /// <summary>
    /// 周结束时间
    /// </summary>
    public DateTime WeekEnd { get; set; }

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 已完成任务数
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// 待处理任务数
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// 失败任务数
    /// </summary>
    public int FailedTasks { get; set; }

    /// <summary>
    /// 每日统计
    /// </summary>
    public List<DailyTaskStatDto> DailyStats { get; set; } = new();
}

/// <summary>
/// 每日任务统计DTO
/// </summary>
public class DailyTaskStatDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 任务总数
    /// </summary>
    public int TaskCount { get; set; }

    /// <summary>
    /// 完成数量
    /// </summary>
    public int CompletedCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
}

/// <summary>
/// 告警统计DTO（与Go版本Alarm保持一致）
/// </summary>
public class AlarmStatDto
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public uint Id { get; set; }

    /// <summary>
    /// 告警类型
    /// </summary>
    [JsonPropertyName("alarm_type")]
    public int AlarmType { get; set; }

    /// <summary>
    /// 告警分类
    /// </summary>
    public int Category { get; set; }

    /// <summary>
    /// 告警码
    /// </summary>
    public int Code { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    [JsonPropertyName("device_id")]
    public uint DeviceId { get; set; }

    /// <summary>
    /// 告警说明
    /// </summary>
    public string Remark { get; set; } = string.Empty;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
