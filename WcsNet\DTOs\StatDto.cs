namespace WcsNet.DTOs;

/// <summary>
/// 任务统计DTO
/// </summary>
public class TaskStatDto
{
    /// <summary>
    /// 任务ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 任务名称
    /// </summary>
    public string TaskName { get; set; } = string.Empty;

    /// <summary>
    /// 任务类型
    /// </summary>
    public string TaskType { get; set; } = string.Empty;

    /// <summary>
    /// 任务状态
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// 优先级
    /// </summary>
    public int Priority { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreateTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime UpdateTime { get; set; }

    /// <summary>
    /// 进度百分比
    /// </summary>
    public int Progress { get; set; }
}

/// <summary>
/// 周任务统计DTO
/// </summary>
public class WeekTaskStatDto
{
    /// <summary>
    /// 周ID
    /// </summary>
    public int WeekId { get; set; }

    /// <summary>
    /// 周开始时间
    /// </summary>
    public DateTime WeekStart { get; set; }

    /// <summary>
    /// 周结束时间
    /// </summary>
    public DateTime WeekEnd { get; set; }

    /// <summary>
    /// 总任务数
    /// </summary>
    public int TotalTasks { get; set; }

    /// <summary>
    /// 已完成任务数
    /// </summary>
    public int CompletedTasks { get; set; }

    /// <summary>
    /// 待处理任务数
    /// </summary>
    public int PendingTasks { get; set; }

    /// <summary>
    /// 失败任务数
    /// </summary>
    public int FailedTasks { get; set; }

    /// <summary>
    /// 每日统计
    /// </summary>
    public List<DailyTaskStatDto> DailyStats { get; set; } = new();
}

/// <summary>
/// 每日任务统计DTO
/// </summary>
public class DailyTaskStatDto
{
    /// <summary>
    /// 日期
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// 任务总数
    /// </summary>
    public int TaskCount { get; set; }

    /// <summary>
    /// 完成数量
    /// </summary>
    public int CompletedCount { get; set; }

    /// <summary>
    /// 失败数量
    /// </summary>
    public int FailedCount { get; set; }
}

/// <summary>
/// 告警统计DTO
/// </summary>
public class AlarmStatDto
{
    /// <summary>
    /// 告警ID
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// 告警类型
    /// </summary>
    public string AlarmType { get; set; } = string.Empty;

    /// <summary>
    /// 告警级别
    /// </summary>
    public string Level { get; set; } = string.Empty;

    /// <summary>
    /// 告警消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 设备名称
    /// </summary>
    public string DeviceName { get; set; } = string.Empty;

    /// <summary>
    /// 告警时间
    /// </summary>
    public DateTime AlarmTime { get; set; }

    /// <summary>
    /// 是否已处理
    /// </summary>
    public bool IsHandled { get; set; }

    /// <summary>
    /// 处理时间
    /// </summary>
    public DateTime? HandleTime { get; set; }
}
