using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
[Route("api/stocks")] // 添加别名路由
public class StockController : ControllerBase
{
    private readonly StockService _stockService;

    public StockController(StockService stockService)
    {
        _stockService = stockService;
    }

    /// <summary>
    /// 获取库存列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <returns>库存列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<Stock>>>> GetStocks(
        int pageIndex = 1,
        int pageSize = 20,
        string? laneCode = null,
        sbyte? status = null)
    {
        try
        {
            var (items, total) = await _stockService.GetPagedAsync(pageIndex, pageSize, query =>
            {
                if (!string.IsNullOrEmpty(laneCode))
                    query = query.Where(s => s.LaneCode == laneCode);

                if (status.HasValue)
                    query = query.Where(s => s.Status == status.Value);

                return query.OrderBy(s => s.LaneCode)
                           .ThenBy(s => s.X)
                           .ThenBy(s => s.Y)
                           .ThenBy(s => s.Z);
            });

            var response = new PagedResponse<Stock>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Stock>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Stock>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取库存
    /// </summary>
    /// <param name="id">库存ID</param>
    /// <returns>库存信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<Stock>>> GetStock(ulong id)
    {
        try
        {
            var stock = await _stockService.FindByIdAsync(id);
            if (stock == null)
            {
                return Ok(ApiResponse<Stock>.Error(404, "库存不存在"));
            }

            return Ok(ApiResponse<Stock>.Success(stock));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Stock>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建库存
    /// </summary>
    /// <param name="stock">库存信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<Stock>>> CreateStock([FromBody] Stock stock)
    {
        try
        {
            var createdStock = await _stockService.CreateAsync(stock);
            return Ok(ApiResponse<Stock>.Success(createdStock, "库存创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Stock>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新库存
    /// </summary>
    /// <param name="id">库存ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse>> UpdateStock(ulong id, [FromBody] Dictionary<string, object> updates)
    {
        try
        {
            var success = await _stockService.UpdateAsync(id, updates);
            
            if (success)
            {
                return Ok(ApiResponse.Success("库存更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "库存不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除库存
    /// </summary>
    /// <param name="id">库存ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteStock(ulong id)
    {
        try
        {
            var success = await _stockService.DeleteAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("库存删除成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "库存不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 入库操作
    /// </summary>
    /// <param name="id">库存ID</param>
    /// <param name="request">入库请求</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/inbound")]
    public async Task<ActionResult<ApiResponse>> Inbound(ulong id, [FromBody] InboundRequest request)
    {
        try
        {
            var success = await _stockService.InboundAsync(id, request.GoodsCode);
            
            if (success)
            {
                return Ok(ApiResponse.Success("入库成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "库存不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 出库操作
    /// </summary>
    /// <param name="id">库存ID</param>
    /// <returns>操作结果</returns>
    [HttpPost("{id}/outbound")]
    public async Task<ActionResult<ApiResponse>> Outbound(ulong id)
    {
        try
        {
            var success = await _stockService.OutboundAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("出库成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "库存不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取空闲货位
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>空闲货位列表</returns>
    [HttpGet("empty/{laneId}")]
    public async Task<ActionResult<ApiResponse<List<Stock>>>> GetEmptyStocks(string laneCode)
    {
        try
        {
            var stocks = await _stockService.GetEmptyStocksAsync(laneCode);
            return Ok(ApiResponse<List<Stock>>.Success(stocks));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<Stock>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取有货货位
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>有货货位列表</returns>
    [HttpGet("occupied/{laneId}")]
    public async Task<ActionResult<ApiResponse<List<Stock>>>> GetOccupiedStocks(string laneCode)
    {
        try
        {
            var stocks = await _stockService.GetOccupiedStocksAsync(laneCode);
            return Ok(ApiResponse<List<Stock>>.Success(stocks));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<Stock>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取库存统计信息
    /// </summary>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, int>>>> GetStockStatistics(string? laneCode = null)
    {
        try
        {
            var statistics = await _stockService.GetStockStatisticsAsync(laneCode);
            return Ok(ApiResponse<Dictionary<string, int>>.Success(statistics));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Dictionary<string, int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 搜索库存
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Stock>>>> SearchStocks(
        string keyword, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _stockService.SearchStocksAsync(keyword, pageIndex, pageSize);

            var response = new PagedResponse<Stock>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Stock>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Stock>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据货物编码查找库存
    /// </summary>
    /// <param name="goodsCode">货物编码</param>
    /// <returns>库存列表</returns>
    [HttpGet("goods/{goodsCode}")]
    public async Task<ActionResult<ApiResponse<List<Stock>>>> FindByGoodsCode(string goodsCode)
    {
        try
        {
            var stocks = await _stockService.FindByGoodsCodeAsync(goodsCode);
            return Ok(ApiResponse<List<Stock>>.Success(stocks));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<Stock>>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 入库请求DTO
/// </summary>
public class InboundRequest
{
    public string GoodsCode { get; set; } = string.Empty;
}
