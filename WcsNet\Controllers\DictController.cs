using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;

namespace WcsNet.Controllers;

/// <summary>
/// 字典管理控制器
/// </summary>
[ApiController]
[Route("api/sys")]
public class DictController : ControllerBase
{
    private readonly DictService _dictService;

    public DictController(DictService dictService)
    {
        _dictService = dictService;
    }

    /// <summary>
    /// 获取字典值列表
    /// </summary>
    /// <param name="dictId">字典ID</param>
    /// <returns>字典值列表</returns>
    [HttpGet("dictvalues/{dictId}")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetDictValues(int dictId)
    {
        try
        {
            // 从数据库获取字典值数据
            var dictValues = await _dictService.GetDictValuesAsync((ulong)dictId);

            // 转换为前端需要的格式
            var result = dictValues.Select(dv => new
            {
                Id = dv.Id,
                Label = dv.DictLabel,
                Value = dv.DictVal,
                DictId = dv.DictId,
                Sort = dv.SortNumber,
                Status = dv.Status
            }).ToList<object>();

            return Ok(ApiResponse<List<object>>.Success(result));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取字典列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="keyword">搜索关键词</param>
    /// <returns>字典列表</returns>
    [HttpGet("dicts")]
    public async Task<ActionResult<ApiResponse<PagedResponse<object>>>> GetDicts(
        int page = 1,
        int pageSize = 10,
        string? keyword = null)
    {
        try
        {
            // 从数据库获取字典数据
            var (items, total) = await _dictService.SearchAsync(keyword, page, pageSize);

            // 转换为前端需要的格式
            var dictList = items.Select(d => new
            {
                Id = d.Id,
                DictName = d.DictName,
                DictCode = d.DictCode,
                Status = d.Status,
                Remark = d.Remark
            }).ToList<object>();

            var response = new PagedResponse<object>
            {
                Items = dictList,
                Total = total,
                PageIndex = page,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典
    /// </summary>
    /// <param name="request">字典信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("dicts")]
    public async Task<ActionResult<ApiResponse<string>>> CreateDict([FromBody] CreateDictRequest request)
    {
        try
        {
            // 检查字典编码是否已存在
            if (await _dictService.IsCodeExistAsync(request.DictCode))
            {
                return Ok(ApiResponse<string>.Error(400, "字典编码已存在"));
            }

            // 创建字典
            var dict = new Dict
            {
                DictCode = request.DictCode,
                DictName = request.DictName,
                Status = request.Status,
                Remark = request.Remark ?? string.Empty
            };

            await _dictService.CreateAsync(dict);
            return Ok(ApiResponse<string>.Success("字典创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <param name="request">字典信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("dicts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDict(int id, [FromBody] UpdateDictRequest request)
    {
        try
        {
            // 检查字典编码是否已存在（排除当前记录）
            if (!string.IsNullOrEmpty(request.DictCode) &&
                await _dictService.IsCodeExistAsync(request.DictCode, (ulong)id))
            {
                return Ok(ApiResponse<string>.Error(400, "字典编码已存在"));
            }

            // 构建更新字段
            var updates = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(request.DictName))
                updates["DictName"] = request.DictName;
            if (!string.IsNullOrEmpty(request.DictCode))
                updates["DictCode"] = request.DictCode;
            if (request.Status.HasValue)
                updates["Status"] = request.Status.Value;
            if (request.Remark != null)
                updates["Remark"] = request.Remark;

            var success = await _dictService.UpdateAsync((ulong)id, updates);

            if (success)
            {
                return Ok(ApiResponse<string>.Success("字典更新成功"));
            }
            else
            {
                return Ok(ApiResponse<string>.Error(404, "字典不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dicts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDict(int id)
    {
        try
        {
            var success = await _dictService.DeleteAsync((ulong)id);

            if (success)
            {
                return Ok(ApiResponse<string>.Success("字典删除成功"));
            }
            else
            {
                return Ok(ApiResponse<string>.Error(404, "字典不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典值
    /// </summary>
    /// <param name="request">字典值信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("dictvalues")]
    public async Task<ActionResult<ApiResponse<string>>> CreateDictValue([FromBody] CreateDictValueRequest request)
    {
        try
        {
            // 创建字典值
            var dictValue = new DictValue
            {
                DictId = (ulong)request.DictId,
                DictLabel = request.Label,
                DictVal = request.Value,
                SortNumber = request.Sort,
                Status = request.Status
            };

            await _dictService.CreateDictValueAsync(dictValue);
            return Ok(ApiResponse<string>.Success("字典值创建成功"));
        }
        catch (InvalidOperationException ex)
        {
            return Ok(ApiResponse<string>.Error(400, ex.Message));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <param name="request">字典值信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("dictvalues/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDictValue(int id, [FromBody] UpdateDictValueRequest request)
    {
        try
        {
            // 构建更新字段
            var updates = new Dictionary<string, object>();
            if (!string.IsNullOrEmpty(request.Label))
                updates["DictLabel"] = request.Label;
            if (!string.IsNullOrEmpty(request.Value))
                updates["DictVal"] = request.Value;
            if (request.Sort.HasValue)
                updates["SortNumber"] = request.Sort.Value;
            if (request.Status.HasValue)
                updates["Status"] = request.Status.Value;

            var success = await _dictService.UpdateDictValueAsync((ulong)id, updates);

            if (success)
            {
                return Ok(ApiResponse<string>.Success("字典值更新成功"));
            }
            else
            {
                return Ok(ApiResponse<string>.Error(404, "字典值不存在"));
            }
        }
        catch (InvalidOperationException ex)
        {
            return Ok(ApiResponse<string>.Error(400, ex.Message));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dictvalues/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDictValue(int id)
    {
        try
        {
            var success = await _dictService.DeleteDictValueAsync((ulong)id);

            if (success)
            {
                return Ok(ApiResponse<string>.Success("字典值删除成功"));
            }
            else
            {
                return Ok(ApiResponse<string>.Error(404, "字典值不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 创建字典请求
/// </summary>
public class CreateDictRequest
{
    public string DictName { get; set; } = string.Empty;
    public string DictCode { get; set; } = string.Empty;
    public sbyte Status { get; set; } = 1;
    public string? Remark { get; set; }
}

/// <summary>
/// 更新字典请求
/// </summary>
public class UpdateDictRequest
{
    public string? DictName { get; set; }
    public string? DictCode { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// 创建字典值请求
/// </summary>
public class CreateDictValueRequest
{
    public int DictId { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public int Sort { get; set; } = 0;
    public sbyte Status { get; set; } = 1;
    public string? Remark { get; set; }
}

/// <summary>
/// 更新字典值请求
/// </summary>
public class UpdateDictValueRequest
{
    public string? Label { get; set; }
    public string? Value { get; set; }
    public int? Sort { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}
