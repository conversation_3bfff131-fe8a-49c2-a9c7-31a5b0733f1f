using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 字典管理控制器
/// </summary>
[ApiController]
[Route("api/sys")]
public class DictController : ControllerBase
{
    private readonly WcsDbContext _context;

    public DictController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取字典值列表
    /// </summary>
    /// <param name="dictId">字典ID</param>
    /// <returns>字典值列表</returns>
    [HttpGet("dictvalues/{dictId}")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetDictValues(int dictId)
    {
        try
        {
            // 模拟字典值数据，实际应该从数据库获取
            var dictValues = new List<object>();

            // 根据不同的字典ID返回不同的数据
            switch (dictId)
            {
                case 1: // 用户状态
                    dictValues.AddRange(new[]
                    {
                        new { Id = 1, Label = "启用", Value = "1", DictId = 1 },
                        new { Id = 2, Label = "停用", Value = "0", DictId = 1 }
                    });
                    break;
                case 2: // 角色类型
                    dictValues.AddRange(new[]
                    {
                        new { Id = 3, Label = "系统管理员", Value = "1", DictId = 2 },
                        new { Id = 4, Label = "系统普通角色", Value = "2", DictId = 2 },
                        new { Id = 5, Label = "普通角色", Value = "3", DictId = 2 }
                    });
                    break;
                case 3: // 菜单类型
                    dictValues.AddRange(new[]
                    {
                        new { Id = 6, Label = "菜单", Value = "0", DictId = 3 },
                        new { Id = 7, Label = "iframe", Value = "1", DictId = 3 },
                        new { Id = 8, Label = "外链", Value = "2", DictId = 3 },
                        new { Id = 9, Label = "按钮", Value = "3", DictId = 3 }
                    });
                    break;
                default:
                    break;
            }

            return Ok(ApiResponse<List<object>>.Success(dictValues));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取字典列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="keyword">搜索关键词</param>
    /// <returns>字典列表</returns>
    [HttpGet("dicts")]
    public async Task<ActionResult<ApiResponse<PagedResponse<object>>>> GetDicts(
        int page = 1, 
        int pageSize = 10, 
        string? keyword = null)
    {
        try
        {
            // 模拟字典数据
            var dicts = new List<object>
            {
                new { Id = 1, DictName = "用户状态", DictCode = "user_status", Status = 1, Remark = "用户状态字典" },
                new { Id = 2, DictName = "角色类型", DictCode = "role_type", Status = 1, Remark = "角色类型字典" },
                new { Id = 3, DictName = "菜单类型", DictCode = "menu_type", Status = 1, Remark = "菜单类型字典" }
            };

            // 搜索过滤
            if (!string.IsNullOrEmpty(keyword))
            {
                dicts = dicts.Where(d => 
                {
                    dynamic dict = d;
                    return dict.DictName.Contains(keyword) || dict.DictCode.Contains(keyword);
                }).ToList();
            }

            var total = dicts.Count;
            var pagedDicts = dicts.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            var response = new PagedResponse<object>
            {
                Items = pagedDicts,
                Total = total,
                PageIndex = page,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典
    /// </summary>
    /// <param name="request">字典信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("dicts")]
    public async Task<ActionResult<ApiResponse<string>>> CreateDict([FromBody] CreateDictRequest request)
    {
        try
        {
            // 模拟创建字典
            return Ok(ApiResponse<string>.Success("字典创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <param name="request">字典信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("dicts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDict(int id, [FromBody] UpdateDictRequest request)
    {
        try
        {
            // 模拟更新字典
            return Ok(ApiResponse<string>.Success("字典更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dicts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDict(int id)
    {
        try
        {
            // 模拟删除字典
            return Ok(ApiResponse<string>.Success("字典删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建字典值
    /// </summary>
    /// <param name="request">字典值信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("dictvalues")]
    public async Task<ActionResult<ApiResponse<string>>> CreateDictValue([FromBody] CreateDictValueRequest request)
    {
        try
        {
            // 模拟创建字典值
            return Ok(ApiResponse<string>.Success("字典值创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <param name="request">字典值信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("dictvalues/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDictValue(int id, [FromBody] UpdateDictValueRequest request)
    {
        try
        {
            // 模拟更新字典值
            return Ok(ApiResponse<string>.Success("字典值更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("dictvalues/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDictValue(int id)
    {
        try
        {
            // 模拟删除字典值
            return Ok(ApiResponse<string>.Success("字典值删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 创建字典请求
/// </summary>
public class CreateDictRequest
{
    public string DictName { get; set; } = string.Empty;
    public string DictCode { get; set; } = string.Empty;
    public sbyte Status { get; set; } = 1;
    public string? Remark { get; set; }
}

/// <summary>
/// 更新字典请求
/// </summary>
public class UpdateDictRequest
{
    public string? DictName { get; set; }
    public string? DictCode { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// 创建字典值请求
/// </summary>
public class CreateDictValueRequest
{
    public int DictId { get; set; }
    public string Label { get; set; } = string.Empty;
    public string Value { get; set; } = string.Empty;
    public int Sort { get; set; } = 0;
    public sbyte Status { get; set; } = 1;
    public string? Remark { get; set; }
}

/// <summary>
/// 更新字典值请求
/// </summary>
public class UpdateDictValueRequest
{
    public string? Label { get; set; }
    public string? Value { get; set; }
    public int? Sort { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}
