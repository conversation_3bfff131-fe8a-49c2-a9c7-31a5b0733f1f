using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_lane")]
public class Lane : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("lane_code")]
    public string LaneCode { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("lane_name")]
    public string LaneName { get; set; } = string.Empty;

    [Column("lane_type")]
    public int LaneType { get; set; } = 0;

    [Column("sort_number")]
    public int SortNumber { get; set; } = 0;

    public sbyte Status { get; set; } = 1;

    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;
}
