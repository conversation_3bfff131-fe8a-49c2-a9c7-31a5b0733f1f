﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_lane")]
public class Lane : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("lane_name")]
    public string LaneName { get; set; } = string.Empty;

    [Required]
    [StringLength(24)]
    [Column("lane_code")]
    public string LaneCode { get; set; } = string.Empty;

    [Column("repo_id")]
    public ulong RepoId { get; set; } = 0;
}
