using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_lane")]
public class Lane : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("lane_name")]
    public string LaneName { get; set; } = string.Empty;

    [Required]
    [StringLength(24)]
    [Column("lane_code")]
    public string LaneCode { get; set; } = string.Empty;

    [Column("repo_id")]
    public ulong RepoId { get; set; } = 0; // 所属仓库id

    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
