﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_login")]
public class LoginLog : BaseModel
{
    [Required]
    [StringLength(64)]
    public string Username { get; set; } = string.Empty;

    [Required]
    [StringLength(15)]
    public string Ip { get; set; } = string.Empty;

    [StringLength(64)]
    public string Location { get; set; } = string.Empty;

    [StringLength(24)]
    public string Os { get; set; } = string.Empty;

    [StringLength(32)]
    public string Browser { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1成功 2失败

    [StringLength(128)]
    public string Msg { get; set; } = string.Empty;
}
