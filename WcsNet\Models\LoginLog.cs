﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("log_login")]
public class LoginLog : BaseModel
{
    [Required]
    [StringLength(64)]
    public string Username { get; set; } = string.Empty;

    [Required]
    [StringLength(15)]
    public string Ip { get; set; } = string.Empty;

    [StringLength(24)]
    public string Os { get; set; } = string.Empty;

    [StringLength(32)]
    public string Browser { get; set; } = string.Empty;

    public sbyte Status { get; set; } = 1; // 1成功 2失败

    // 添加兼容属性
    [NotMapped]
    public DateTime LoginTime => CreatedAt ?? DateTime.Now;

    [NotMapped]
    public string Location => "";

    [NotMapped]
    public string Msg => "";

    [NotMapped]
    public string LoginIp => Ip;

    [NotMapped]
    public string LoginLocation => "";

    [NotMapped]
    public string Message => "";
}
