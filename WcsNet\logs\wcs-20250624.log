2025-06-24 08:44:34.039 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:34.047 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:34.049 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:34.056 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.064 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:34.345 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:34.374 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 308.1322ms
2025-06-24 08:44:34.375 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.379 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 329ms - 响应大小: 371bytes
2025-06-24 08:44:34.394 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 354.808ms
2025-06-24 08:44:35.754 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:35.758 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:35.758 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:35.760 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.761 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:35.968 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:35.970 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 206.6449ms
2025-06-24 08:44:35.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.972 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 213ms - 响应大小: 371bytes
2025-06-24 08:44:35.978 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 223.7874ms
2025-06-24 08:44:37.541 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:37.543 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:37.544 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:37.545 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.546 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:37.714 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:37.715 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 167.1449ms
2025-06-24 08:44:37.716 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.717 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 173ms - 响应大小: 371bytes
2025-06-24 08:44:37.719 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 177.6483ms
2025-06-24 08:44:38.005 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:38.036 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:38.042 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:38.050 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.052 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:38.154 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:38.184 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 122.3528ms
2025-06-24 08:44:38.192 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.250 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 208ms - 响应大小: 371bytes
2025-06-24 08:44:38.313 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 307.7236ms
2025-06-24 08:44:48.283 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:48.308 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:48.360 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:48.384 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:48.392 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:48.399 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:48.397 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.469 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:48.460 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.479 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:48.633 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:48.646 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 172.9117ms
2025-06-24 08:44:48.658 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.660 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 275ms - 响应大小: 371bytes
2025-06-24 08:44:48.721 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 438.2998ms
2025-06-24 08:44:48.759 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:48.881 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 238.1915ms
2025-06-24 08:44:48.884 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.892 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 492ms - 响应大小: 371bytes
2025-06-24 08:44:48.931 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 571.1659ms
2025-06-24 08:44:57.234 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:57.245 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:57.249 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:57.258 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:57.265 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:57.560 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:57.564 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 296.9463ms
2025-06-24 08:44:57.565 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:57.566 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 316ms - 响应大小: 371bytes
2025-06-24 08:44:57.569 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 335.3636ms
2025-06-24 08:44:58.277 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:58.287 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:58.312 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:58.314 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:58.316 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:58.500 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:58.503 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 182.4041ms
2025-06-24 08:44:58.504 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:58.505 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 192ms - 响应大小: 371bytes
2025-06-24 08:44:58.508 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 230.8325ms
2025-06-24 08:45:08.480 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:08.493 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:08.494 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:08.496 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:08.498 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:08.721 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:08.724 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 203.8621ms
2025-06-24 08:45:08.725 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:08.726 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 231ms - 响应大小: 371bytes
2025-06-24 08:45:08.728 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 247.3507ms
2025-06-24 08:45:20.797 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:20.799 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:20.800 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:20.802 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:20.804 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:21.268 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:21.308 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 500.2828ms
2025-06-24 08:45:21.329 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:21.393 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 593ms - 响应大小: 371bytes
2025-06-24 08:45:21.489 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 692.4265ms
2025-06-24 08:45:28.258 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:28.260 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:28.261 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:28.263 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:28.264 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:28.332 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:28.333 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 66.5736ms
2025-06-24 08:45:28.334 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:28.335 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 73ms - 响应大小: 371bytes
2025-06-24 08:45:28.337 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 79.2995ms
2025-06-24 08:45:41.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:45:41.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:41.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:41.379 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:45:41.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:45:41.384 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.277ms
2025-06-24 08:45:41.386 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:41.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1599bytes
2025-06-24 08:45:41.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.5529ms
2025-06-24 08:45:50.033 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:45:50.039 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:50.045 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:50.045 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:45:50.047 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:45:50.048 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.9301ms
2025-06-24 08:45:50.048 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:50.049 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1598bytes
2025-06-24 08:45:50.050 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8554ms
2025-06-24 08:46:06.752 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 08:46:06.763 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:46:06.764 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:06.767 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:46:06.771 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:46:06.840 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:46:07.114 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:46:07.151 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:46:07.230 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:46:07.244 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:07.246 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 474.1166ms
2025-06-24 08:46:07.247 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:46:07.247 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 483ms - 响应大小: 370bytes
2025-06-24 08:46:07.250 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 498.1997ms
2025-06-24 08:46:08.785 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:46:08.787 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:08.789 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:08.796 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:46:08.982 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:09.007 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:09.012 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 214.3499ms
2025-06-24 08:46:09.013 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:09.013 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 226ms - 响应大小: 1678bytes
2025-06-24 08:46:09.014 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 229.525ms
2025-06-24 08:46:10.964 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:10.964 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:10.966 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:10.967 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:10.969 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:10.970 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:10.971 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:10.972 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:10.974 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:10.975 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:10.976 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 2.7987ms
2025-06-24 08:46:10.977 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.5558ms
2025-06-24 08:46:10.978 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:10.979 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:10.980 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 847bytes
2025-06-24 08:46:10.981 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1598bytes
2025-06-24 08:46:10.982 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 18.4978ms
2025-06-24 08:46:10.984 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.661ms
2025-06-24 08:46:11.030 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:11.031 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.032 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.033 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.033 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.034 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 0.7417ms
2025-06-24 08:46:11.035 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.035 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 3ms - 响应大小: 620bytes
2025-06-24 08:46:11.036 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 6.4169ms
2025-06-24 08:46:11.279 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:11.279 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:11.281 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.282 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.283 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.283 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:11.284 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.285 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.285 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.286 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.286 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 1.3375ms
2025-06-24 08:46:11.287 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.2073ms
2025-06-24 08:46:11.288 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.288 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:11.289 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 8ms - 响应大小: 1144bytes
2025-06-24 08:46:11.289 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 7ms - 响应大小: 617bytes
2025-06-24 08:46:11.291 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 11.6549ms
2025-06-24 08:46:11.292 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 12.6637ms
2025-06-24 08:46:12.865 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:12.868 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:12.869 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:12.870 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:12.871 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:12.872 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.5908ms
2025-06-24 08:46:12.873 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:12.873 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 5ms - 响应大小: 613bytes
2025-06-24 08:46:12.876 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 10.8107ms
2025-06-24 08:46:20.944 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:20.947 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:20.949 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:20.949 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:20.950 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:20.951 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3609ms
2025-06-24 08:46:20.952 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:20.952 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 5ms - 响应大小: 1598bytes
2025-06-24 08:46:20.953 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 9.0356ms
2025-06-24 08:46:28.222 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:46:28.224 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.227 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:28.229 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:46:28.341 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:28.342 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:28.343 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 112.6123ms
2025-06-24 08:46:28.344 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:28.344 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 119ms - 响应大小: 1678bytes
2025-06-24 08:46:28.345 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 123.8534ms
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:28.444 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:28.444 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:28.445 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.446 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.447 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.448 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.449 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.450 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.451 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:28.452 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.453 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.456 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.457 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.459 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:28.460 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.460 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.461 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.463 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.464 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.465 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.467 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.468 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.469 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.2116ms
2025-06-24 08:46:28.472 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 7.761ms
2025-06-24 08:46:28.473 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.8125ms
2025-06-24 08:46:28.475 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 8.3263ms
2025-06-24 08:46:28.476 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 8.1257ms
2025-06-24 08:46:28.477 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.479 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:28.480 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.482 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.483 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.483 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 38ms - 响应大小: 845bytes
2025-06-24 08:46:28.484 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 38ms - 响应大小: 1596bytes
2025-06-24 08:46:28.485 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 38ms - 响应大小: 618bytes
2025-06-24 08:46:28.486 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 37ms - 响应大小: 1146bytes
2025-06-24 08:46:28.486 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 37ms - 响应大小: 613bytes
2025-06-24 08:46:28.488 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 44.5788ms
2025-06-24 08:46:28.490 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 46.4915ms
2025-06-24 08:46:28.491 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 47.9165ms
2025-06-24 08:46:28.493 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 49.2675ms
2025-06-24 08:46:28.494 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 50.7043ms
2025-06-24 08:46:28.751 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:28.753 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.754 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.754 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.756 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.756 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.0547ms
2025-06-24 08:46:28.757 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.758 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 5ms - 响应大小: 615bytes
2025-06-24 08:46:28.759 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 7.5571ms
2025-06-24 08:46:34.194 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 08:46:34.195 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.196 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:34.199 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:34.337 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:34.340 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:34.347 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 147.5482ms
2025-06-24 08:46:34.348 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:34.348 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 152ms - 响应大小: 80bytes
2025-06-24 08:46:34.349 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 155.2673ms
2025-06-24 08:46:34.359 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:34.359 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 08:46:34.360 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.361 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.362 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:34.363 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:34.365 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:34.365 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 08:46:34.526 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 08:46:34.529 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 08:46:34.530 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 08:46:34.619 +08:00 [INF] Executed DbCommand (46ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:34.626 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:34.636 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 269.6241ms
2025-06-24 08:46:34.637 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:34.638 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 277ms - 响应大小: 262bytes
2025-06-24 08:46:34.639 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 279.9353ms
2025-06-24 08:46:34.658 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 08:46:34.660 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:34.662 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 295.4425ms
2025-06-24 08:46:34.662 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:34.663 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 301ms - 响应大小: 92bytes
2025-06-24 08:46:34.664 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 304.6412ms
2025-06-24 08:46:36.377 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:36.377 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menulist - null null
2025-06-24 08:46:36.378 +08:00 [INF] 请求开始: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:36.379 +08:00 [INF] 请求开始: GET /api/sys/menulist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:36.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:36.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:36.383 +08:00 [INF] Route matched with {action = "GetMenuList", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetMenuList() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:36.383 +08:00 [INF] Route matched with {action = "GetRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetRoles(Int32, Int32, System.String) on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:36.514 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`title` AS `Title`, `s`.`parent_id` AS `ParentId`, `s`.`perm_type` AS `PermType`, `s`.`icon` AS `Icon`, `s`.`rank` AS `Rank`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:36.514 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_role` AS `s`
2025-06-24 08:46:36.537 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`, `s`.`role_type` AS `RoleType`, `s`.`sort_number` AS `SortNumber`, `s`.`status` AS `Status`, `s`.`remark` AS `Remark`, `s`.`perms` AS `Perms`
FROM `sys_role` AS `s`
ORDER BY `s`.`sort_number`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:36.539 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:36.544 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetRoles (WcsNet) in 158.7858ms
2025-06-24 08:46:36.545 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:36.545 +08:00 [INF] 请求完成: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 167ms - 响应大小: 258bytes
2025-06-24 08:46:36.546 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 169.4487ms
2025-06-24 08:46:36.556 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:36.564 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenuList (WcsNet) in 179.9836ms
2025-06-24 08:46:36.565 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:36.566 +08:00 [INF] 请求完成: GET /api/sys/menulist - 状态码: 200 - 耗时: 186ms - 响应大小: 684bytes
2025-06-24 08:46:36.567 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menulist - 200 null application/json; charset=utf-8 189.583ms
2025-06-24 08:46:38.036 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - null null
2025-06-24 08:46:38.038 +08:00 [INF] 请求开始: GET /api/sys/menus?title= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:38.040 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:38.042 +08:00 [INF] Route matched with {action = "GetMenus", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetMenus() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:38.220 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:38.222 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:38.223 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenus (WcsNet) in 179.7268ms
2025-06-24 08:46:38.224 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:38.224 +08:00 [INF] 请求完成: GET /api/sys/menus?title= - 状态码: 200 - 耗时: 186ms - 响应大小: 1688bytes
2025-06-24 08:46:38.226 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - 200 null application/json; charset=utf-8 189.7656ms
2025-06-24 08:46:39.156 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - null null
2025-06-24 08:46:39.158 +08:00 [INF] 请求开始: GET /api/sys/depts?name=&status= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:39.159 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:39.162 +08:00 [INF] Route matched with {action = "GetDepts", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDepts() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:39.424 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:39.444 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:39.446 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDepts (WcsNet) in 283.5772ms
2025-06-24 08:46:39.447 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:39.447 +08:00 [INF] 请求完成: GET /api/sys/depts?name=&status= - 状态码: 200 - 耗时: 289ms - 响应大小: 169bytes
2025-06-24 08:46:39.448 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - 200 null application/json; charset=utf-8 292.5596ms
2025-06-24 08:46:40.594 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:40.594 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:40.595 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.596 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.597 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.598 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:40.599 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.599 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:40.600 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.601 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.602 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 2.2741ms
2025-06-24 08:46:40.603 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.2159ms
2025-06-24 08:46:40.605 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.606 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:40.606 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 843bytes
2025-06-24 08:46:40.607 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1599bytes
2025-06-24 08:46:40.608 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 14.374ms
2025-06-24 08:46:40.609 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.7443ms
2025-06-24 08:46:40.916 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:40.917 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:40.918 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:40.919 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:40.925 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.928 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.930 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.931 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.932 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.932 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.933 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.934 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.936 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.937 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.937 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.938 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.939 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.940 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.5231ms
2025-06-24 08:46:40.941 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.6173ms
2025-06-24 08:46:40.941 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 3.1524ms
2025-06-24 08:46:40.942 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 2.584ms
2025-06-24 08:46:40.942 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.943 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.943 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.944 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.944 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 19ms - 响应大小: 620bytes
2025-06-24 08:46:40.945 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 16ms - 响应大小: 1144bytes
2025-06-24 08:46:40.945 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 15ms - 响应大小: 611bytes
2025-06-24 08:46:40.946 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 14ms - 响应大小: 616bytes
2025-06-24 08:46:40.946 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 30.0463ms
2025-06-24 08:46:40.947 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 29.8478ms
2025-06-24 08:46:40.948 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 30.1398ms
2025-06-24 08:46:40.949 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 30.4987ms
2025-06-24 08:46:46.483 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 08:46:46.484 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:46.485 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:46.486 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:46.711 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:46.713 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:46.714 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 227.0864ms
2025-06-24 08:46:46.714 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:46.715 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 230ms - 响应大小: 80bytes
2025-06-24 08:46:46.715 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 232.4667ms
2025-06-24 08:46:46.746 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:46.747 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:46.748 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:46.748 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 08:46:47.047 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 08:46:47.049 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.051 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:47.052 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:47.082 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 08:46:47.170 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:47.171 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:47.172 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 423.1742ms
2025-06-24 08:46:47.173 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:47.173 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 426ms - 响应大小: 262bytes
2025-06-24 08:46:47.175 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 428.6ms
2025-06-24 08:46:47.263 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 08:46:47.264 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:47.264 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 211.5275ms
2025-06-24 08:46:47.266 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:47.267 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 217ms - 响应大小: 92bytes
2025-06-24 08:46:47.268 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 220.7031ms
2025-06-24 08:46:47.390 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:47.391 +08:00 [INF] 请求开始: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:47.392 +08:00 [INF] Route matched with {action = "GetRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetRoles(Int32, Int32, System.String) on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:47.616 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_role` AS `s`
2025-06-24 08:46:47.671 +08:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`, `s`.`role_type` AS `RoleType`, `s`.`sort_number` AS `SortNumber`, `s`.`status` AS `Status`, `s`.`remark` AS `Remark`, `s`.`perms` AS `Perms`
FROM `sys_role` AS `s`
ORDER BY `s`.`sort_number`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:47.673 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:47.677 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetRoles (WcsNet) in 283.5452ms
2025-06-24 08:46:47.680 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:47.682 +08:00 [INF] 请求完成: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 290ms - 响应大小: 258bytes
2025-06-24 08:46:47.684 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 294.4923ms
2025-06-24 08:46:47.693 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menulist - null null
2025-06-24 08:46:47.695 +08:00 [INF] 请求开始: GET /api/sys/menulist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.697 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:47.698 +08:00 [INF] Route matched with {action = "GetMenuList", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetMenuList() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:47.895 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - null null
2025-06-24 08:46:47.897 +08:00 [INF] 请求开始: GET /api/sys/menus?title= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.899 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:47.899 +08:00 [INF] Route matched with {action = "GetMenus", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetMenus() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:48.045 +08:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`title` AS `Title`, `s`.`parent_id` AS `ParentId`, `s`.`perm_type` AS `PermType`, `s`.`icon` AS `Icon`, `s`.`rank` AS `Rank`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:48.048 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:48.049 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenuList (WcsNet) in 350.8985ms
2025-06-24 08:46:48.050 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:48.051 +08:00 [INF] 请求完成: GET /api/sys/menulist - 状态码: 200 - 耗时: 356ms - 响应大小: 684bytes
2025-06-24 08:46:48.053 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menulist - 200 null application/json; charset=utf-8 359.2649ms
2025-06-24 08:46:48.171 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:48.174 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:48.178 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenus (WcsNet) in 277.9193ms
2025-06-24 08:46:48.181 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:48.183 +08:00 [INF] 请求完成: GET /api/sys/menus?title= - 状态码: 200 - 耗时: 285ms - 响应大小: 1688bytes
2025-06-24 08:46:48.187 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - 200 null application/json; charset=utf-8 291.7004ms
2025-06-24 08:46:49.702 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - null null
2025-06-24 08:46:49.703 +08:00 [INF] 请求开始: GET /api/sys/depts?name=&status= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:49.704 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:49.705 +08:00 [INF] Route matched with {action = "GetDepts", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDepts() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:50.096 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:50.097 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:50.098 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDepts (WcsNet) in 390.7754ms
2025-06-24 08:46:50.098 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:50.099 +08:00 [INF] 请求完成: GET /api/sys/depts?name=&status= - 状态码: 200 - 耗时: 395ms - 响应大小: 169bytes
2025-06-24 08:46:50.100 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - 200 null application/json; charset=utf-8 397.7352ms
2025-06-24 08:47:46.965 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:47:46.966 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:47:46.967 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:47:46.968 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:47:47.252 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:47:47.254 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:47:47.254 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 285.7793ms
2025-06-24 08:47:47.255 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:47:47.255 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 289ms - 响应大小: 1678bytes
2025-06-24 08:47:47.257 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 291.5629ms
2025-06-24 08:48:37.076 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:48:37.077 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:48:37.078 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:37.079 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.081 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.082 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.087 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:37.086 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.084 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.088 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:37.090 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.091 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.094 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.096 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.098 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.099 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.2583ms
2025-06-24 08:48:37.102 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:37.103 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 20ms - 响应大小: 1597bytes
2025-06-24 08:48:37.100 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 4.2934ms
2025-06-24 08:48:37.106 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.105 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 27.5184ms
2025-06-24 08:48:37.101 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.4746ms
2025-06-24 08:48:37.112 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.113 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 33ms - 响应大小: 848bytes
2025-06-24 08:48:37.107 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 26ms - 响应大小: 619bytes
2025-06-24 08:48:37.114 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 37.8794ms
2025-06-24 08:48:37.116 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 38.7337ms
2025-06-24 08:48:37.389 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:48:37.392 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.393 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.394 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.396 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.397 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 1.3658ms
2025-06-24 08:48:37.398 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.399 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 6ms - 响应大小: 1146bytes
2025-06-24 08:48:37.400 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 10.7164ms
2025-06-24 08:48:39.044 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:48:39.046 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:39.048 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.049 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:39.050 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:39.052 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.2538ms
2025-06-24 08:48:39.052 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.053 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 7ms - 响应大小: 618bytes
2025-06-24 08:48:39.055 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 10.637ms
2025-06-24 08:48:39.076 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:48:39.078 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:39.079 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.080 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:39.082 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:39.083 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.1801ms
2025-06-24 08:48:39.084 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.084 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 6ms - 响应大小: 616bytes
2025-06-24 08:48:39.086 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 9.9668ms
2025-06-24 08:48:47.505 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:47.508 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:47.510 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:47.511 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:47.513 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:47.514 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6549ms
2025-06-24 08:48:47.516 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:47.516 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1598bytes
2025-06-24 08:48:47.519 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0511ms
2025-06-24 08:48:57.182 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:57.183 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:57.184 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:57.185 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:57.186 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:57.186 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7848ms
2025-06-24 08:48:57.187 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:57.188 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:48:57.189 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 7.1545ms
2025-06-24 08:49:07.265 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:07.267 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:07.268 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:07.268 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:07.269 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:07.270 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7595ms
2025-06-24 08:49:07.270 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:07.271 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:49:07.272 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.7262ms
2025-06-24 08:49:17.182 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:17.183 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:17.184 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:17.185 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:17.186 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:17.186 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6696ms
2025-06-24 08:49:17.187 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:17.187 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:49:17.188 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.4589ms
2025-06-24 08:49:27.493 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:27.495 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:27.496 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:27.496 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:27.497 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:27.498 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7355ms
2025-06-24 08:49:27.498 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:27.498 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1597bytes
2025-06-24 08:49:27.499 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.0691ms
2025-06-24 08:49:37.197 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:37.215 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:37.216 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:37.217 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:37.220 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:37.222 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6912ms
2025-06-24 08:49:37.223 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:37.225 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1599bytes
2025-06-24 08:49:37.226 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 29.3963ms
2025-06-24 08:49:47.495 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:47.497 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:47.498 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:47.498 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:47.499 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:47.499 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6224ms
2025-06-24 08:49:47.500 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:47.500 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1598bytes
2025-06-24 08:49:47.501 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.8744ms
2025-06-24 08:49:57.188 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:57.190 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:57.190 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:57.191 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:57.192 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:57.192 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6626ms
2025-06-24 08:49:57.193 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:57.194 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:49:57.195 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.7972ms
2025-06-24 08:50:07.511 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:07.517 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:07.528 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:07.531 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:07.538 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:07.542 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.2568ms
2025-06-24 08:50:07.544 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:07.547 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 29ms - 响应大小: 1598bytes
2025-06-24 08:50:07.550 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 39.5603ms
2025-06-24 08:50:16.963 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:16.965 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:16.965 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:16.966 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:16.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:16.967 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.8067ms
2025-06-24 08:50:16.968 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:16.969 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:50:16.970 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.5853ms
2025-06-24 08:50:27.275 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:27.277 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:27.278 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:27.279 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:27.280 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:27.282 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4351ms
2025-06-24 08:50:27.282 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:27.283 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1599bytes
2025-06-24 08:50:27.285 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.0949ms
2025-06-24 08:50:37.195 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:37.197 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:37.198 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:37.201 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:37.202 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:37.204 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3285ms
2025-06-24 08:50:37.205 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:37.206 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1597bytes
2025-06-24 08:50:37.207 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.2231ms
2025-06-24 08:50:47.507 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:47.508 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:47.509 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:47.509 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:47.510 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:47.511 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6181ms
2025-06-24 08:50:47.511 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:47.512 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1597bytes
2025-06-24 08:50:47.512 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.7014ms
2025-06-24 08:50:57.185 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:57.186 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:57.188 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:57.188 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:57.189 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:57.190 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7302ms
2025-06-24 08:50:57.190 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:57.191 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:50:57.191 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.2984ms
2025-06-24 08:51:07.492 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:07.493 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:07.494 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:07.495 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:07.496 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:07.496 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6188ms
2025-06-24 08:51:07.497 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:07.497 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1598bytes
2025-06-24 08:51:07.498 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.7081ms
2025-06-24 08:51:17.192 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:17.194 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:17.194 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:17.195 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:17.196 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:17.197 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0424ms
2025-06-24 08:51:17.197 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:17.198 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1596bytes
2025-06-24 08:51:17.199 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.2322ms
2025-06-24 08:51:27.484 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:27.486 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:27.487 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:27.487 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:27.488 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:27.489 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7387ms
2025-06-24 08:51:27.489 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:27.490 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:51:27.491 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.6544ms
2025-06-24 08:52:28.191 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:52:28.193 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:52:28.195 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:52:28.195 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:52:28.657 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:52:28.658 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:52:28.659 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 461.9914ms
2025-06-24 08:52:28.659 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:52:28.660 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 466ms - 响应大小: 1678bytes
2025-06-24 08:52:28.661 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 469.3652ms
2025-06-24 08:55:27.330 +08:00 [INF] 使用内存存储服务
2025-06-24 08:55:30.340 +08:00 [INF] Executed DbCommand (69ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-24 08:55:30.451 +08:00 [INF] 数据库和表创建成功
2025-06-24 08:55:30.737 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 08:55:31.068 +08:00 [INF] 数据库连接正常，当前有 1 个用户
2025-06-24 08:55:31.261 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 08:55:31.393 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 08:55:31.411 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 08:55:31.418 +08:00 [INF] Hosting environment: Development
2025-06-24 08:55:31.420 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 08:55:31.874 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/init/reset - application/x-www-form-urlencoded 0
2025-06-24 08:55:31.903 +08:00 [INF] 请求开始: POST /api/init/reset - IP: ::ffff:127.0.0.1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:55:31.911 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 08:55:31.913 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.InitController.ResetDatabase (WcsNet)'
2025-06-24 08:55:31.925 +08:00 [INF] Route matched with {action = "ResetDatabase", controller = "Init"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] ResetDatabase() on controller WcsNet.Controllers.InitController (WcsNet).
2025-06-24 08:55:32.134 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
2025-06-24 08:55:32.230 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`perms`, `s`.`remark`, `s`.`role_code`, `s`.`role_name`, `s`.`role_type`, `s`.`sort_number`, `s`.`status`
FROM `sys_role` AS `s`
2025-06-24 08:55:32.267 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
2025-06-24 08:55:32.316 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
2025-06-24 08:55:32.341 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
2025-06-24 08:55:32.383 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_code`, `s`.`dict_name`, `s`.`Remark`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict` AS `s`
2025-06-24 08:55:32.563 +08:00 [INF] Executed DbCommand (27ms) [Parameters=[@p0='?' (DbType = UInt64), @p1='?' (DbType = UInt64), @p2='?' (DbType = UInt64), @p3='?' (DbType = UInt64), @p4='?' (DbType = UInt64), @p5='?' (DbType = UInt64), @p6='?' (DbType = UInt64), @p7='?' (DbType = UInt64), @p8='?' (DbType = UInt64), @p9='?' (DbType = UInt64), @p10='?' (DbType = UInt32), @p11='?' (DbType = UInt32), @p12='?' (DbType = UInt32), @p13='?' (DbType = UInt32), @p14='?' (DbType = UInt32), @p15='?' (DbType = UInt32), @p16='?' (DbType = UInt32), @p17='?' (DbType = UInt64), @p18='?' (DbType = UInt64), @p19='?' (DbType = UInt64), @p20='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
DELETE FROM `sys_department`
WHERE `Id` = @p0;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p1;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p2;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p3;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p4;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p5;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p6;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p7;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p8;
SELECT ROW_COUNT();

DELETE FROM `sys_dict_value`
WHERE `Id` = @p9;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p10;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p11;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p12;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p13;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p14;
SELECT ROW_COUNT();

DELETE FROM `sys_permission`
WHERE `Id` = @p15;
SELECT ROW_COUNT();

DELETE FROM `sys_role`
WHERE `Id` = @p16;
SELECT ROW_COUNT();

DELETE FROM `sys_user`
WHERE `Id` = @p17;
SELECT ROW_COUNT();

DELETE FROM `sys_dict`
WHERE `Id` = @p18;
SELECT ROW_COUNT();

DELETE FROM `sys_dict`
WHERE `Id` = @p19;
SELECT ROW_COUNT();

DELETE FROM `sys_dict`
WHERE `Id` = @p20;
SELECT ROW_COUNT();
2025-06-24 08:55:32.629 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@p0='?' (DbType = UInt32), @p1='?' (Size = 1024), @p2='?' (Size = 128), @p3='?' (Size = 32), @p4='?' (Size = 32), @p5='?' (DbType = SByte), @p6='?' (DbType = Int32), @p7='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
INSERT INTO `sys_role` (`Id`, `perms`, `remark`, `role_code`, `role_name`, `role_type`, `sort_number`, `status`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7);
2025-06-24 08:55:32.654 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@p0='?' (DbType = UInt32), @p1='?' (Size = 128), @p2='?' (Size = 255), @p3='?' (Size = 128), @p4='?' (DbType = SByte), @p5='?' (DbType = SByte), @p6='?' (Size = 191), @p7='?' (DbType = SByte), @p8='?' (Size = 128), @p9='?' (DbType = SByte), @p10='?' (Size = 128), @p11='?' (DbType = UInt32), @p12='?' (Size = 128), @p13='?' (DbType = SByte), @p14='?' (DbType = Int32), @p15='?' (Size = 255), @p16='?' (Size = 255), @p17='?' (DbType = SByte), @p18='?' (DbType = SByte), @p19='?' (Size = 64), @p20='?' (DbType = UInt32), @p21='?' (Size = 128), @p22='?' (Size = 255), @p23='?' (Size = 128), @p24='?' (DbType = SByte), @p25='?' (DbType = SByte), @p26='?' (Size = 191), @p27='?' (DbType = SByte), @p28='?' (Size = 128), @p29='?' (DbType = SByte), @p30='?' (Size = 128), @p31='?' (DbType = UInt32), @p32='?' (Size = 128), @p33='?' (DbType = SByte), @p34='?' (DbType = Int32), @p35='?' (Size = 255), @p36='?' (Size = 255), @p37='?' (DbType = SByte), @p38='?' (DbType = SByte), @p39='?' (Size = 64), @p40='?' (DbType = UInt32), @p41='?' (Size = 128), @p42='?' (Size = 255), @p43='?' (Size = 128), @p44='?' (DbType = SByte), @p45='?' (DbType = SByte), @p46='?' (Size = 191), @p47='?' (DbType = SByte), @p48='?' (Size = 128), @p49='?' (DbType = SByte), @p50='?' (Size = 128), @p51='?' (DbType = UInt32), @p52='?' (Size = 128), @p53='?' (DbType = SByte), @p54='?' (DbType = Int32), @p55='?' (Size = 255), @p56='?' (Size = 255), @p57='?' (DbType = SByte), @p58='?' (DbType = SByte), @p59='?' (Size = 64), @p60='?' (DbType = UInt32), @p61='?' (Size = 128), @p62='?' (Size = 255), @p63='?' (Size = 128), @p64='?' (DbType = SByte), @p65='?' (DbType = SByte), @p66='?' (Size = 191), @p67='?' (DbType = SByte), @p68='?' (Size = 128), @p69='?' (DbType = SByte), @p70='?' (Size = 128), @p71='?' (DbType = UInt32), @p72='?' (Size = 128), @p73='?' (DbType = SByte), @p74='?' (DbType = Int32), @p75='?' (Size = 255), @p76='?' (Size = 255), @p77='?' (DbType = SByte), @p78='?' (DbType = SByte), @p79='?' (Size = 64), @p80='?' (DbType = UInt32), @p81='?' (Size = 128), @p82='?' (Size = 255), @p83='?' (Size = 128), @p84='?' (DbType = SByte), @p85='?' (DbType = SByte), @p86='?' (Size = 191), @p87='?' (DbType = SByte), @p88='?' (Size = 128), @p89='?' (DbType = SByte), @p90='?' (Size = 128), @p91='?' (DbType = UInt32), @p92='?' (Size = 128), @p93='?' (DbType = SByte), @p94='?' (DbType = Int32), @p95='?' (Size = 255), @p96='?' (Size = 255), @p97='?' (DbType = SByte), @p98='?' (DbType = SByte), @p99='?' (Size = 64), @p100='?' (DbType = UInt32), @p101='?' (Size = 128), @p102='?' (Size = 255), @p103='?' (Size = 128), @p104='?' (DbType = SByte), @p105='?' (DbType = SByte), @p106='?' (Size = 191), @p107='?' (DbType = SByte), @p108='?' (Size = 128), @p109='?' (DbType = SByte), @p110='?' (Size = 128), @p111='?' (DbType = UInt32), @p112='?' (Size = 128), @p113='?' (DbType = SByte), @p114='?' (DbType = Int32), @p115='?' (Size = 255), @p116='?' (Size = 255), @p117='?' (DbType = SByte), @p118='?' (DbType = SByte), @p119='?' (Size = 64), @p120='?' (DbType = UInt32), @p121='?' (Size = 128), @p122='?' (Size = 255), @p123='?' (Size = 128), @p124='?' (DbType = SByte), @p125='?' (DbType = SByte), @p126='?' (Size = 191), @p127='?' (DbType = SByte), @p128='?' (Size = 128), @p129='?' (DbType = SByte), @p130='?' (Size = 128), @p131='?' (DbType = UInt32), @p132='?' (Size = 128), @p133='?' (DbType = SByte), @p134='?' (DbType = Int32), @p135='?' (Size = 255), @p136='?' (Size = 255), @p137='?' (DbType = SByte), @p138='?' (DbType = SByte), @p139='?' (Size = 64), @p140='?' (DbType = UInt32), @p141='?' (Size = 128), @p142='?' (Size = 255), @p143='?' (Size = 128), @p144='?' (DbType = SByte), @p145='?' (DbType = SByte), @p146='?' (Size = 191), @p147='?' (DbType = SByte), @p148='?' (Size = 128), @p149='?' (DbType = SByte), @p150='?' (Size = 128), @p151='?' (DbType = UInt32), @p152='?' (Size = 128), @p153='?' (DbType = SByte), @p154='?' (DbType = Int32), @p155='?' (Size = 255), @p156='?' (Size = 255), @p157='?' (DbType = SByte), @p158='?' (DbType = SByte), @p159='?' (Size = 64), @p160='?' (DbType = UInt32), @p161='?' (Size = 128), @p162='?' (Size = 255), @p163='?' (Size = 128), @p164='?' (DbType = SByte), @p165='?' (DbType = SByte), @p166='?' (Size = 191), @p167='?' (DbType = SByte), @p168='?' (Size = 128), @p169='?' (DbType = SByte), @p170='?' (Size = 128), @p171='?' (DbType = UInt32), @p172='?' (Size = 128), @p173='?' (DbType = SByte), @p174='?' (DbType = Int32), @p175='?' (Size = 255), @p176='?' (Size = 255), @p177='?' (DbType = SByte), @p178='?' (DbType = SByte), @p179='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
INSERT INTO `sys_permission` (`Id`, `active_path`, `component`, `extra_icon`, `fixed_tag`, `frame_loading`, `frame_src`, `hide_tag`, `icon`, `keepalive`, `name`, `parent_id`, `perm_code`, `perm_type`, `rank`, `redirect`, `route_path`, `show_link`, `show_parent`, `title`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19),
(@p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39),
(@p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47, @p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55, @p56, @p57, @p58, @p59),
(@p60, @p61, @p62, @p63, @p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71, @p72, @p73, @p74, @p75, @p76, @p77, @p78, @p79),
(@p80, @p81, @p82, @p83, @p84, @p85, @p86, @p87, @p88, @p89, @p90, @p91, @p92, @p93, @p94, @p95, @p96, @p97, @p98, @p99),
(@p100, @p101, @p102, @p103, @p104, @p105, @p106, @p107, @p108, @p109, @p110, @p111, @p112, @p113, @p114, @p115, @p116, @p117, @p118, @p119),
(@p120, @p121, @p122, @p123, @p124, @p125, @p126, @p127, @p128, @p129, @p130, @p131, @p132, @p133, @p134, @p135, @p136, @p137, @p138, @p139),
(@p140, @p141, @p142, @p143, @p144, @p145, @p146, @p147, @p148, @p149, @p150, @p151, @p152, @p153, @p154, @p155, @p156, @p157, @p158, @p159),
(@p160, @p161, @p162, @p163, @p164, @p165, @p166, @p167, @p168, @p169, @p170, @p171, @p172, @p173, @p174, @p175, @p176, @p177, @p178, @p179);
2025-06-24 08:55:32.710 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[@p0='?' (DbType = UInt64), @p1='?' (Size = 255), @p2='?' (Size = 255), @p3='?' (DbType = DateTime), @p4='?' (DbType = Int64), @p5='?' (Size = 32), @p6='?' (Size = 64), @p7='?' (Size = 24), @p8='?' (DbType = UInt64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = DateTime), @p12='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
INSERT INTO `sys_department` (`Id`, `Ancestors`, `Brief`, `created_at`, `created_id`, `dept_code`, `dept_name`, `Leader`, `parent_id`, `sort_number`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12);
2025-06-24 08:55:32.769 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[@p0='?' (DbType = UInt64), @p1='?' (DbType = Int64), @p2='?' (Size = 64), @p3='?' (Size = 64), @p4='?' (Size = 255), @p5='?' (DbType = SByte), @p6='?' (DbType = Int64), @p7='?' (DbType = UInt64), @p8='?' (DbType = Int64), @p9='?' (Size = 64), @p10='?' (Size = 64), @p11='?' (Size = 255), @p12='?' (DbType = SByte), @p13='?' (DbType = Int64), @p14='?' (DbType = UInt64), @p15='?' (DbType = Int64), @p16='?' (Size = 64), @p17='?' (Size = 64), @p18='?' (Size = 255), @p19='?' (DbType = SByte), @p20='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
INSERT INTO `sys_dict` (`Id`, `created_id`, `dict_code`, `dict_name`, `Remark`, `Status`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6),
(@p7, @p8, @p9, @p10, @p11, @p12, @p13),
(@p14, @p15, @p16, @p17, @p18, @p19, @p20);
2025-06-24 08:55:32.838 +08:00 [INF] Executed DbCommand (54ms) [Parameters=[@p0='?' (DbType = UInt64), @p1='?' (DbType = Int64), @p2='?' (DbType = UInt64), @p3='?' (Size = 128), @p4='?' (Size = 128), @p5='?' (DbType = Int32), @p6='?' (DbType = SByte), @p7='?' (DbType = Int64), @p8='?' (DbType = UInt64), @p9='?' (DbType = Int64), @p10='?' (DbType = UInt64), @p11='?' (Size = 128), @p12='?' (Size = 128), @p13='?' (DbType = Int32), @p14='?' (DbType = SByte), @p15='?' (DbType = Int64), @p16='?' (DbType = UInt64), @p17='?' (DbType = Int64), @p18='?' (DbType = UInt64), @p19='?' (Size = 128), @p20='?' (Size = 128), @p21='?' (DbType = Int32), @p22='?' (DbType = SByte), @p23='?' (DbType = Int64), @p24='?' (DbType = UInt64), @p25='?' (DbType = Int64), @p26='?' (DbType = UInt64), @p27='?' (Size = 128), @p28='?' (Size = 128), @p29='?' (DbType = Int32), @p30='?' (DbType = SByte), @p31='?' (DbType = Int64), @p32='?' (DbType = UInt64), @p33='?' (DbType = Int64), @p34='?' (DbType = UInt64), @p35='?' (Size = 128), @p36='?' (Size = 128), @p37='?' (DbType = Int32), @p38='?' (DbType = SByte), @p39='?' (DbType = Int64), @p40='?' (DbType = UInt64), @p41='?' (DbType = Int64), @p42='?' (DbType = UInt64), @p43='?' (Size = 128), @p44='?' (Size = 128), @p45='?' (DbType = Int32), @p46='?' (DbType = SByte), @p47='?' (DbType = Int64), @p48='?' (DbType = UInt64), @p49='?' (DbType = Int64), @p50='?' (DbType = UInt64), @p51='?' (Size = 128), @p52='?' (Size = 128), @p53='?' (DbType = Int32), @p54='?' (DbType = SByte), @p55='?' (DbType = Int64), @p56='?' (DbType = UInt64), @p57='?' (DbType = Int64), @p58='?' (DbType = UInt64), @p59='?' (Size = 128), @p60='?' (Size = 128), @p61='?' (DbType = Int32), @p62='?' (DbType = SByte), @p63='?' (DbType = Int64), @p64='?' (DbType = UInt64), @p65='?' (DbType = Int64), @p66='?' (DbType = UInt64), @p67='?' (Size = 128), @p68='?' (Size = 128), @p69='?' (DbType = Int32), @p70='?' (DbType = SByte), @p71='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
INSERT INTO `sys_dict_value` (`Id`, `created_id`, `dict_id`, `dict_label`, `dict_value`, `sort_number`, `Status`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7),
(@p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15),
(@p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23),
(@p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31),
(@p32, @p33, @p34, @p35, @p36, @p37, @p38, @p39),
(@p40, @p41, @p42, @p43, @p44, @p45, @p46, @p47),
(@p48, @p49, @p50, @p51, @p52, @p53, @p54, @p55),
(@p56, @p57, @p58, @p59, @p60, @p61, @p62, @p63),
(@p64, @p65, @p66, @p67, @p68, @p69, @p70, @p71);
2025-06-24 08:55:33.081 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[@p0='?' (Size = 64), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int64), @p3='?' (DbType = UInt64), @p4='?' (Size = 255), @p5='?' (DbType = SByte), @p6='?' (Size = 15), @p7='?' (Size = 128), @p8='?' (Size = 128), @p9='?' (Size = 64), @p10='?' (Size = 128), @p11='?' (DbType = UInt64), @p12='?' (Size = 24), @p13='?' (DbType = SByte), @p14='?' (DbType = DateTime), @p15='?' (DbType = Int64), @p16='?' (DbType = SByte)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `sys_user` (`Account`, `created_at`, `created_id`, `dept_id`, `Email`, `Gender`, `Mobile`, `Password`, `Photo`, `Realname`, `Remark`, `role_id`, `staff_code`, `Status`, `updated_at`, `updated_id`, `user_source`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16);
SELECT `Id`
FROM `sys_user`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-24 08:55:33.141 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:55:33.179 +08:00 [INF] Executed action WcsNet.Controllers.InitController.ResetDatabase (WcsNet) in 1251.2656ms
2025-06-24 08:55:33.182 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.InitController.ResetDatabase (WcsNet)'
2025-06-24 08:55:33.184 +08:00 [INF] 请求完成: POST /api/init/reset - 状态码: 200 - 耗时: 1281ms - 响应大小: 72bytes
2025-06-24 08:55:33.191 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/init/reset - 200 null application/json; charset=utf-8 1318.0492ms
2025-06-24 08:55:41.693 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 08:55:41.696 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::ffff:127.0.0.1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:55:41.739 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:55:41.771 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:55:42.091 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:55:42.248 +08:00 [INF] Executed DbCommand (60ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:55:42.250 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:55:42.349 +08:00 [INF] Executed DbCommand (84ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:55:42.364 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:55:42.366 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 593.3731ms
2025-06-24 08:55:42.367 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:55:42.368 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 671ms - 响应大小: 370bytes
2025-06-24 08:55:42.369 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 676.1495ms
2025-06-24 08:55:48.594 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:55:48.598 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::ffff:127.0.0.1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:55:48.599 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:55:48.603 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:55:48.881 +08:00 [INF] Executed DbCommand (116ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:55:48.885 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:55:48.891 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 285.557ms
2025-06-24 08:55:48.892 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:55:48.892 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 294ms - 响应大小: 2483bytes
2025-06-24 08:55:48.894 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 299.0626ms
2025-06-24 08:55:59.666 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:55:59.668 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::ffff:127.0.0.1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:55:59.671 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:55:59.675 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(WcsNet.DTOs.RefreshTokenRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:55:59.935 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:55:59.936 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 260.1726ms
2025-06-24 08:55:59.937 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:55:59.938 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 200 - 耗时: 269ms - 响应大小: 375bytes
2025-06-24 08:55:59.939 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 200 null application/json; charset=utf-8 272.6883ms
2025-06-24 09:07:35.935 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 09:07:35.939 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:07:35.940 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:07:35.941 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 09:07:36.357 +08:00 [INF] Executed DbCommand (58ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 09:07:36.360 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:07:36.361 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 419.1603ms
2025-06-24 09:07:36.361 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:07:36.362 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 422ms - 响应大小: 2483bytes
2025-06-24 09:07:36.363 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 427.1854ms
2025-06-24 09:08:24.045 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 09:08:24.046 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:24.047 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.049 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:08:24.053 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:24.059 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 9.6562ms
2025-06-24 09:08:24.060 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.061 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 14ms - 响应大小: 846bytes
2025-06-24 09:08:24.062 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 16.4415ms
2025-06-24 09:08:24.360 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:08:24.360 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 09:08:24.360 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 09:08:24.361 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:24.362 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:24.363 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:24.364 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:24.364 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.365 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.366 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:08:24.366 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:08:24.367 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:08:24.368 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:24.369 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:24.369 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 2.0813ms
2025-06-24 09:08:24.370 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:24.370 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 1.6124ms
2025-06-24 09:08:24.371 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.372 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:08:24.373 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 10ms - 响应大小: 619bytes
2025-06-24 09:08:24.373 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 9ms - 响应大小: 1144bytes
2025-06-24 09:08:24.374 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 13.9729ms
2025-06-24 09:08:24.374 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 6.058ms
2025-06-24 09:08:24.375 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 14.8555ms
2025-06-24 09:08:24.377 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:24.379 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 17ms - 响应大小: 1599bytes
2025-06-24 09:08:24.379 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.772ms
2025-06-24 09:08:26.024 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 09:08:26.025 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:26.026 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:08:26.028 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:08:26.029 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:26.030 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.1719ms
2025-06-24 09:08:26.030 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:08:26.031 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 5ms - 响应大小: 618bytes
2025-06-24 09:08:26.032 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 8.2972ms
2025-06-24 09:08:26.055 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 09:08:26.056 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:26.057 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:08:26.058 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:08:26.058 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:26.059 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 0.8145ms
2025-06-24 09:08:26.060 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:08:26.060 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 4ms - 响应大小: 618bytes
2025-06-24 09:08:26.061 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 6.2217ms
2025-06-24 09:08:34.503 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:08:34.504 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:34.505 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:34.506 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:08:34.507 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:34.507 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.8303ms
2025-06-24 09:08:34.508 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:34.508 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 09:08:34.509 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.6029ms
2025-06-24 09:08:44.168 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:08:44.170 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:44.171 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:44.171 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:08:44.172 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:44.173 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7969ms
2025-06-24 09:08:44.173 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:44.174 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 09:08:44.175 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.6327ms
2025-06-24 09:08:54.501 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:08:54.502 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:08:54.503 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:54.503 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:08:54.504 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:08:54.505 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0637ms
2025-06-24 09:08:54.506 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:08:54.507 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1597bytes
2025-06-24 09:08:54.508 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 7.228ms
2025-06-24 09:09:04.249 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:09:04.252 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:09:04.254 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:09:04.255 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:09:04.257 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:09:04.258 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.597ms
2025-06-24 09:09:04.259 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:09:04.260 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 7ms - 响应大小: 1598bytes
2025-06-24 09:09:04.268 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 18.6673ms
2025-06-24 09:09:09.337 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 09:09:09.340 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:09:09.344 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:09:09.350 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:09:09.482 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:09:09.487 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:09:09.506 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 153.2928ms
2025-06-24 09:09:09.507 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:09:09.508 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 167ms - 响应大小: 311bytes
2025-06-24 09:09:09.510 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 172.7236ms
2025-06-24 09:11:07.542 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 09:11:07.544 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:11:07.545 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 09:11:07.548 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 09:11:07.720 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:11:07.721 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:11:07.724 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 174.9274ms
2025-06-24 09:11:07.724 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 09:11:07.725 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 181ms - 响应大小: 80bytes
2025-06-24 09:11:07.726 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 183.6629ms
2025-06-24 09:11:09.605 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 09:11:09.606 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:11:09.608 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 09:11:09.613 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 09:11:09.803 +08:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 09:11:09.806 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 09:11:09.807 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 09:11:09.839 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 09:11:09.841 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:11:09.846 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 231.9867ms
2025-06-24 09:11:09.847 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 09:11:09.848 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 241ms - 响应大小: 262bytes
2025-06-24 09:11:09.850 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 244.9425ms
2025-06-24 09:11:09.911 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 09:11:09.912 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:11:09.913 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 09:11:09.916 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 09:11:10.126 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 09:11:10.128 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:11:10.131 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 213.4986ms
2025-06-24 09:11:10.131 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 09:11:10.132 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 220ms - 响应大小: 92bytes
2025-06-24 09:11:10.134 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 222.818ms
2025-06-24 09:12:53.138 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 09:12:53.142 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:12:53.143 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:12:53.143 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:12:53.224 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:12:53.226 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:12:53.226 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 82.2637ms
2025-06-24 09:12:53.227 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:12:53.227 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 85ms - 响应大小: 311bytes
2025-06-24 09:12:53.228 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 89.9654ms
2025-06-24 09:43:15.593 +08:00 [INF] 使用内存存储服务
2025-06-24 09:43:16.731 +08:00 [INF] Executed DbCommand (85ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-24 09:43:16.738 +08:00 [INF] 数据库和表创建成功
2025-06-24 09:43:16.962 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 09:43:16.967 +08:00 [INF] 数据库连接正常，当前有 1 个用户
2025-06-24 09:43:17.025 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 09:43:17.058 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 09:43:17.060 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 09:43:17.061 +08:00 [INF] Hosting environment: Development
2025-06-24 09:43:17.061 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 09:44:03.296 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 09:44:03.352 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:44:03.374 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 09:44:03.377 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 09:44:03.402 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 09:44:03.596 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 09:44:03.719 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 09:44:03.772 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 09:44:03.910 +08:00 [INF] Executed DbCommand (47ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 09:44:03.956 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:44:03.969 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 560.7761ms
2025-06-24 09:44:03.973 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 09:44:03.974 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 624ms - 响应大小: 370bytes
2025-06-24 09:44:03.984 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 690.6251ms
2025-06-24 09:44:14.547 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 09:44:14.555 +08:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 09:44:14.558 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: No authenticationScheme was specified, and there was no DefaultChallengeScheme found. The default schemes can be set using either AddAuthentication(string defaultScheme) or AddAuthentication(Action<AuthenticationOptions> configureOptions).
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 09:44:14.573 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 500 null text/plain; charset=utf-8 25.2732ms
2025-06-24 09:44:54.098 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 09:44:54.103 +08:00 [INF] Authorization failed. These requirements were not met:
DenyAnonymousAuthorizationRequirement: Requires an authenticated user.
2025-06-24 09:44:54.105 +08:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: No authenticationScheme was specified, and there was no DefaultChallengeScheme found. The default schemes can be set using either AddAuthentication(string defaultScheme) or AddAuthentication(Action<AuthenticationOptions> configureOptions).
   at Microsoft.AspNetCore.Authentication.AuthenticationService.ChallengeAsync(HttpContext context, String scheme, AuthenticationProperties properties)
   at Microsoft.AspNetCore.Authorization.Policy.AuthorizationMiddlewareResultHandler.<>c__DisplayClass0_0.<<HandleAsync>g__Handle|0>d.MoveNext()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-06-24 09:44:54.109 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 500 null text/plain; charset=utf-8 10.8858ms
2025-06-24 09:45:31.175 +08:00 [INF] 使用内存存储服务
2025-06-24 09:45:32.287 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-24 09:45:32.295 +08:00 [INF] 数据库和表创建成功
2025-06-24 09:45:32.465 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 09:45:32.470 +08:00 [INF] 数据库连接正常，当前有 1 个用户
2025-06-24 09:45:32.534 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 09:45:32.571 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 09:45:32.573 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 09:45:32.574 +08:00 [INF] Hosting environment: Development
2025-06-24 09:45:32.574 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 09:46:14.187 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 09:46:14.226 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:46:14.233 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 09:46:14.236 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 09:46:14.265 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:46:14.460 +08:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 09:46:14.552 +08:00 [INF] Executed DbCommand (34ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 09:46:14.568 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:46:14.656 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 385.9563ms
2025-06-24 09:46:14.660 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 09:46:14.663 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 439ms - 响应大小: 1598bytes
2025-06-24 09:46:14.673 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 487.357ms
2025-06-24 09:46:27.996 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices - application/json 196
2025-06-24 09:46:27.999 +08:00 [INF] 请求开始: POST /api/devices - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:46:28.004 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 09:46:28.007 +08:00 [INF] Route matched with {action = "CreateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] CreateDevice(WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:46:28.302 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE `w`.`device_code` = @__request_DeviceCode_0)
2025-06-24 09:46:28.574 +08:00 [INF] Executed DbCommand (28ms) [Parameters=[@p0='?' (Size = 128), @p1='?' (DbType = SByte), @p2='?' (DbType = DateTime), @p3='?' (DbType = Int64), @p4='?' (Size = 128), @p5='?' (Size = 128), @p6='?' (DbType = SByte), @p7='?' (DbType = SByte), @p8='?' (DbType = Int64), @p9='?' (DbType = Int32), @p10='?' (DbType = SByte), @p11='?' (DbType = SByte), @p12='?' (DbType = DateTime), @p13='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_device` (`Addr`, `comm_type`, `created_at`, `created_id`, `device_code`, `device_name`, `device_type`, `Online`, `Port`, `Position`, `run_status`, `Status`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13);
SELECT `Id`
FROM `wcs_device`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-24 09:46:28.630 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:46:28.636 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDevice (WcsNet) in 627.4274ms
2025-06-24 09:46:28.637 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDevice (WcsNet)'
2025-06-24 09:46:28.639 +08:00 [INF] 请求完成: POST /api/devices - 状态码: 200 - 耗时: 639ms - 响应大小: 292bytes
2025-06-24 09:46:28.642 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices - 200 null application/json; charset=utf-8 645.4754ms
2025-06-24 09:46:45.376 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/8 - null null
2025-06-24 09:46:45.380 +08:00 [INF] 请求开始: GET /api/devices/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:46:45.382 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevice (WcsNet)'
2025-06-24 09:46:45.387 +08:00 [INF] Route matched with {action = "GetDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DeviceDto]]] GetDevice(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:46:45.539 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__id_0
LIMIT 1
2025-06-24 09:46:45.545 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:46:45.552 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevice (WcsNet) in 163.0676ms
2025-06-24 09:46:45.554 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevice (WcsNet)'
2025-06-24 09:46:45.554 +08:00 [INF] 请求完成: GET /api/devices/8 - 状态码: 200 - 耗时: 174ms - 响应大小: 272bytes
2025-06-24 09:46:45.556 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/8 - 200 null application/json; charset=utf-8 179.5992ms
2025-06-24 09:46:54.874 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/8 - application/json 218
2025-06-24 09:46:54.878 +08:00 [INF] 请求开始: PUT /api/devices/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:46:54.880 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-24 09:46:54.885 +08:00 [INF] Route matched with {action = "UpdateDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDevice(UInt64, WcsNet.DTOs.DeviceRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:46:55.263 +08:00 [INF] Executed DbCommand (109ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 09:46:55.303 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[@__request_DeviceCode_0='?' (Size = 128), @__id_1='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT EXISTS (
    SELECT 1
    FROM `wcs_device` AS `w`
    WHERE (`w`.`device_code` = @__request_DeviceCode_0) AND (`w`.`Id` <> @__id_1))
2025-06-24 09:46:55.359 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@p5='?' (DbType = UInt64), @p0='?' (Size = 128), @p1='?' (Size = 128), @p2='?' (DbType = Int64), @p3='?' (DbType = Int32), @p4='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device` SET `device_code` = @p0, `device_name` = @p1, `Port` = @p2, `Position` = @p3, `updated_at` = @p4
WHERE `Id` = @p5;
SELECT ROW_COUNT();
2025-06-24 09:46:55.363 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:46:55.366 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet) in 478.6295ms
2025-06-24 09:46:55.367 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDevice (WcsNet)'
2025-06-24 09:46:55.368 +08:00 [INF] 请求完成: PUT /api/devices/8 - 状态码: 200 - 耗时: 490ms - 响应大小: 48bytes
2025-06-24 09:46:55.370 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/8 - 200 null application/json; charset=utf-8 495.6385ms
2025-06-24 09:47:03.768 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - null 0
2025-06-24 09:47:03.772 +08:00 [INF] 请求开始: DELETE /api/devices/8 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:03.774 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 09:47:03.779 +08:00 [INF] Route matched with {action = "DeleteDevice", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteDevice(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:04.019 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`comm_type`, `w`.`created_at`, `w`.`created_id`, `w`.`device_code`, `w`.`device_name`, `w`.`device_type`, `w`.`Online`, `w`.`Port`, `w`.`Position`, `w`.`run_status`, `w`.`Status`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 09:47:04.118 +08:00 [INF] Executed DbCommand (69ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__id_0
2025-06-24 09:47:04.175 +08:00 [INF] Executed DbCommand (21ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_device`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-24 09:47:04.179 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:47:04.180 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet) in 398.9834ms
2025-06-24 09:47:04.182 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.DeleteDevice (WcsNet)'
2025-06-24 09:47:04.182 +08:00 [INF] 请求完成: DELETE /api/devices/8 - 状态码: 200 - 耗时: 410ms - 响应大小: 48bytes
2025-06-24 09:47:04.184 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/8 - 200 null application/json; charset=utf-8 415.8286ms
2025-06-24 09:47:17.479 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/devices/property - application/json 205
2025-06-24 09:47:17.482 +08:00 [INF] 请求开始: POST /api/devices/property - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:17.484 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-24 09:47:17.489 +08:00 [INF] Route matched with {action = "CreateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] CreateDeviceProperty(WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:17.693 +08:00 [INF] Executed DbCommand (36ms) [Parameters=[@p0='?' (Size = 64), @p1='?' (DbType = DateTime), @p2='?' (DbType = Int64), @p3='?' (DbType = UInt64), @p4='?' (DbType = SByte), @p5='?' (DbType = SByte), @p6='?' (Size = 16), @p7='?' (Size = 64), @p8='?' (DbType = SByte), @p9='?' (Size = 128), @p10='?' (DbType = DateTime), @p11='?' (DbType = Int64)], CommandType='"Text"', CommandTimeout='30']
INSERT INTO `wcs_device_property` (`Addr`, `created_at`, `created_id`, `device_id`, `Direction`, `modbus_type`, `plc_type`, `prop_code`, `prop_length`, `prop_name`, `updated_at`, `updated_id`)
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11);
SELECT `Id`
FROM `wcs_device_property`
WHERE ROW_COUNT() = 1 AND `Id` = LAST_INSERT_ID();
2025-06-24 09:47:17.721 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:47:17.725 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet) in 234.0602ms
2025-06-24 09:47:17.727 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.CreateDeviceProperty (WcsNet)'
2025-06-24 09:47:17.730 +08:00 [INF] 请求完成: POST /api/devices/property - 状态码: 200 - 耗时: 247ms - 响应大小: 275bytes
2025-06-24 09:47:17.732 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/devices/property - 200 null application/json; charset=utf-8 252.4677ms
2025-06-24 09:47:27.016 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - null null
2025-06-24 09:47:27.018 +08:00 [INF] 请求开始: GET /api/devices/props/2 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:27.020 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 09:47:27.025 +08:00 [INF] Route matched with {action = "GetDeviceProperties", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.DevicePropertyDto]]]] GetDeviceProperties(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:27.125 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[@__deviceId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`device_id` = @__deviceId_0
ORDER BY `w`.`created_at`
2025-06-24 09:47:27.128 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:47:27.132 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet) in 106.1996ms
2025-06-24 09:47:27.133 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperties (WcsNet)'
2025-06-24 09:47:27.135 +08:00 [INF] 请求完成: GET /api/devices/props/2 - 状态码: 200 - 耗时: 116ms - 响应大小: 1340bytes
2025-06-24 09:47:27.136 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/props/2 - 200 null application/json; charset=utf-8 120.1563ms
2025-06-24 09:47:38.329 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices/property/7 - null null
2025-06-24 09:47:38.333 +08:00 [INF] 请求开始: GET /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:38.335 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperty (WcsNet)'
2025-06-24 09:47:38.341 +08:00 [INF] Route matched with {action = "GetDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.DevicePropertyDto]]] GetDeviceProperty(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:38.574 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[@__id_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_id` AS `DeviceId`, `w`.`prop_code` AS `PropCode`, `w`.`prop_name` AS `PropName`, `w`.`Addr`, `w`.`Direction`, `w`.`modbus_type` AS `ModbusType`, `w`.`plc_type` AS `PlcType`, `w`.`prop_length` AS `PropLength`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device_property` AS `w`
WHERE `w`.`Id` = @__id_0
LIMIT 1
2025-06-24 09:47:38.578 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:47:38.580 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDeviceProperty (WcsNet) in 233.6111ms
2025-06-24 09:47:38.581 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDeviceProperty (WcsNet)'
2025-06-24 09:47:38.582 +08:00 [INF] 请求完成: GET /api/devices/property/7 - 状态码: 200 - 耗时: 249ms - 响应大小: 256bytes
2025-06-24 09:47:38.584 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices/property/7 - 200 null application/json; charset=utf-8 255.0592ms
2025-06-24 09:47:51.013 +08:00 [INF] Request starting HTTP/1.1 PUT http://localhost:8666/api/devices/property/7 - application/json 222
2025-06-24 09:47:51.015 +08:00 [INF] 请求开始: PUT /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:51.017 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-24 09:47:51.021 +08:00 [INF] Route matched with {action = "UpdateDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] UpdateDeviceProperty(UInt64, WcsNet.DTOs.DevicePropertyRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:51.282 +08:00 [INF] Executed DbCommand (68ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 09:47:51.454 +08:00 [INF] Executed DbCommand (84ms) [Parameters=[@p8='?' (DbType = UInt64), @p0='?' (Size = 64), @p1='?' (DbType = SByte), @p2='?' (DbType = SByte), @p3='?' (Size = 16), @p4='?' (Size = 64), @p5='?' (DbType = SByte), @p6='?' (Size = 128), @p7='?' (DbType = DateTime)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
UPDATE `wcs_device_property` SET `Addr` = @p0, `Direction` = @p1, `modbus_type` = @p2, `plc_type` = @p3, `prop_code` = @p4, `prop_length` = @p5, `prop_name` = @p6, `updated_at` = @p7
WHERE `Id` = @p8;
SELECT ROW_COUNT();
2025-06-24 09:47:51.459 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:47:51.462 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet) in 438.4413ms
2025-06-24 09:47:51.463 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.UpdateDeviceProperty (WcsNet)'
2025-06-24 09:47:51.464 +08:00 [INF] 请求完成: PUT /api/devices/property/7 - 状态码: 200 - 耗时: 449ms - 响应大小: 48bytes
2025-06-24 09:47:51.466 +08:00 [INF] Request finished HTTP/1.1 PUT http://localhost:8666/api/devices/property/7 - 200 null application/json; charset=utf-8 452.9769ms
2025-06-24 09:47:59.751 +08:00 [INF] Request starting HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - null 0
2025-06-24 09:47:59.754 +08:00 [INF] 请求开始: DELETE /api/devices/property/7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 09:47:59.755 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.DeleteDeviceProperty (WcsNet)'
2025-06-24 09:47:59.760 +08:00 [INF] Route matched with {action = "DeleteDeviceProperty", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.String]]] DeleteDeviceProperty(UInt64) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 09:47:59.884 +08:00 [INF] Executed DbCommand (15ms) [Parameters=[@__p_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`Addr`, `w`.`created_at`, `w`.`created_id`, `w`.`device_id`, `w`.`Direction`, `w`.`modbus_type`, `w`.`plc_type`, `w`.`prop_code`, `w`.`prop_length`, `w`.`prop_name`, `w`.`updated_at`, `w`.`updated_id`
FROM `wcs_device_property` AS `w`
WHERE `w`.`Id` = @__p_0
LIMIT 1
2025-06-24 09:48:00.076 +08:00 [INF] Executed DbCommand (88ms) [Parameters=[@p0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SET AUTOCOMMIT = 1;
DELETE FROM `wcs_device_property`
WHERE `Id` = @p0;
SELECT ROW_COUNT();
2025-06-24 09:48:00.079 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:48:00.080 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.DeleteDeviceProperty (WcsNet) in 317.3175ms
2025-06-24 09:48:00.081 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.DeleteDeviceProperty (WcsNet)'
2025-06-24 09:48:00.082 +08:00 [INF] 请求完成: DELETE /api/devices/property/7 - 状态码: 200 - 耗时: 328ms - 响应大小: 48bytes
2025-06-24 09:48:00.084 +08:00 [INF] Request finished HTTP/1.1 DELETE http://localhost:8666/api/devices/property/7 - 200 null application/json; charset=utf-8 333.039ms
2025-06-24 09:55:29.225 +08:00 [INF] Application is shutting down...
2025-06-24 09:56:01.151 +08:00 [INF] 使用内存存储服务
2025-06-24 09:56:02.189 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcs'
2025-06-24 09:56:02.196 +08:00 [INF] 数据库和表创建成功
2025-06-24 09:56:02.329 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 09:56:02.334 +08:00 [INF] 数据库连接正常，当前有 1 个用户
2025-06-24 09:56:02.398 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 09:56:02.435 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 09:56:02.437 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 09:56:02.438 +08:00 [INF] Hosting environment: Development
2025-06-24 09:56:02.438 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 09:56:22.060 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 09:56:22.090 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:56:22.095 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 09:56:22.096 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:56:22.108 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 09:56:22.382 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 09:56:22.425 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:56:22.467 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 356.49ms
2025-06-24 09:56:22.468 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:56:22.469 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 381ms - 响应大小: 2483bytes
2025-06-24 09:56:22.478 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 419.1589ms
2025-06-24 09:56:24.135 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 09:56:24.140 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:56:24.141 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:56:24.150 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:56:24.382 +08:00 [INF] Executed DbCommand (68ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:56:24.409 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:56:24.426 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 274.4857ms
2025-06-24 09:56:24.428 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:56:24.429 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 288ms - 响应大小: 311bytes
2025-06-24 09:56:24.431 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 295.541ms
2025-06-24 09:57:46.061 +08:00 [INF] Application is shutting down...
2025-06-24 09:57:50.301 +08:00 [INF] 使用内存存储服务
2025-06-24 09:57:51.468 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 09:57:51.476 +08:00 [INF] 数据库和表创建成功
2025-06-24 09:57:51.704 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 09:57:51.712 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 09:57:51.776 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 09:57:51.813 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 09:57:51.815 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 09:57:51.816 +08:00 [INF] Hosting environment: Development
2025-06-24 09:57:51.816 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 09:57:59.143 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 09:57:59.174 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:57:59.177 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 09:57:59.179 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:57:59.190 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 09:57:59.418 +08:00 [INF] Executed DbCommand (70ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 09:57:59.461 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:57:59.505 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 311.6708ms
2025-06-24 09:57:59.507 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:57:59.508 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 335ms - 响应大小: 6656bytes
2025-06-24 09:57:59.516 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 373.9763ms
2025-06-24 09:57:59.840 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 09:57:59.843 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:57:59.845 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:57:59.852 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:58:00.067 +08:00 [INF] Executed DbCommand (23ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:58:00.085 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:58:00.100 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 245.5234ms
2025-06-24 09:58:00.101 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:58:00.102 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 258ms - 响应大小: 328bytes
2025-06-24 09:58:00.103 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 263.2936ms
2025-06-24 09:58:03.824 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - null null
2025-06-24 09:58:03.826 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:58:03.828 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:58:03.828 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:58:04.092 +08:00 [INF] Executed DbCommand (41ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:58:04.094 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:58:04.095 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 265.4799ms
2025-06-24 09:58:04.095 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:58:04.096 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/10 - 状态码: 200 - 耗时: 269ms - 响应大小: 358bytes
2025-06-24 09:58:04.097 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/10 - 200 null application/json; charset=utf-8 272.7476ms
2025-06-24 09:58:10.340 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 09:58:10.341 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:58:10.342 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:58:10.343 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 09:58:10.589 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 09:58:10.591 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:58:10.592 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 248.0992ms
2025-06-24 09:58:10.593 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 09:58:10.593 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 252ms - 响应大小: 328bytes
2025-06-24 09:58:10.594 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 254.9466ms
2025-06-24 09:59:02.419 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 09:59:02.421 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:02.424 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:59:02.425 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 09:59:02.610 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 09:59:02.613 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 09:59:02.615 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 187.5142ms
2025-06-24 09:59:02.616 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 09:59:02.616 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 195ms - 响应大小: 6656bytes
2025-06-24 09:59:02.618 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 199.6894ms
2025-06-24 09:59:39.307 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:59:39.307 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 09:59:39.307 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 09:59:39.309 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:39.310 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:39.311 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:39.313 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.312 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.311 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:39.315 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:59:39.315 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:59:39.316 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:59:39.323 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:39.323 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:39.324 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:39.337 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 17.5327ms
2025-06-24 09:59:39.337 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 19.5593ms
2025-06-24 09:59:39.337 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 20.6943ms
2025-06-24 09:59:39.338 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:39.339 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.339 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.340 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 31ms - 响应大小: 1599bytes
2025-06-24 09:59:39.341 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 30ms - 响应大小: 847bytes
2025-06-24 09:59:39.341 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 30ms - 响应大小: 619bytes
2025-06-24 09:59:39.342 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 34.731ms
2025-06-24 09:59:39.343 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 35.5196ms
2025-06-24 09:59:39.344 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 36.4406ms
2025-06-24 09:59:39.611 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 09:59:39.613 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:39.614 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.614 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:59:39.615 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:39.616 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 0.8023ms
2025-06-24 09:59:39.616 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 09:59:39.617 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 3ms - 响应大小: 1145bytes
2025-06-24 09:59:39.618 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 6.2039ms
2025-06-24 09:59:41.320 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 09:59:41.322 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:41.323 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:59:41.325 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:59:41.326 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:41.327 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.5154ms
2025-06-24 09:59:41.328 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:59:41.329 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 6ms - 响应大小: 616bytes
2025-06-24 09:59:41.331 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 10.6569ms
2025-06-24 09:59:41.350 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 09:59:41.351 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:41.352 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:59:41.353 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 09:59:41.354 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:41.355 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.5513ms
2025-06-24 09:59:41.356 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 09:59:41.357 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 5ms - 响应大小: 615bytes
2025-06-24 09:59:41.358 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 7.9953ms
2025-06-24 09:59:49.583 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:59:49.585 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:49.587 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:49.588 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:59:49.591 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:49.592 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.9911ms
2025-06-24 09:59:49.593 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:49.593 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1599bytes
2025-06-24 09:59:49.594 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 11.4118ms
2025-06-24 09:59:59.286 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 09:59:59.291 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 09:59:59.292 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:59.294 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 09:59:59.295 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 09:59:59.296 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3769ms
2025-06-24 09:59:59.296 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 09:59:59.298 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1598bytes
2025-06-24 09:59:59.300 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 13.5792ms
2025-06-24 10:00:09.032 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - null null
2025-06-24 10:00:09.033 +08:00 [INF] 请求开始: GET /api/sys/dictvalues/3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 10:00:09.034 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:00:09.035 +08:00 [INF] Route matched with {action = "GetDictValues", controller = "Dict"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDictValues(Int32) on controller WcsNet.Controllers.DictController (WcsNet).
2025-06-24 10:00:09.190 +08:00 [INF] Executed DbCommand (21ms) [Parameters=[@__dictId_0='?' (DbType = UInt64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`created_id`, `s`.`dict_id`, `s`.`dict_label`, `s`.`dict_value`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_id`
FROM `sys_dict_value` AS `s`
WHERE (`s`.`dict_id` = @__dictId_0) AND (`s`.`Status` = 1)
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 10:00:09.191 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 10:00:09.192 +08:00 [INF] Executed action WcsNet.Controllers.DictController.GetDictValues (WcsNet) in 156.4229ms
2025-06-24 10:00:09.193 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DictController.GetDictValues (WcsNet)'
2025-06-24 10:00:09.193 +08:00 [INF] 请求完成: GET /api/sys/dictvalues/3 - 状态码: 200 - 耗时: 159ms - 响应大小: 328bytes
2025-06-24 10:00:09.194 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/dictvalues/3 - 200 null application/json; charset=utf-8 162.0976ms
2025-06-24 10:05:26.956 +08:00 [INF] 使用内存存储服务
2025-06-24 10:05:28.249 +08:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:05:28.256 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:05:28.413 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:05:28.418 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:05:28.502 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:05:28.595 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:05:28.599 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:05:28.600 +08:00 [INF] Hosting environment: Development
2025-06-24 10:05:28.601 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:05:54.484 +08:00 [INF] 使用内存存储服务
2025-06-24 10:05:55.998 +08:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:05:56.006 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:05:56.172 +08:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:05:56.177 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:05:56.242 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:05:56.279 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:05:56.281 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:05:56.282 +08:00 [INF] Hosting environment: Development
2025-06-24 10:05:56.283 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:06:21.149 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - null null
2025-06-24 10:06:21.180 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:06:21.183 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:06:21.185 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:06:21.201 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:06:21.333 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:06:21.409 +08:00 [INF] Executed DbCommand (37ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:06:21.418 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:06:21.468 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 263.274ms
2025-06-24 10:06:21.470 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:06:21.471 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=10 - 状态码: 200 - 耗时: 292ms - 响应大小: 1628bytes
2025-06-24 10:06:21.476 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 328.8298ms
2025-06-24 10:06:54.006 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:06:54.011 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:06:54.015 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:06:54.016 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:06:54.240 +08:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:06:54.256 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:06:54.258 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:06:54.260 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 238.2763ms
2025-06-24 10:06:54.261 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:06:54.262 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 250ms - 响应大小: 1628bytes
2025-06-24 10:06:54.264 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 257.6541ms
2025-06-24 10:07:38.033 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - null null
2025-06-24 10:07:38.037 +08:00 [INF] 请求开始: GET /api/devices?page_no=1&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:07:38.038 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:07:38.040 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:07:38.258 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:07:38.285 +08:00 [INF] Executed DbCommand (16ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:07:38.287 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:07:38.289 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 243.1613ms
2025-06-24 10:07:38.290 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:07:38.290 +08:00 [INF] 请求完成: GET /api/devices?page_no=1&page_size=3 - 状态码: 200 - 耗时: 253ms - 响应大小: 1628bytes
2025-06-24 10:07:38.292 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=1&page_size=3 - 200 null application/json; charset=utf-8 259.2013ms
2025-06-24 10:08:23.246 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - null null
2025-06-24 10:08:23.266 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:08:23.275 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:08:23.277 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:08:23.694 +08:00 [INF] Executed DbCommand (83ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:08:23.830 +08:00 [INF] Executed DbCommand (105ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:08:23.837 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:08:23.841 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 561.2312ms
2025-06-24 10:08:23.843 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:08:23.844 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3 - 状态码: 200 - 耗时: 577ms - 响应大小: 1628bytes
2025-06-24 10:08:23.847 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - 200 null application/json; charset=utf-8 600.5413ms
2025-06-24 10:24:38.015 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3&device_name=test - null null
2025-06-24 10:24:38.017 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3&device_name=test - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:24:38.018 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:24:38.019 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:24:38.301 +08:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:24:38.392 +08:00 [INF] Executed DbCommand (81ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:24:38.394 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:24:38.395 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 375.3529ms
2025-06-24 10:24:38.396 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:24:38.397 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3&device_name=test - 状态码: 200 - 耗时: 379ms - 响应大小: 1628bytes
2025-06-24 10:24:38.398 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3&device_name=test - 200 null application/json; charset=utf-8 382.6428ms
2025-06-24 10:25:35.933 +08:00 [INF] 使用内存存储服务
2025-06-24 10:25:37.187 +08:00 [INF] Executed DbCommand (32ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT CASE WHEN COUNT(*) = 0 THEN FALSE ELSE TRUE END
FROM information_schema.tables
WHERE table_type = 'BASE TABLE' AND table_schema = 'wcsTest'
2025-06-24 10:25:37.194 +08:00 [INF] 数据库和表创建成功
2025-06-24 10:25:37.331 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 10:25:37.337 +08:00 [INF] 数据库连接正常，当前有 2 个用户
2025-06-24 10:25:37.399 +08:00 [INF] WCS.NET服务启动，端口: 8666
2025-06-24 10:25:37.435 +08:00 [INF] Now listening on: http://[::]:8666
2025-06-24 10:25:37.437 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-24 10:25:37.438 +08:00 [INF] Hosting environment: Development
2025-06-24 10:25:37.438 +08:00 [INF] Content root path: E:\SourceCode\Test\TempTest\Ai\wcs\WcsNet
2025-06-24 10:26:15.041 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - null null
2025-06-24 10:26:15.072 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:26:15.076 +08:00 [WRN] Failed to determine the https port for redirect.
2025-06-24 10:26:15.077 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:26:15.093 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:26:15.313 +08:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:26:15.425 +08:00 [INF] Executed DbCommand (77ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:26:15.436 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:26:15.485 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 389.4011ms
2025-06-24 10:26:15.487 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:26:15.488 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3 - 状态码: 200 - 耗时: 417ms - 响应大小: 1619bytes
2025-06-24 10:26:15.494 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - 200 null application/json; charset=utf-8 453.7102ms
2025-06-24 10:26:47.207 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - null null
2025-06-24 10:26:47.212 +08:00 [INF] 请求开始: GET /api/devices?page_no=2&page_size=3 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 10:26:47.213 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:26:47.214 +08:00 [INF] Route matched with {action = "GetDevices", controller = "Device"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.DeviceDto]]]] GetDevices(WcsNet.DTOs.DeviceListRequest) on controller WcsNet.Controllers.DeviceController (WcsNet).
2025-06-24 10:26:47.349 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `wcs_device` AS `w`
2025-06-24 10:26:47.374 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `w`.`Id`, `w`.`device_code` AS `DeviceCode`, `w`.`device_name` AS `DeviceName`, `w`.`device_type` AS `DeviceType`, `w`.`comm_type` AS `CommType`, `w`.`Addr`, `w`.`Port`, `w`.`Online`, `w`.`run_status` AS `RunStatus`, `w`.`Status`, `w`.`Position`, `w`.`created_at` AS `CreatedAt`, `w`.`updated_at` AS `UpdatedAt`
FROM `wcs_device` AS `w`
ORDER BY `w`.`created_at` DESC
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 10:26:47.376 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 10:26:47.377 +08:00 [INF] Executed action WcsNet.Controllers.DeviceController.GetDevices (WcsNet) in 161.6794ms
2025-06-24 10:26:47.377 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeviceController.GetDevices (WcsNet)'
2025-06-24 10:26:47.378 +08:00 [INF] 请求完成: GET /api/devices?page_no=2&page_size=3 - 状态码: 200 - 耗时: 165ms - 响应大小: 1619bytes
2025-06-24 10:26:47.379 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/devices?page_no=2&page_size=3 - 200 null application/json; charset=utf-8 171.6013ms
