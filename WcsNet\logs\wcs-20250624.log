2025-06-24 08:44:34.039 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:34.047 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:34.049 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:34.056 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.064 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:34.345 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:34.374 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 308.1322ms
2025-06-24 08:44:34.375 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.379 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 329ms - 响应大小: 371bytes
2025-06-24 08:44:34.394 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 354.808ms
2025-06-24 08:44:35.754 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:35.758 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:35.758 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:35.760 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.761 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:35.968 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:35.970 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 206.6449ms
2025-06-24 08:44:35.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.972 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 213ms - 响应大小: 371bytes
2025-06-24 08:44:35.978 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 223.7874ms
2025-06-24 08:44:37.541 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:37.543 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:37.544 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:37.545 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.546 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:37.714 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:37.715 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 167.1449ms
2025-06-24 08:44:37.716 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.717 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 173ms - 响应大小: 371bytes
2025-06-24 08:44:37.719 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 177.6483ms
2025-06-24 08:44:38.005 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:38.036 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:38.042 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:38.050 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.052 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:38.154 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:38.184 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 122.3528ms
2025-06-24 08:44:38.192 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.250 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 208ms - 响应大小: 371bytes
2025-06-24 08:44:38.313 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 307.7236ms
