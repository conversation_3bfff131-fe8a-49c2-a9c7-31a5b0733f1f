2025-06-24 08:44:34.039 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:34.047 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:34.049 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:34.056 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.064 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:34.345 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:34.374 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 308.1322ms
2025-06-24 08:44:34.375 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:34.379 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 329ms - 响应大小: 371bytes
2025-06-24 08:44:34.394 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 354.808ms
2025-06-24 08:44:35.754 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:35.758 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:35.758 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:35.760 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.761 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:35.968 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:35.970 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 206.6449ms
2025-06-24 08:44:35.971 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:35.972 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 213ms - 响应大小: 371bytes
2025-06-24 08:44:35.978 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 223.7874ms
2025-06-24 08:44:37.541 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:37.543 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:37.544 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:37.545 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.546 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:37.714 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:37.715 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 167.1449ms
2025-06-24 08:44:37.716 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:37.717 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 173ms - 响应大小: 371bytes
2025-06-24 08:44:37.719 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 177.6483ms
2025-06-24 08:44:38.005 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:38.036 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:38.042 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:38.050 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.052 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:38.154 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:38.184 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 122.3528ms
2025-06-24 08:44:38.192 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:38.250 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 208ms - 响应大小: 371bytes
2025-06-24 08:44:38.313 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 307.7236ms
2025-06-24 08:44:48.283 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:48.308 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:48.360 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:48.384 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:48.392 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:48.399 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:48.397 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.469 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:48.460 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.479 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:48.633 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:48.646 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 172.9117ms
2025-06-24 08:44:48.658 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.660 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 275ms - 响应大小: 371bytes
2025-06-24 08:44:48.721 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 438.2998ms
2025-06-24 08:44:48.759 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:48.881 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 238.1915ms
2025-06-24 08:44:48.884 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:48.892 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 492ms - 响应大小: 371bytes
2025-06-24 08:44:48.931 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 571.1659ms
2025-06-24 08:44:57.234 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:57.245 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:57.249 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:57.258 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:57.265 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:57.560 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:57.564 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 296.9463ms
2025-06-24 08:44:57.565 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:57.566 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 316ms - 响应大小: 371bytes
2025-06-24 08:44:57.569 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 335.3636ms
2025-06-24 08:44:58.277 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:44:58.287 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:44:58.312 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:44:58.314 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:58.316 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:44:58.500 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:44:58.503 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 182.4041ms
2025-06-24 08:44:58.504 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:44:58.505 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 192ms - 响应大小: 371bytes
2025-06-24 08:44:58.508 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 230.8325ms
2025-06-24 08:45:08.480 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:08.493 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:08.494 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:08.496 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:08.498 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:08.721 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:08.724 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 203.8621ms
2025-06-24 08:45:08.725 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:08.726 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 231ms - 响应大小: 371bytes
2025-06-24 08:45:08.728 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 247.3507ms
2025-06-24 08:45:20.797 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:20.799 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:20.800 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:20.802 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:20.804 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:21.268 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:21.308 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 500.2828ms
2025-06-24 08:45:21.329 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:21.393 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 593ms - 响应大小: 371bytes
2025-06-24 08:45:21.489 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 692.4265ms
2025-06-24 08:45:28.258 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - application/json 51
2025-06-24 08:45:28.260 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:45:28.261 +08:00 [INF] 请求开始: POST /api/user/refreshtoken - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:28.263 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:28.264 +08:00 [INF] Route matched with {action = "RefreshToken", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] RefreshToken(System.String) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:45:28.332 +08:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-06-24 08:45:28.333 +08:00 [INF] Executed action WcsNet.Controllers.UserController.RefreshToken (WcsNet) in 66.5736ms
2025-06-24 08:45:28.334 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.RefreshToken (WcsNet)'
2025-06-24 08:45:28.335 +08:00 [INF] 请求完成: POST /api/user/refreshtoken - 状态码: 400 - 耗时: 73ms - 响应大小: 371bytes
2025-06-24 08:45:28.337 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/refreshtoken - 400 null application/problem+json; charset=utf-8 79.2995ms
2025-06-24 08:45:41.371 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:45:41.375 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:41.378 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:41.379 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:45:41.382 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:45:41.384 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 3.277ms
2025-06-24 08:45:41.386 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:41.387 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 12ms - 响应大小: 1599bytes
2025-06-24 08:45:41.390 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.5529ms
2025-06-24 08:45:50.033 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:45:50.039 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:45:50.045 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:50.045 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:45:50.047 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:45:50.048 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.9301ms
2025-06-24 08:45:50.048 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:45:50.049 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1598bytes
2025-06-24 08:45:50.050 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 16.8554ms
2025-06-24 08:46:06.752 +08:00 [INF] Request starting HTTP/1.1 POST http://localhost:8666/api/user/login - application/json 74
2025-06-24 08:46:06.763 +08:00 [INF] CORS policy execution successful.
2025-06-24 08:46:06.764 +08:00 [INF] 请求开始: POST /api/user/login - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:06.767 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:46:06.771 +08:00 [INF] Route matched with {action = "Login", controller = "User"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.LoginResponse]]] Login(WcsNet.DTOs.LoginRequest) on controller WcsNet.Controllers.UserController (WcsNet).
2025-06-24 08:46:06.840 +08:00 [INF] 登录调试 - 账号: admin, 输入密码: dc483e80a7a0bd9ef71d8cf973673924, 盐值: Быстрый@84$!94jY4, 生成哈希: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:46:07.114 +08:00 [INF] Executed DbCommand (56ms) [Parameters=[@__account_0='?' (Size = 64)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE (`s`.`Account` = @__account_0) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:46:07.151 +08:00 [INF] 数据库密码: 2d21abef9a7f37c11dd1b7a057aa003a102b7896ad0f0169e48608bfdc4d2cbf
2025-06-24 08:46:07.230 +08:00 [INF] Executed DbCommand (25ms) [Parameters=[@__account_0='?' (Size = 64), @__hashedPassword_1='?' (Size = 128)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_id`, `s`.`Email`, `s`.`Gender`, `s`.`Mobile`, `s`.`Password`, `s`.`Photo`, `s`.`Realname`, `s`.`Remark`, `s`.`role_id`, `s`.`staff_code`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`, `s`.`user_source`
FROM `sys_user` AS `s`
WHERE ((`s`.`Account` = @__account_0) AND (`s`.`Password` = @__hashedPassword_1)) AND (`s`.`Status` = 1)
LIMIT 1
2025-06-24 08:46:07.244 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:07.246 +08:00 [INF] Executed action WcsNet.Controllers.UserController.Login (WcsNet) in 474.1166ms
2025-06-24 08:46:07.247 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.UserController.Login (WcsNet)'
2025-06-24 08:46:07.247 +08:00 [INF] 请求完成: POST /api/user/login - 状态码: 200 - 耗时: 483ms - 响应大小: 370bytes
2025-06-24 08:46:07.250 +08:00 [INF] Request finished HTTP/1.1 POST http://localhost:8666/api/user/login - 200 null application/json; charset=utf-8 498.1997ms
2025-06-24 08:46:08.785 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:46:08.787 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:08.789 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:08.796 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:46:08.982 +08:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:09.007 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:09.012 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 214.3499ms
2025-06-24 08:46:09.013 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:09.013 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 226ms - 响应大小: 1678bytes
2025-06-24 08:46:09.014 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 229.525ms
2025-06-24 08:46:10.964 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:10.964 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:10.966 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:10.967 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:10.969 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:10.970 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:10.971 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:10.972 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:10.974 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:10.975 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:10.976 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 2.7987ms
2025-06-24 08:46:10.977 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.5558ms
2025-06-24 08:46:10.978 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:10.979 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:10.980 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 847bytes
2025-06-24 08:46:10.981 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 13ms - 响应大小: 1598bytes
2025-06-24 08:46:10.982 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 18.4978ms
2025-06-24 08:46:10.984 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 19.661ms
2025-06-24 08:46:11.030 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:11.031 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.032 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.033 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.033 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.034 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 0.7417ms
2025-06-24 08:46:11.035 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.035 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 3ms - 响应大小: 620bytes
2025-06-24 08:46:11.036 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 6.4169ms
2025-06-24 08:46:11.279 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:11.279 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:11.281 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.282 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:11.283 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.283 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:11.284 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.285 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:11.285 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.286 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:11.286 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 1.3375ms
2025-06-24 08:46:11.287 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.2073ms
2025-06-24 08:46:11.288 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:11.288 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:11.289 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 8ms - 响应大小: 1144bytes
2025-06-24 08:46:11.289 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 7ms - 响应大小: 617bytes
2025-06-24 08:46:11.291 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 11.6549ms
2025-06-24 08:46:11.292 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 12.6637ms
2025-06-24 08:46:12.865 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:12.868 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:12.869 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:12.870 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:12.871 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:12.872 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.5908ms
2025-06-24 08:46:12.873 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:12.873 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 5ms - 响应大小: 613bytes
2025-06-24 08:46:12.876 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 10.8107ms
2025-06-24 08:46:20.944 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:20.947 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:20.949 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:20.949 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:20.950 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:20.951 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3609ms
2025-06-24 08:46:20.952 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:20.952 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 5ms - 响应大小: 1598bytes
2025-06-24 08:46:20.953 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 9.0356ms
2025-06-24 08:46:28.222 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:46:28.224 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.227 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:28.229 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:46:28.341 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:28.342 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:28.343 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 112.6123ms
2025-06-24 08:46:28.344 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:46:28.344 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 119ms - 响应大小: 1678bytes
2025-06-24 08:46:28.345 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 123.8534ms
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:28.443 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:28.444 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:28.444 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:28.445 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.446 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.447 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.448 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.449 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.450 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.451 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:28.452 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.453 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.456 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.457 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.459 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:28.460 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.460 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.461 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.463 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.464 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.465 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.467 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.468 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.469 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.2116ms
2025-06-24 08:46:28.472 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 7.761ms
2025-06-24 08:46:28.473 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 7.8125ms
2025-06-24 08:46:28.475 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 8.3263ms
2025-06-24 08:46:28.476 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 8.1257ms
2025-06-24 08:46:28.477 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.479 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:28.480 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.482 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:28.483 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.483 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 38ms - 响应大小: 845bytes
2025-06-24 08:46:28.484 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 38ms - 响应大小: 1596bytes
2025-06-24 08:46:28.485 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 38ms - 响应大小: 618bytes
2025-06-24 08:46:28.486 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 37ms - 响应大小: 1146bytes
2025-06-24 08:46:28.486 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 37ms - 响应大小: 613bytes
2025-06-24 08:46:28.488 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 44.5788ms
2025-06-24 08:46:28.490 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 46.4915ms
2025-06-24 08:46:28.491 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 47.9165ms
2025-06-24 08:46:28.493 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 49.2675ms
2025-06-24 08:46:28.494 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 50.7043ms
2025-06-24 08:46:28.751 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:28.753 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:28.754 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.754 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:28.756 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:28.756 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.0547ms
2025-06-24 08:46:28.757 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:28.758 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 5ms - 响应大小: 615bytes
2025-06-24 08:46:28.759 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 7.5571ms
2025-06-24 08:46:34.194 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 08:46:34.195 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.196 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:34.199 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:34.337 +08:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:34.340 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:34.347 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 147.5482ms
2025-06-24 08:46:34.348 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:34.348 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 152ms - 响应大小: 80bytes
2025-06-24 08:46:34.349 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 155.2673ms
2025-06-24 08:46:34.359 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:34.359 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 08:46:34.360 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.361 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:34.362 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:34.363 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:34.365 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:34.365 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 08:46:34.526 +08:00 [INF] Executed DbCommand (52ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 08:46:34.529 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 08:46:34.530 +08:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-24 08:46:34.619 +08:00 [INF] Executed DbCommand (46ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:34.626 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:34.636 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 269.6241ms
2025-06-24 08:46:34.637 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:34.638 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 277ms - 响应大小: 262bytes
2025-06-24 08:46:34.639 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 279.9353ms
2025-06-24 08:46:34.658 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 08:46:34.660 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:34.662 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 295.4425ms
2025-06-24 08:46:34.662 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:34.663 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 301ms - 响应大小: 92bytes
2025-06-24 08:46:34.664 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 304.6412ms
2025-06-24 08:46:36.377 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:36.377 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menulist - null null
2025-06-24 08:46:36.378 +08:00 [INF] 请求开始: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:36.379 +08:00 [INF] 请求开始: GET /api/sys/menulist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:36.380 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:36.381 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:36.383 +08:00 [INF] Route matched with {action = "GetMenuList", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetMenuList() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:36.383 +08:00 [INF] Route matched with {action = "GetRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetRoles(Int32, Int32, System.String) on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:36.514 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`title` AS `Title`, `s`.`parent_id` AS `ParentId`, `s`.`perm_type` AS `PermType`, `s`.`icon` AS `Icon`, `s`.`rank` AS `Rank`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:36.514 +08:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_role` AS `s`
2025-06-24 08:46:36.537 +08:00 [INF] Executed DbCommand (9ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`, `s`.`role_type` AS `RoleType`, `s`.`sort_number` AS `SortNumber`, `s`.`status` AS `Status`, `s`.`remark` AS `Remark`, `s`.`perms` AS `Perms`
FROM `sys_role` AS `s`
ORDER BY `s`.`sort_number`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:36.539 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:36.544 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetRoles (WcsNet) in 158.7858ms
2025-06-24 08:46:36.545 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:36.545 +08:00 [INF] 请求完成: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 167ms - 响应大小: 258bytes
2025-06-24 08:46:36.546 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 169.4487ms
2025-06-24 08:46:36.556 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:36.564 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenuList (WcsNet) in 179.9836ms
2025-06-24 08:46:36.565 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:36.566 +08:00 [INF] 请求完成: GET /api/sys/menulist - 状态码: 200 - 耗时: 186ms - 响应大小: 684bytes
2025-06-24 08:46:36.567 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menulist - 200 null application/json; charset=utf-8 189.583ms
2025-06-24 08:46:38.036 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - null null
2025-06-24 08:46:38.038 +08:00 [INF] 请求开始: GET /api/sys/menus?title= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:38.040 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:38.042 +08:00 [INF] Route matched with {action = "GetMenus", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetMenus() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:38.220 +08:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:38.222 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:38.223 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenus (WcsNet) in 179.7268ms
2025-06-24 08:46:38.224 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:38.224 +08:00 [INF] 请求完成: GET /api/sys/menus?title= - 状态码: 200 - 耗时: 186ms - 响应大小: 1688bytes
2025-06-24 08:46:38.226 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - 200 null application/json; charset=utf-8 189.7656ms
2025-06-24 08:46:39.156 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - null null
2025-06-24 08:46:39.158 +08:00 [INF] 请求开始: GET /api/sys/depts?name=&status= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:39.159 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:39.162 +08:00 [INF] Route matched with {action = "GetDepts", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDepts() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:39.424 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:39.444 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:39.446 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDepts (WcsNet) in 283.5772ms
2025-06-24 08:46:39.447 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:39.447 +08:00 [INF] 请求完成: GET /api/sys/depts?name=&status= - 状态码: 200 - 耗时: 289ms - 响应大小: 169bytes
2025-06-24 08:46:39.448 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - 200 null application/json; charset=utf-8 292.5596ms
2025-06-24 08:46:40.594 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:46:40.594 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:46:40.595 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.596 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.597 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.598 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:40.599 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.599 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:46:40.600 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.601 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.602 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 2.2741ms
2025-06-24 08:46:40.603 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 2.2159ms
2025-06-24 08:46:40.605 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.606 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:46:40.606 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 843bytes
2025-06-24 08:46:40.607 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1599bytes
2025-06-24 08:46:40.608 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 14.374ms
2025-06-24 08:46:40.609 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 15.7443ms
2025-06-24 08:46:40.916 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:46:40.917 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:46:40.918 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:46:40.919 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:46:40.925 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.928 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.930 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.931 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:40.932 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.932 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.933 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.934 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.935 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.936 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:46:40.937 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.937 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.938 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.939 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:40.940 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.5231ms
2025-06-24 08:46:40.941 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.6173ms
2025-06-24 08:46:40.941 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 3.1524ms
2025-06-24 08:46:40.942 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 2.584ms
2025-06-24 08:46:40.942 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.943 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:46:40.943 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.944 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:46:40.944 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 19ms - 响应大小: 620bytes
2025-06-24 08:46:40.945 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 16ms - 响应大小: 1144bytes
2025-06-24 08:46:40.945 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 15ms - 响应大小: 611bytes
2025-06-24 08:46:40.946 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 14ms - 响应大小: 616bytes
2025-06-24 08:46:40.946 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 30.0463ms
2025-06-24 08:46:40.947 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 29.8478ms
2025-06-24 08:46:40.948 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 30.1398ms
2025-06-24 08:46:40.949 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 30.4987ms
2025-06-24 08:46:46.483 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - null null
2025-06-24 08:46:46.484 +08:00 [INF] 请求开始: GET /api/sys/deptlist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:46.485 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:46.486 +08:00 [INF] Route matched with {action = "GetDeptList", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDeptList() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:46.711 +08:00 [INF] Executed DbCommand (50ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`dept_name` AS `DeptName`, `s`.`parent_id` AS `ParentId`
FROM `sys_department` AS `s`
WHERE `s`.`Status` = 1
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:46.713 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:46.714 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDeptList (WcsNet) in 227.0864ms
2025-06-24 08:46:46.714 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDeptList (WcsNet)'
2025-06-24 08:46:46.715 +08:00 [INF] 请求完成: GET /api/sys/deptlist - 状态码: 200 - 耗时: 230ms - 响应大小: 80bytes
2025-06-24 08:46:46.715 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/deptlist - 200 null application/json; charset=utf-8 232.4667ms
2025-06-24 08:46:46.746 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:46.747 +08:00 [INF] 请求开始: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:46.748 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:46.748 +08:00 [INF] Route matched with {action = "GetUsers", controller = "SysUser"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetUsers(Int32, Int32, System.String) on controller WcsNet.Controllers.SysUserController (WcsNet).
2025-06-24 08:46:47.047 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - null null
2025-06-24 08:46:47.049 +08:00 [INF] 请求开始: GET /api/sys/roles/all - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.051 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:47.052 +08:00 [INF] Route matched with {action = "GetAllRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetAllRoles() on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:47.082 +08:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_user` AS `s`
2025-06-24 08:46:47.170 +08:00 [INF] Executed DbCommand (40ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Account`, `s`.`Realname`, `s`.`Email`, `s`.`Mobile`, `s`.`Status`, `s`.`role_id` AS `RoleId`, `s`.`dept_id` AS `DeptId`, `s`.`created_at` AS `CreatedAt`, `s`.`updated_at` AS `UpdatedAt`
FROM `sys_user` AS `s`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:47.171 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:47.172 +08:00 [INF] Executed action WcsNet.Controllers.SysUserController.GetUsers (WcsNet) in 423.1742ms
2025-06-24 08:46:47.173 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysUserController.GetUsers (WcsNet)'
2025-06-24 08:46:47.173 +08:00 [INF] 请求完成: GET /api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 426ms - 响应大小: 262bytes
2025-06-24 08:46:47.175 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/users?dept_id=&account=&mobile=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 428.6ms
2025-06-24 08:46:47.263 +08:00 [INF] Executed DbCommand (30ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`
FROM `sys_role` AS `s`
WHERE `s`.`status` = 1
ORDER BY `s`.`sort_number`
2025-06-24 08:46:47.264 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:47.264 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetAllRoles (WcsNet) in 211.5275ms
2025-06-24 08:46:47.266 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetAllRoles (WcsNet)'
2025-06-24 08:46:47.267 +08:00 [INF] 请求完成: GET /api/sys/roles/all - 状态码: 200 - 耗时: 217ms - 响应大小: 92bytes
2025-06-24 08:46:47.268 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles/all - 200 null application/json; charset=utf-8 220.7031ms
2025-06-24 08:46:47.390 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - null null
2025-06-24 08:46:47.391 +08:00 [INF] 请求开始: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.392 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:47.392 +08:00 [INF] Route matched with {action = "GetRoles", controller = "Role"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[System.Object]]]] GetRoles(Int32, Int32, System.String) on controller WcsNet.Controllers.RoleController (WcsNet).
2025-06-24 08:46:47.616 +08:00 [INF] Executed DbCommand (31ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM `sys_role` AS `s`
2025-06-24 08:46:47.671 +08:00 [INF] Executed DbCommand (35ms) [Parameters=[@__p_1='?' (DbType = Int32), @__p_0='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`role_name` AS `RoleName`, `s`.`role_code` AS `RoleCode`, `s`.`role_type` AS `RoleType`, `s`.`sort_number` AS `SortNumber`, `s`.`status` AS `Status`, `s`.`remark` AS `Remark`, `s`.`perms` AS `Perms`
FROM `sys_role` AS `s`
ORDER BY `s`.`sort_number`
LIMIT @__p_1 OFFSET @__p_0
2025-06-24 08:46:47.673 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:46:47.677 +08:00 [INF] Executed action WcsNet.Controllers.RoleController.GetRoles (WcsNet) in 283.5452ms
2025-06-24 08:46:47.680 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.RoleController.GetRoles (WcsNet)'
2025-06-24 08:46:47.682 +08:00 [INF] 请求完成: GET /api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 状态码: 200 - 耗时: 290ms - 响应大小: 258bytes
2025-06-24 08:46:47.684 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/roles?name=&code=&status=&page_size=10&page_no=1 - 200 null application/json; charset=utf-8 294.4923ms
2025-06-24 08:46:47.693 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menulist - null null
2025-06-24 08:46:47.695 +08:00 [INF] 请求开始: GET /api/sys/menulist - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.697 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:47.698 +08:00 [INF] Route matched with {action = "GetMenuList", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetMenuList() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:47.895 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - null null
2025-06-24 08:46:47.897 +08:00 [INF] 请求开始: GET /api/sys/menus?title= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:47.899 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:47.899 +08:00 [INF] Route matched with {action = "GetMenus", controller = "Menu"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetMenus() on controller WcsNet.Controllers.MenuController (WcsNet).
2025-06-24 08:46:48.045 +08:00 [INF] Executed DbCommand (61ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`title` AS `Title`, `s`.`parent_id` AS `ParentId`, `s`.`perm_type` AS `PermType`, `s`.`icon` AS `Icon`, `s`.`rank` AS `Rank`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:48.048 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:48.049 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenuList (WcsNet) in 350.8985ms
2025-06-24 08:46:48.050 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenuList (WcsNet)'
2025-06-24 08:46:48.051 +08:00 [INF] 请求完成: GET /api/sys/menulist - 状态码: 200 - 耗时: 356ms - 响应大小: 684bytes
2025-06-24 08:46:48.053 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menulist - 200 null application/json; charset=utf-8 359.2649ms
2025-06-24 08:46:48.171 +08:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE `s`.`perm_type` < 3
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:46:48.174 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:48.178 +08:00 [INF] Executed action WcsNet.Controllers.MenuController.GetMenus (WcsNet) in 277.9193ms
2025-06-24 08:46:48.181 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.MenuController.GetMenus (WcsNet)'
2025-06-24 08:46:48.183 +08:00 [INF] 请求完成: GET /api/sys/menus?title= - 状态码: 200 - 耗时: 285ms - 响应大小: 1688bytes
2025-06-24 08:46:48.187 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/menus?title= - 200 null application/json; charset=utf-8 291.7004ms
2025-06-24 08:46:49.702 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - null null
2025-06-24 08:46:49.703 +08:00 [INF] 请求开始: GET /api/sys/depts?name=&status= - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:46:49.704 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:49.705 +08:00 [INF] Route matched with {action = "GetDepts", controller = "Dept"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[System.Object]]]] GetDepts() on controller WcsNet.Controllers.DeptController (WcsNet).
2025-06-24 08:46:50.096 +08:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`Ancestors`, `s`.`Brief`, `s`.`created_at`, `s`.`created_id`, `s`.`dept_code`, `s`.`dept_name`, `s`.`Leader`, `s`.`parent_id`, `s`.`sort_number`, `s`.`Status`, `s`.`updated_at`, `s`.`updated_id`
FROM `sys_department` AS `s`
ORDER BY `s`.`sort_number`, `s`.`Id`
2025-06-24 08:46:50.097 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:46:50.098 +08:00 [INF] Executed action WcsNet.Controllers.DeptController.GetDepts (WcsNet) in 390.7754ms
2025-06-24 08:46:50.098 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.DeptController.GetDepts (WcsNet)'
2025-06-24 08:46:50.099 +08:00 [INF] 请求完成: GET /api/sys/depts?name=&status= - 状态码: 200 - 耗时: 395ms - 响应大小: 169bytes
2025-06-24 08:46:50.100 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/depts?name=&status= - 200 null application/json; charset=utf-8 397.7352ms
2025-06-24 08:47:46.965 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:47:46.966 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:47:46.967 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:47:46.968 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:47:47.252 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:47:47.254 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:47:47.254 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 285.7793ms
2025-06-24 08:47:47.255 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:47:47.255 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 289ms - 响应大小: 1678bytes
2025-06-24 08:47:47.257 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 291.5629ms
2025-06-24 08:48:37.076 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - null null
2025-06-24 08:48:37.077 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - null null
2025-06-24 08:48:37.078 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:37.079 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_no=1&page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.081 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=7 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.082 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.087 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:37.086 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.084 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.088 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:37.090 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.091 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.094 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.096 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.098 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.099 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 5.2583ms
2025-06-24 08:48:37.102 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:37.103 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 20ms - 响应大小: 1597bytes
2025-06-24 08:48:37.100 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 4.2934ms
2025-06-24 08:48:37.106 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.105 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 27.5184ms
2025-06-24 08:48:37.101 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 3.4746ms
2025-06-24 08:48:37.112 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.113 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_no=1&page_size=10 - 状态码: 200 - 耗时: 33ms - 响应大小: 848bytes
2025-06-24 08:48:37.107 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=7 - 状态码: 200 - 耗时: 26ms - 响应大小: 619bytes
2025-06-24 08:48:37.114 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_no=1&page_size=10 - 200 null application/json; charset=utf-8 37.8794ms
2025-06-24 08:48:37.116 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=7 - 200 null application/json; charset=utf-8 38.7337ms
2025-06-24 08:48:37.389 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - null null
2025-06-24 08:48:37.392 +08:00 [INF] 请求开始: GET /api/stat/tasks?page_size=14 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:37.393 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.394 +08:00 [INF] Route matched with {action = "GetTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:37.396 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:37.397 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetTaskStats (WcsNet) in 1.3658ms
2025-06-24 08:48:37.398 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetTaskStats (WcsNet)'
2025-06-24 08:48:37.399 +08:00 [INF] 请求完成: GET /api/stat/tasks?page_size=14 - 状态码: 200 - 耗时: 6ms - 响应大小: 1146bytes
2025-06-24 08:48:37.400 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/tasks?page_size=14 - 200 null application/json; charset=utf-8 10.7164ms
2025-06-24 08:48:39.044 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - null null
2025-06-24 08:48:39.046 +08:00 [INF] 请求开始: GET /api/stat/weektasks/0 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:39.048 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.049 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:39.050 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:39.052 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.2538ms
2025-06-24 08:48:39.052 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.053 +08:00 [INF] 请求完成: GET /api/stat/weektasks/0 - 状态码: 200 - 耗时: 7ms - 响应大小: 618bytes
2025-06-24 08:48:39.055 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/0 - 200 null application/json; charset=utf-8 10.637ms
2025-06-24 08:48:39.076 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - null null
2025-06-24 08:48:39.078 +08:00 [INF] 请求开始: GET /api/stat/weektasks/1 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:39.079 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.080 +08:00 [INF] Route matched with {action = "GetWeekTaskStats", controller = "Stat"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.StatTaskDto]]] GetWeekTaskStats(Int32) on controller WcsNet.Controllers.StatController (WcsNet).
2025-06-24 08:48:39.082 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:39.083 +08:00 [INF] Executed action WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet) in 1.1801ms
2025-06-24 08:48:39.084 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.StatController.GetWeekTaskStats (WcsNet)'
2025-06-24 08:48:39.084 +08:00 [INF] 请求完成: GET /api/stat/weektasks/1 - 状态码: 200 - 耗时: 6ms - 响应大小: 616bytes
2025-06-24 08:48:39.086 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/stat/weektasks/1 - 200 null application/json; charset=utf-8 9.9668ms
2025-06-24 08:48:47.505 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:47.508 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:47.510 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:47.511 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:47.513 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:47.514 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6549ms
2025-06-24 08:48:47.516 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:47.516 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 8ms - 响应大小: 1598bytes
2025-06-24 08:48:47.519 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 14.0511ms
2025-06-24 08:48:57.182 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:48:57.183 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:48:57.184 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:57.185 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:48:57.186 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:48:57.186 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7848ms
2025-06-24 08:48:57.187 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:48:57.188 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:48:57.189 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 7.1545ms
2025-06-24 08:49:07.265 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:07.267 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:07.268 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:07.268 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:07.269 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:07.270 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7595ms
2025-06-24 08:49:07.270 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:07.271 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:49:07.272 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.7262ms
2025-06-24 08:49:17.182 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:17.183 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:17.184 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:17.185 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:17.186 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:17.186 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6696ms
2025-06-24 08:49:17.187 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:17.187 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:49:17.188 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.4589ms
2025-06-24 08:49:27.493 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:27.495 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:27.496 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:27.496 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:27.497 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:27.498 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7355ms
2025-06-24 08:49:27.498 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:27.498 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1597bytes
2025-06-24 08:49:27.499 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.0691ms
2025-06-24 08:49:37.197 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:37.215 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:37.216 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:37.217 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:37.220 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:37.222 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.6912ms
2025-06-24 08:49:37.223 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:37.225 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 10ms - 响应大小: 1599bytes
2025-06-24 08:49:37.226 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 29.3963ms
2025-06-24 08:49:47.495 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:47.497 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:47.498 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:47.498 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:47.499 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:47.499 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6224ms
2025-06-24 08:49:47.500 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:47.500 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1598bytes
2025-06-24 08:49:47.501 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.8744ms
2025-06-24 08:49:57.188 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:49:57.190 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:49:57.190 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:57.191 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:49:57.192 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:49:57.192 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6626ms
2025-06-24 08:49:57.193 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:49:57.194 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:49:57.195 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.7972ms
2025-06-24 08:50:07.511 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:07.517 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:07.528 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:07.531 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:07.538 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:07.542 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 4.2568ms
2025-06-24 08:50:07.544 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:07.547 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 29ms - 响应大小: 1598bytes
2025-06-24 08:50:07.550 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 39.5603ms
2025-06-24 08:50:16.963 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:16.965 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:16.965 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:16.966 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:16.967 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:16.967 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.8067ms
2025-06-24 08:50:16.968 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:16.969 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:50:16.970 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.5853ms
2025-06-24 08:50:27.275 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:27.277 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:27.278 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:27.279 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:27.280 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:27.282 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.4351ms
2025-06-24 08:50:27.282 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:27.283 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 6ms - 响应大小: 1599bytes
2025-06-24 08:50:27.285 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 10.0949ms
2025-06-24 08:50:37.195 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:37.197 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:37.198 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:37.201 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:37.202 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:37.204 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.3285ms
2025-06-24 08:50:37.205 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:37.206 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 9ms - 响应大小: 1597bytes
2025-06-24 08:50:37.207 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 12.2231ms
2025-06-24 08:50:47.507 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:47.508 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:47.509 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:47.509 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:47.510 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:47.511 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6181ms
2025-06-24 08:50:47.511 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:47.512 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1597bytes
2025-06-24 08:50:47.512 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.7014ms
2025-06-24 08:50:57.185 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:50:57.186 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:50:57.188 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:57.188 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:50:57.189 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:50:57.190 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7302ms
2025-06-24 08:50:57.190 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:50:57.191 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1599bytes
2025-06-24 08:50:57.191 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.2984ms
2025-06-24 08:51:07.492 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:07.493 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:07.494 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:07.495 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:07.496 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:07.496 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.6188ms
2025-06-24 08:51:07.497 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:07.497 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 3ms - 响应大小: 1598bytes
2025-06-24 08:51:07.498 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 5.7081ms
2025-06-24 08:51:17.192 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:17.194 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:17.194 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:17.195 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:17.196 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:17.197 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 1.0424ms
2025-06-24 08:51:17.197 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:17.198 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1596bytes
2025-06-24 08:51:17.199 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.2322ms
2025-06-24 08:51:27.484 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - null null
2025-06-24 08:51:27.486 +08:00 [INF] 请求开始: GET /api/alarms?page_size=10 - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-06-24 08:51:27.487 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:27.487 +08:00 [INF] Route matched with {action = "GetAlarms", controller = "Alarm"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[WcsNet.DTOs.PagedResponse`1[WcsNet.DTOs.AlarmStatDto]]] GetAlarms(Int32) on controller WcsNet.Controllers.AlarmController (WcsNet).
2025-06-24 08:51:27.488 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.AlarmStatDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]]'.
2025-06-24 08:51:27.489 +08:00 [INF] Executed action WcsNet.Controllers.AlarmController.GetAlarms (WcsNet) in 0.7387ms
2025-06-24 08:51:27.489 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.AlarmController.GetAlarms (WcsNet)'
2025-06-24 08:51:27.490 +08:00 [INF] 请求完成: GET /api/alarms?page_size=10 - 状态码: 200 - 耗时: 4ms - 响应大小: 1598bytes
2025-06-24 08:51:27.491 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/alarms?page_size=10 - 200 null application/json; charset=utf-8 6.6544ms
2025-06-24 08:52:28.191 +08:00 [INF] Request starting HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - null null
2025-06-24 08:52:28.193 +08:00 [INF] 请求开始: GET /api/sys/rolemenus - IP: ::1 - User: Anonymous(Anonymous) - UserAgent: Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.26100.4202
2025-06-24 08:52:28.195 +08:00 [INF] Executing endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:52:28.195 +08:00 [INF] Route matched with {action = "GetRoleMenus", controller = "Sys"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult`1[WcsNet.DTOs.ApiResponse`1[System.Collections.Generic.List`1[WcsNet.DTOs.MenuDto]]]] GetRoleMenus() on controller WcsNet.Controllers.SysController (WcsNet).
2025-06-24 08:52:28.657 +08:00 [INF] Executed DbCommand (38ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT `s`.`Id`, `s`.`active_path`, `s`.`component`, `s`.`extra_icon`, `s`.`fixed_tag`, `s`.`frame_loading`, `s`.`frame_src`, `s`.`hide_tag`, `s`.`icon`, `s`.`keepalive`, `s`.`name`, `s`.`parent_id`, `s`.`perm_code`, `s`.`perm_type`, `s`.`rank`, `s`.`redirect`, `s`.`route_path`, `s`.`show_link`, `s`.`show_parent`, `s`.`title`
FROM `sys_permission` AS `s`
WHERE (`s`.`perm_type` < 3) AND (`s`.`show_link` = 1)
ORDER BY `s`.`rank`, `s`.`Id`
2025-06-24 08:52:28.658 +08:00 [INF] Executing OkObjectResult, writing value of type 'WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=*******, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-06-24 08:52:28.659 +08:00 [INF] Executed action WcsNet.Controllers.SysController.GetRoleMenus (WcsNet) in 461.9914ms
2025-06-24 08:52:28.659 +08:00 [INF] Executed endpoint 'WcsNet.Controllers.SysController.GetRoleMenus (WcsNet)'
2025-06-24 08:52:28.660 +08:00 [INF] 请求完成: GET /api/sys/rolemenus - 状态码: 200 - 耗时: 466ms - 响应大小: 1678bytes
2025-06-24 08:52:28.661 +08:00 [INF] Request finished HTTP/1.1 GET http://localhost:8666/api/sys/rolemenus - 200 null application/json; charset=utf-8 469.3652ms
