using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 菜单管理控制器
/// </summary>
[ApiController]
[Route("api/sys")]
public class MenuController : ControllerBase
{
    private readonly WcsDbContext _context;

    public MenuController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取菜单列表（树形结构）
    /// </summary>
    /// <returns>菜单树</returns>
    [HttpGet("menus")]
    public async Task<ActionResult<ApiResponse<List<MenuDto>>>> GetMenus()
    {
        try
        {
            var permissions = await _context.Permissions
                .Where(p => p.PermType < 3) // 只返回菜单类型，不包括按钮
                .OrderBy(p => p.Rank)
                .ThenBy(p => p.Id)
                .ToListAsync();

            var menuTree = BuildMenuDtoTree(permissions, 0);

            return Ok(ApiResponse<List<MenuDto>>.Success(menuTree));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<MenuDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取菜单列表（用于角色权限分配）
    /// </summary>
    /// <returns>菜单列表</returns>
    [HttpGet("menulist")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetMenuList()
    {
        try
        {
            var permissions = await _context.Permissions
                .Where(p => p.PermType < 3) // 只返回菜单类型，不包括按钮
                .OrderBy(p => p.Rank)
                .ThenBy(p => p.Id)
                .Select(p => new
                {
                    p.Id,
                    p.Title,
                    p.ParentId,
                    p.PermType,
                    p.Icon,
                    p.Rank
                })
                .ToListAsync();

            var menuTree = BuildMenuTree(permissions.Cast<object>().ToList(), 0);

            return Ok(ApiResponse<List<object>>.Success(menuTree));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建菜单
    /// </summary>
    /// <param name="request">菜单信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("menus")]
    public async Task<ActionResult<ApiResponse<string>>> CreateMenu([FromBody] CreateMenuRequest request)
    {
        try
        {
            var permission = new Permission
            {
                PermType = request.PermType,
                Title = request.Title,
                ParentId = request.ParentId,
                RoutePath = request.RoutePath ?? "",
                Name = request.Name ?? "",
                Component = request.Component ?? "",
                Redirect = request.Redirect ?? "",
                Rank = request.Rank,
                Icon = request.Icon ?? "",
                ExtraIcon = request.ExtraIcon ?? "",
                ActivePath = request.ActivePath ?? "",
                FrameSrc = request.FrameSrc ?? "",
                FrameLoading = request.FrameLoading ?? 1,
                ShowLink = request.ShowLink ?? 1,
                HideTag = request.HideTag ?? 0,
                Keepalive = request.Keepalive ?? 0,
                FixedTag = request.FixedTag ?? 0,
                ShowParent = request.ShowParent ?? 0,
                PermCode = request.PermCode ?? ""
            };

            _context.Permissions.Add(permission);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("菜单创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <param name="request">菜单信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("menus/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateMenu(uint id, [FromBody] UpdateMenuRequest request)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(id);
            if (permission == null)
            {
                return Ok(ApiResponse<string>.Error(404, "菜单不存在"));
            }

            if (request.PermType.HasValue) permission.PermType = request.PermType.Value;
            if (!string.IsNullOrEmpty(request.Title)) permission.Title = request.Title;
            if (request.ParentId.HasValue) permission.ParentId = request.ParentId.Value;
            if (request.RoutePath != null) permission.RoutePath = request.RoutePath;
            if (request.Name != null) permission.Name = request.Name;
            if (request.Component != null) permission.Component = request.Component;
            if (request.Redirect != null) permission.Redirect = request.Redirect;
            if (request.Rank.HasValue) permission.Rank = request.Rank.Value;
            if (request.Icon != null) permission.Icon = request.Icon;
            if (request.ExtraIcon != null) permission.ExtraIcon = request.ExtraIcon;
            if (request.ActivePath != null) permission.ActivePath = request.ActivePath;
            if (request.FrameSrc != null) permission.FrameSrc = request.FrameSrc;
            if (request.FrameLoading.HasValue) permission.FrameLoading = request.FrameLoading.Value;
            if (request.ShowLink.HasValue) permission.ShowLink = request.ShowLink.Value;
            if (request.HideTag.HasValue) permission.HideTag = request.HideTag.Value;
            if (request.Keepalive.HasValue) permission.Keepalive = request.Keepalive.Value;
            if (request.FixedTag.HasValue) permission.FixedTag = request.FixedTag.Value;
            if (request.ShowParent.HasValue) permission.ShowParent = request.ShowParent.Value;
            if (request.PermCode != null) permission.PermCode = request.PermCode;

            // permission.UpdatedAt = DateTime.Now; // Permission实体没有UpdatedAt字段

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("菜单更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除菜单
    /// </summary>
    /// <param name="id">菜单ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("menus/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteMenu(uint id)
    {
        try
        {
            var permission = await _context.Permissions.FindAsync(id);
            if (permission == null)
            {
                return Ok(ApiResponse<string>.Error(404, "菜单不存在"));
            }

            // 检查是否有子菜单
            var childCount = await _context.Permissions.CountAsync(p => p.ParentId == id);
            if (childCount > 0)
            {
                return Ok(ApiResponse<string>.Error(400, "该菜单下还有子菜单，无法删除"));
            }

            _context.Permissions.Remove(permission);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("菜单删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 构建菜单树
    /// </summary>
    /// <param name="permissions">权限列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>菜单树</returns>
    private List<object> BuildMenuTree(List<Permission> permissions, uint parentId)
    {
        var result = new List<object>();

        foreach (var permission in permissions.Where(p => p.ParentId == parentId))
        {
            var children = BuildMenuTree(permissions, (uint)permission.Id);
            
            var menuItem = new
            {
                permission.Id,
                Title = permission.Title,
                ParentId = permission.ParentId,
                PermType = permission.PermType,
                Path = permission.RoutePath,
                permission.Name,
                permission.Component,
                permission.Redirect,
                Rank = permission.Rank,
                permission.Icon,
                ExtraIcon = permission.ExtraIcon,
                ActivePath = permission.ActivePath,
                FrameSrc = permission.FrameSrc,
                FrameLoading = permission.FrameLoading,
                ShowLink = permission.ShowLink,
                HideTag = permission.HideTag,
                Keepalive = permission.Keepalive,
                FixedTag = permission.FixedTag,
                ShowParent = permission.ShowParent,
                PermCode = permission.PermCode,
                Children = children
            };

            result.Add(menuItem);
        }

        return result;
    }

    /// <summary>
    /// 构建菜单树（通用版本）
    /// </summary>
    /// <param name="items">项目列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>菜单树</returns>
    private List<object> BuildMenuTree(List<object> items, uint parentId)
    {
        var result = new List<object>();

        foreach (dynamic item in items)
        {
            if (item.ParentId == parentId)
            {
                var children = BuildMenuTree(items, item.Id);
                
                // 创建新的对象，包含children属性
                var menuItem = new
                {
                    item.Id,
                    item.Title,
                    item.ParentId,
                    item.PermType,
                    item.Icon,
                    item.Rank,
                    Children = children
                };

                result.Add(menuItem);
            }
        }

        return result;
    }

    /// <summary>
    /// 构建菜单DTO树（与Go版本MenuList保持一致）
    /// </summary>
    /// <param name="permissions">权限列表</param>
    /// <param name="parentId">父级ID</param>
    /// <returns>菜单DTO树</returns>
    private List<MenuDto> BuildMenuDtoTree(List<Permission> permissions, uint parentId)
    {
        var result = new List<MenuDto>();

        foreach (var permission in permissions.Where(p => p.ParentId == parentId))
        {
            var children = BuildMenuDtoTree(permissions, (uint)permission.Id);

            var menuDto = new MenuDto
            {
                Id = (int)permission.Id,
                ParentId = (int)permission.ParentId,
                Path = permission.RoutePath ?? "",
                Redirect = permission.Redirect,
                Name = permission.Name,
                Component = permission.Component,
                Meta = new MenuMeta
                {
                    Icon = permission.Icon ?? "",
                    Title = permission.Title,
                    FrameSrc = permission.FrameSrc,
                    KeepAlive = permission.Keepalive == 1,
                    Rank = permission.Rank,
                    ShowParent = permission.ShowParent == 1,
                    ShowLink = permission.ShowLink == 1,
                    ExtraIcon = permission.ExtraIcon,
                    HideTag = permission.HideTag == 1,
                    FixedTag = permission.FixedTag == 1
                },
                Children = children.Count > 0 ? children : null
            };

            result.Add(menuDto);
        }

        return result;
    }
}

/// <summary>
/// 创建菜单请求
/// </summary>
public class CreateMenuRequest
{
    public sbyte PermType { get; set; }
    public string Title { get; set; } = string.Empty;
    public uint ParentId { get; set; }
    public string? RoutePath { get; set; }
    public string? Name { get; set; }
    public string? Component { get; set; }
    public string? Redirect { get; set; }
    public int Rank { get; set; }
    public string? Icon { get; set; }
    public string? ExtraIcon { get; set; }
    public string? ActivePath { get; set; }
    public string? FrameSrc { get; set; }
    public sbyte? FrameLoading { get; set; }
    public sbyte? ShowLink { get; set; }
    public sbyte? HideTag { get; set; }
    public sbyte? Keepalive { get; set; }
    public sbyte? FixedTag { get; set; }
    public sbyte? ShowParent { get; set; }
    public string? PermCode { get; set; }
}

/// <summary>
/// 更新菜单请求
/// </summary>
public class UpdateMenuRequest
{
    public sbyte? PermType { get; set; }
    public string? Title { get; set; }
    public uint? ParentId { get; set; }
    public string? RoutePath { get; set; }
    public string? Name { get; set; }
    public string? Component { get; set; }
    public string? Redirect { get; set; }
    public int? Rank { get; set; }
    public string? Icon { get; set; }
    public string? ExtraIcon { get; set; }
    public string? ActivePath { get; set; }
    public string? FrameSrc { get; set; }
    public sbyte? FrameLoading { get; set; }
    public sbyte? ShowLink { get; set; }
    public sbyte? HideTag { get; set; }
    public sbyte? Keepalive { get; set; }
    public sbyte? FixedTag { get; set; }
    public sbyte? ShowParent { get; set; }
    public string? PermCode { get; set; }
}
