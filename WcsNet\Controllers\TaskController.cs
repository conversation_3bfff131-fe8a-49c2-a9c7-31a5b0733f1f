using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TaskController : ControllerBase
{
    private readonly WcsDbContext _context;

    public TaskController(WcsDbContext context)
    {
        _context = context;
    }
    /// <summary>
    /// 获取任务执行器分组列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>执行器分组列表</returns>
    [HttpGet("actgroups")]
    public async Task<ActionResult<ApiResponse<object>>> GetActGroups(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取执行器分组数据
            var total = await _context.ActuatorGroups.CountAsync();
            var actGroups = await _context.ActuatorGroups
                .OrderBy(ag => ag.SortNumber)
                .ThenBy(ag => ag.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(ag => new
                {
                    ag.Id,
                    GroupName = ag.GroupName,
                    GroupType = ag.GroupType,
                    SortNumber = ag.SortNumber,
                    ag.Remark,
                    ag.Status,
                    CreatedAt = ag.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new
            {
                list = actGroups,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建执行器分组
    /// </summary>
    /// <param name="request">执行器分组信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("actgroups")]
    public async Task<ActionResult<ApiResponse<string>>> CreateActGroup([FromBody] CreateActGroupRequest request)
    {
        try
        {
            var actGroup = new ActuatorGroup
            {
                GroupName = request.GroupName,
                GroupType = request.GroupType,
                SortNumber = request.SortNumber,
                Remark = request.Remark ?? "",
                Status = request.Status ?? 1
            };

            _context.ActuatorGroups.Add(actGroup);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("执行器分组创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新执行器分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <param name="request">执行器分组信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("actgroups/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateActGroup(ulong id, [FromBody] UpdateActGroupRequest request)
    {
        try
        {
            var actGroup = await _context.ActuatorGroups.FindAsync(id);
            if (actGroup == null)
            {
                return Ok(ApiResponse<string>.Error(404, "执行器分组不存在"));
            }

            if (!string.IsNullOrEmpty(request.GroupName)) actGroup.GroupName = request.GroupName;
            if (!string.IsNullOrEmpty(request.GroupType)) actGroup.GroupType = request.GroupType;
            if (request.SortNumber.HasValue) actGroup.SortNumber = request.SortNumber.Value;
            if (request.Remark != null) actGroup.Remark = request.Remark;
            if (request.Status.HasValue) actGroup.Status = request.Status.Value;

            actGroup.UpdatedAt = DateTime.Now;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("执行器分组更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除执行器分组
    /// </summary>
    /// <param name="id">分组ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("actgroups/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteActGroup(ulong id)
    {
        try
        {
            var actGroup = await _context.ActuatorGroups.FindAsync(id);
            if (actGroup == null)
            {
                return Ok(ApiResponse<string>.Error(404, "执行器分组不存在"));
            }

            _context.ActuatorGroups.Remove(actGroup);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("执行器分组删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取执行器列表
    /// </summary>
    /// <param name="act_name">执行器名称</param>
    /// <param name="group_id">分组ID</param>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>执行器列表</returns>
    [HttpGet("acts")]
    public async Task<ActionResult<ApiResponse<object>>> GetActs(
        string? act_name = null,
        ulong? group_id = null,
        int page_size = 10,
        int page_no = 1)
    {
        try
        {
            // 从数据库获取执行器数据
            var query = _context.Actuators.AsQueryable();

            // 按名称过滤
            if (!string.IsNullOrEmpty(act_name))
            {
                query = query.Where(a => a.ActName.Contains(act_name));
            }

            // 按分组过滤
            if (group_id.HasValue)
            {
                query = query.Where(a => a.GroupId == group_id.Value);
            }

            var total = await query.CountAsync();
            var acts = await query
                .OrderBy(a => a.Level)
                .ThenBy(a => a.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(a => new
                {
                    a.Id,
                    ActName = a.ActName,
                    a.GroupId,
                    GroupName = _context.ActuatorGroups
                        .Where(ag => ag.Id == a.GroupId)
                        .Select(ag => ag.GroupName)
                        .FirstOrDefault() ?? "",
                    a.ActType,
                    a.Level,
                    a.Remark,
                    CreatedAt = a.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new
            {
                list = acts,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建执行器
    /// </summary>
    /// <param name="request">执行器信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("acts")]
    public async Task<ActionResult<ApiResponse<string>>> CreateAct([FromBody] CreateActRequest request)
    {
        try
        {
            var act = new Actuator
            {
                ActName = request.ActName,
                GroupId = request.GroupId,
                ActType = request.ActType,
                Level = request.Level,
                Remark = request.Remark ?? ""
            };

            _context.Actuators.Add(act);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("执行器创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新执行器
    /// </summary>
    /// <param name="id">执行器ID</param>
    /// <param name="request">执行器信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("acts/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateAct(ulong id, [FromBody] UpdateActRequest request)
    {
        try
        {
            var act = await _context.Actuators.FindAsync(id);
            if (act == null)
            {
                return Ok(ApiResponse<string>.Error(404, "执行器不存在"));
            }

            if (!string.IsNullOrEmpty(request.ActName)) act.ActName = request.ActName;
            if (request.GroupId.HasValue) act.GroupId = request.GroupId.Value;
            if (request.ActType.HasValue) act.ActType = request.ActType.Value;
            if (request.Level.HasValue) act.Level = request.Level.Value;
            if (request.Remark != null) act.Remark = request.Remark;

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("执行器更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取路径定义列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>路径定义列表</returns>
    [HttpGet("paths")]
    public async Task<ActionResult<ApiResponse<object>>> GetPaths(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取路径定义数据
            var total = await _context.PathDefines.CountAsync();
            var paths = await _context.PathDefines
                .OrderBy(p => p.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(p => new
                {
                    p.Id,
                    PathName = p.PathName,
                    PathCode = $"PATH_{p.Id:D3}", // 生成路径编码
                    StartPoint = p.StartPoint,
                    EndPoint = p.EndPoint,
                    TransitTime = p.TransitTime,
                    BlockTime = p.BlockTime,
                    Status = 1, // 默认状态为正常
                    CreateTime = p.CreatedAt ?? DateTime.Now,
                    UpdateTime = p.UpdatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new
            {
                list = paths,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建路径定义
    /// </summary>
    /// <param name="request">路径信息</param>
    /// <returns>创建结果</returns>
    [HttpPost("paths")]
    public async Task<ActionResult<ApiResponse<string>>> CreatePath([FromBody] CreatePathRequest request)
    {
        try
        {
            var path = new PathDefine
            {
                PathName = request.PathName,
                StartPoint = request.StartPoint,
                EndPoint = request.EndPoint,
                TransitTime = request.TransitTime,
                BlockTime = request.BlockTime
            };

            _context.PathDefines.Add(path);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("路径创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新路径定义
    /// </summary>
    /// <param name="id">路径ID</param>
    /// <param name="request">路径信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("paths/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdatePath(ulong id, [FromBody] UpdatePathRequest request)
    {
        try
        {
            var path = await _context.PathDefines.FindAsync(id);
            if (path == null)
            {
                return Ok(ApiResponse<string>.Error(404, "路径不存在"));
            }

            if (!string.IsNullOrEmpty(request.PathName)) path.PathName = request.PathName;
            if (!string.IsNullOrEmpty(request.StartPoint)) path.StartPoint = request.StartPoint;
            if (!string.IsNullOrEmpty(request.EndPoint)) path.EndPoint = request.EndPoint;
            if (request.TransitTime.HasValue) path.TransitTime = request.TransitTime.Value;
            if (request.BlockTime.HasValue) path.BlockTime = request.BlockTime.Value;

            path.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("路径更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除路径定义
    /// </summary>
    /// <param name="id">路径ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("paths/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeletePath(ulong id)
    {
        try
        {
            var path = await _context.PathDefines.FindAsync(id);
            if (path == null)
            {
                return Ok(ApiResponse<string>.Error(404, "路径不存在"));
            }

            _context.PathDefines.Remove(path);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("路径删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>任务列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<TaskDto>>>> GetTasks(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取任务数据
            var total = await _context.Works.CountAsync();
            var tasks = await _context.Works
                .OrderByDescending(w => w.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(w => new TaskDto
                {
                    Id = (int)w.Id,
                    Name = $"任务{w.Id:D3}", // 数据库中没有WorkName字段，使用ID生成名称
                    Type = w.TaskType, // 使用TaskType字段
                    Status = w.Status,
                    Priority = 1, // 数据库中没有Priority字段，默认为1
                    CreateTime = w.CreatedAt.HasValue ? w.CreatedAt.Value : DateTime.Now,
                    UpdateTime = w.UpdatedAt.HasValue ? w.UpdatedAt.Value : DateTime.Now
                })
                .ToListAsync();

            var response = new PagedResponse<TaskDto>
            {
                List = tasks,
                Total = total
            };

            return Ok(ApiResponse<PagedResponse<TaskDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<TaskDto>>.Error(500, ex.Message));
        }
    }
}

public class CreateActGroupRequest
{
    public string GroupName { get; set; } = string.Empty;
    public string GroupType { get; set; } = string.Empty;
    public int SortNumber { get; set; } = 0;
    public string? Remark { get; set; }
    public sbyte? Status { get; set; }
}

public class UpdateActGroupRequest
{
    public string? GroupName { get; set; }
    public string? GroupType { get; set; }
    public int? SortNumber { get; set; }
    public string? Remark { get; set; }
    public sbyte? Status { get; set; }
}

public class CreatePathRequest
{
    public string PathName { get; set; } = string.Empty;
    public string StartPoint { get; set; } = string.Empty;
    public string EndPoint { get; set; } = string.Empty;
    public int TransitTime { get; set; } = 0;
    public int BlockTime { get; set; } = 0;
}

public class UpdatePathRequest
{
    public string? PathName { get; set; }
    public string? StartPoint { get; set; }
    public string? EndPoint { get; set; }
    public int? TransitTime { get; set; }
    public int? BlockTime { get; set; }
}

public class CreateActRequest
{
    public string ActName { get; set; } = string.Empty;
    public ulong GroupId { get; set; }
    public sbyte ActType { get; set; } = 0;
    public sbyte Level { get; set; } = 0;
    public string? Remark { get; set; }
}

public class UpdateActRequest
{
    public string? ActName { get; set; }
    public ulong? GroupId { get; set; }
    public sbyte? ActType { get; set; }
    public sbyte? Level { get; set; }
    public string? Remark { get; set; }
}
