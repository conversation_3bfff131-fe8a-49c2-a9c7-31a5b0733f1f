using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TaskController : ControllerBase
{
    /// <summary>
    /// 获取任务执行器分组列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>执行器分组列表</returns>
    [HttpGet("actgroups")]
    public ActionResult<ApiResponse<PagedResponse<ActGroupDto>>> GetActGroups(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟执行器分组数据
            var actGroups = new List<ActGroupDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                actGroups.Add(new ActGroupDto
                {
                    Id = i,
                    GroupName = $"执行器分组{i}",
                    GroupCode = $"ACT_GROUP_{i:D3}",
                    Description = $"执行器分组{i}的描述",
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                    UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
                });
            }

            var response = new PagedResponse<ActGroupDto>
            {
                List = actGroups,
                Total = 50 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<ActGroupDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<ActGroupDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取路径定义列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>路径定义列表</returns>
    [HttpGet("paths")]
    public ActionResult<ApiResponse<PagedResponse<PathDto>>> GetPaths(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟路径定义数据
            var paths = new List<PathDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                paths.Add(new PathDto
                {
                    Id = i,
                    PathName = $"路径{i}",
                    PathCode = $"PATH_{i:D3}",
                    StartPoint = $"起点{i}",
                    EndPoint = $"终点{i}",
                    Distance = random.Next(100, 1000),
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                    UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
                });
            }

            var response = new PagedResponse<PathDto>
            {
                List = paths,
                Total = 30 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<PathDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<PathDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <returns>任务列表</returns>
    [HttpGet]
    public ActionResult<ApiResponse<PagedResponse<TaskDto>>> GetTasks(int page_size = 10)
    {
        try
        {
            // 模拟任务数据，与Go版本格式保持一致
            var tasks = new List<TaskDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                tasks.Add(new TaskDto
                {
                    Id = i,
                    Name = $"任务{i:D3}",
                    Type = random.Next(1, 4),
                    Status = random.Next(0, 3),
                    Priority = random.Next(1, 6),
                    CreateTime = DateTime.Now.AddHours(-random.Next(1, 24)),
                    UpdateTime = DateTime.Now.AddMinutes(-random.Next(1, 60))
                });
            }

            var response = new PagedResponse<TaskDto>
            {
                List = tasks,
                Total = 100 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<TaskDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<TaskDto>>.Error(500, ex.Message));
        }
    }
}
