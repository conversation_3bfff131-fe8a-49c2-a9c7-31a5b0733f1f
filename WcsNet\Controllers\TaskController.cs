using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
public class TaskController : ControllerBase
{
    private readonly WcsDbContext _context;

    public TaskController(WcsDbContext context)
    {
        _context = context;
    }
    /// <summary>
    /// 获取任务执行器分组列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>执行器分组列表</returns>
    [HttpGet("actgroups")]
    public async Task<ActionResult<ApiResponse<object>>> GetActGroups(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取执行器分组数据
            var total = await _context.ActuatorGroups.CountAsync();
            var actGroups = await _context.ActuatorGroups
                .OrderBy(ag => ag.SortNumber)
                .ThenBy(ag => ag.Id)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(ag => new
                {
                    ag.Id,
                    GroupName = ag.GroupName,
                    GroupType = ag.GroupType,
                    SortNumber = ag.SortNumber,
                    ag.Remark,
                    ag.Status,
                    CreatedAt = ag.CreatedAt ?? DateTime.Now
                })
                .ToListAsync();

            var response = new
            {
                list = actGroups,
                total
            };

            return Ok(ApiResponse<object>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取路径定义列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>路径定义列表</returns>
    [HttpGet("paths")]
    public ActionResult<ApiResponse<PagedResponse<PathDto>>> GetPaths(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 模拟路径定义数据
            var paths = new List<PathDto>();
            var random = new Random();
            
            for (int i = 1; i <= page_size; i++)
            {
                paths.Add(new PathDto
                {
                    Id = i,
                    PathName = $"路径{i}",
                    PathCode = $"PATH_{i:D3}",
                    StartPoint = $"起点{i}",
                    EndPoint = $"终点{i}",
                    Distance = random.Next(100, 1000),
                    Status = random.Next(0, 2),
                    CreateTime = DateTime.Now.AddDays(-random.Next(1, 30)),
                    UpdateTime = DateTime.Now.AddHours(-random.Next(1, 24))
                });
            }

            var response = new PagedResponse<PathDto>
            {
                List = paths,
                Total = 30 // 模拟总数
            };

            return Ok(ApiResponse<PagedResponse<PathDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<PathDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取任务列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <returns>任务列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<TaskDto>>>> GetTasks(int page_size = 10, int page_no = 1)
    {
        try
        {
            // 从数据库获取任务数据
            var total = await _context.Works.CountAsync();
            var tasks = await _context.Works
                .OrderByDescending(w => w.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(w => new TaskDto
                {
                    Id = (int)w.Id,
                    Name = $"任务{w.Id:D3}", // 数据库中没有WorkName字段，使用ID生成名称
                    Type = w.TaskType, // 使用TaskType字段
                    Status = w.Status,
                    Priority = 1, // 数据库中没有Priority字段，默认为1
                    CreateTime = w.CreatedAt.HasValue ? w.CreatedAt.Value : DateTime.Now,
                    UpdateTime = w.UpdatedAt.HasValue ? w.UpdatedAt.Value : DateTime.Now
                })
                .ToListAsync();

            var response = new PagedResponse<TaskDto>
            {
                List = tasks,
                Total = total
            };

            return Ok(ApiResponse<PagedResponse<TaskDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<TaskDto>>.Error(500, ex.Message));
        }
    }
}
