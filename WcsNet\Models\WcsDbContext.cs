using Microsoft.EntityFrameworkCore;

namespace WcsNet.Models;

public class WcsDbContext : DbContext
{
    public WcsDbContext(DbContextOptions<WcsDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<Work> Works { get; set; }
    public DbSet<WorkStep> WorkSteps { get; set; }
    public DbSet<Stock> Stocks { get; set; }
    public DbSet<Lane> Lanes { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置User实体
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("sys_user");
            entity.HasIndex(e => e.Account).IsUnique();
        });

        // 配置Department实体
        modelBuilder.Entity<Department>(entity =>
        {
            entity.ToTable("sys_department");
            entity.HasIndex(e => new { e.ParentId, e.DeptName }).IsUnique();
        });

        // 配置Device实体
        modelBuilder.Entity<Device>(entity =>
        {
            entity.ToTable("wcs_device");
            entity.HasIndex(e => e.DeviceCode).IsUnique();
        });

        // 配置Work实体
        modelBuilder.Entity<Work>(entity =>
        {
            entity.ToTable("wcs_work");
            entity.HasIndex(e => e.ItemCode);
        });

        // 配置WorkStep实体
        modelBuilder.Entity<WorkStep>(entity =>
        {
            entity.ToTable("wcs_work_step");
            entity.HasOne(e => e.Work)
                  .WithMany()
                  .HasForeignKey(e => e.WorkId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置Stock实体
        modelBuilder.Entity<Stock>(entity =>
        {
            entity.ToTable("wcs_stock");
            entity.HasIndex(e => e.StockCode).IsUnique();
        });

        // 配置Lane实体
        modelBuilder.Entity<Lane>(entity =>
        {
            entity.ToTable("wcs_lane");
            entity.HasIndex(e => e.LaneCode).IsUnique();
        });
    }


}
