using Microsoft.EntityFrameworkCore;

namespace WcsNet.Models;

public class WcsDbContext : DbContext
{
    public WcsDbContext(DbContextOptions<WcsDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<DeviceProperty> DeviceProperties { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<Work> Works { get; set; }
    public DbSet<WorkStep> WorkSteps { get; set; }
    public DbSet<Stock> Stocks { get; set; }
    public DbSet<Lane> Lanes { get; set; }
    public DbSet<Dict> Dicts { get; set; }
    public DbSet<DictValue> DictValues { get; set; }
    public DbSet<LoginLog> LoginLogs { get; set; }
    public DbSet<OperationLog> OperationLogs { get; set; }
    public DbSet<Alarm> Alarms { get; set; }
    public DbSet<ApiLog> ApiLogs { get; set; }
    public DbSet<ActuatorGroup> ActuatorGroups { get; set; }
    public DbSet<Actuator> Actuators { get; set; }
    public DbSet<PathDefine> PathDefines { get; set; }
    public DbSet<Parameter> Parameters { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // 配置User实体
        modelBuilder.Entity<User>(entity =>
        {
            entity.ToTable("sys_user");
            entity.HasIndex(e => e.Account).IsUnique();
        });

        // 配置Role实体
        modelBuilder.Entity<Role>(entity =>
        {
            entity.ToTable("sys_role");
            entity.HasIndex(e => e.RoleCode).IsUnique();
        });

        // 配置Department实体
        modelBuilder.Entity<Department>(entity =>
        {
            entity.ToTable("sys_department");
            entity.HasIndex(e => new { e.ParentId, e.DeptName }).IsUnique();
        });

        // 配置Device实体
        modelBuilder.Entity<Device>(entity =>
        {
            entity.ToTable("wcs_device");
            entity.HasIndex(e => e.DeviceCode).IsUnique();
        });

        // 配置Work实体
        modelBuilder.Entity<Work>(entity =>
        {
            entity.ToTable("wcs_work");
            entity.HasIndex(e => e.ItemCode);
        });

        // 配置WorkStep实体
        modelBuilder.Entity<WorkStep>(entity =>
        {
            entity.ToTable("wcs_work_step");
            entity.HasOne(e => e.Work)
                  .WithMany()
                  .HasForeignKey(e => e.WorkId)
                  .OnDelete(DeleteBehavior.Cascade);
        });

        // 配置Stock实体
        modelBuilder.Entity<Stock>(entity =>
        {
            entity.ToTable("wcs_stock");
            entity.HasIndex(e => e.StockCode).IsUnique();
        });

        // 配置Lane实体
        modelBuilder.Entity<Lane>(entity =>
        {
            entity.ToTable("wcs_lane");
            entity.HasIndex(e => e.LaneCode).IsUnique();
        });

        // 配置Dict实体
        modelBuilder.Entity<Dict>(entity =>
        {
            entity.ToTable("sys_dict");
            entity.HasIndex(e => e.DictCode).IsUnique();
        });

        // 配置DictValue实体
        modelBuilder.Entity<DictValue>(entity =>
        {
            entity.ToTable("sys_dict_value");
            entity.HasIndex(e => new { e.DictId, e.DictVal }).IsUnique();
        });

        // 配置LoginLog实体
        modelBuilder.Entity<LoginLog>(entity =>
        {
            entity.ToTable("log_login");
            entity.HasIndex(e => e.Username);
        });

        // 配置OperationLog实体
        modelBuilder.Entity<OperationLog>(entity =>
        {
            entity.ToTable("log_operation");
            entity.HasIndex(e => e.Module);
        });

        // 配置Alarm实体
        modelBuilder.Entity<Alarm>(entity =>
        {
            entity.ToTable("log_alarm");
            entity.HasIndex(e => e.DeviceId);
            entity.HasIndex(e => e.AlarmType);
        });

        // 配置ApiLog实体
        modelBuilder.Entity<ApiLog>(entity =>
        {
            entity.ToTable("wcs_api_log");
            entity.HasIndex(e => e.WorkId);
            entity.HasIndex(e => e.ApiName);
        });

        // 配置Parameter实体
        modelBuilder.Entity<Parameter>(entity =>
        {
            entity.ToTable("sys_parameter");
            entity.HasIndex(e => e.ConfKey).IsUnique();
        });
    }


}
