using Microsoft.EntityFrameworkCore;
using WcsNet.Models;

namespace WcsNet.Services;

/// <summary>
/// 字典服务
/// </summary>
public class DictService : BaseService<Dict>
{
    public DictService(WcsDbContext context) : base(context)
    {
    }

    /// <summary>
    /// 根据字典编码查询字典
    /// </summary>
    /// <param name="dictCode">字典编码</param>
    /// <returns>字典信息</returns>
    public async Task<Dict?> GetByCodeAsync(string dictCode)
    {
        return await Context.Dicts
            .FirstOrDefaultAsync(d => d.DictCode == dictCode && d.Status == 1);
    }

    /// <summary>
    /// 检查字典编码是否存在
    /// </summary>
    /// <param name="dictCode">字典编码</param>
    /// <param name="excludeId">排除的ID</param>
    /// <returns>是否存在</returns>
    public async Task<bool> IsCodeExistAsync(string dictCode, ulong? excludeId = null)
    {
        var query = Context.Dicts.Where(d => d.DictCode == dictCode);

        if (excludeId.HasValue)
        {
            query = query.Where(d => d.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    /// <summary>
    /// 获取字典值列表
    /// </summary>
    /// <param name="dictId">字典ID</param>
    /// <returns>字典值列表</returns>
    public async Task<List<DictValue>> GetDictValuesAsync(ulong dictId)
    {
        return await Context.DictValues
            .Where(dv => dv.DictId == dictId && dv.Status == 1)
            .OrderBy(dv => dv.SortNumber)
            .ThenBy(dv => dv.Id)
            .ToListAsync();
    }

    /// <summary>
    /// 根据字典编码获取字典值列表
    /// </summary>
    /// <param name="dictCode">字典编码</param>
    /// <returns>字典值列表</returns>
    public async Task<List<DictValue>> GetDictValuesByCodeAsync(string dictCode)
    {
        return await Context.DictValues
            .Include(dv => dv.Dict)
            .Where(dv => dv.Dict!.DictCode == dictCode && dv.Dict.Status == 1 && dv.Status == 1)
            .OrderBy(dv => dv.SortNumber)
            .ThenBy(dv => dv.Id)
            .ToListAsync();
    }

    /// <summary>
    /// 创建字典值
    /// </summary>
    /// <param name="dictValue">字典值信息</param>
    /// <returns>创建的字典值</returns>
    public async Task<DictValue> CreateDictValueAsync(DictValue dictValue)
    {
        // 检查字典值是否已存在
        var exists = await Context.DictValues
            .AnyAsync(dv => dv.DictId == dictValue.DictId && dv.Value == dictValue.Value);

        if (exists)
        {
            throw new InvalidOperationException("字典值已存在");
        }

        Context.DictValues.Add(dictValue);
        await Context.SaveChangesAsync();

        return dictValue;
    }

    /// <summary>
    /// 更新字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>是否成功</returns>
    public async Task<bool> UpdateDictValueAsync(ulong id, Dictionary<string, object> updates)
    {
        var dictValue = await Context.DictValues.FindAsync(id);
        if (dictValue == null)
        {
            return false;
        }

        // 如果更新字典值，检查是否重复
        if (updates.ContainsKey("Value"))
        {
            var newValue = updates["Value"].ToString();
            var exists = await Context.DictValues
                .AnyAsync(dv => dv.DictId == dictValue.DictId &&
                               dv.Value == newValue &&
                               dv.Id != id);

            if (exists)
            {
                throw new InvalidOperationException("字典值已存在");
            }
        }

        // 更新字段
        foreach (var update in updates)
        {
            var property = typeof(DictValue).GetProperty(update.Key);
            if (property != null && property.CanWrite)
            {
                var value = Convert.ChangeType(update.Value, property.PropertyType);
                property.SetValue(dictValue, value);
            }
        }

        dictValue.UpdatedAt = DateTime.Now;
        await _context.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// 删除字典值
    /// </summary>
    /// <param name="id">字典值ID</param>
    /// <returns>是否成功</returns>
    public async Task<bool> DeleteDictValueAsync(ulong id)
    {
        var dictValue = await _context.DictValues.FindAsync(id);
        if (dictValue == null)
        {
            return false;
        }

        _context.DictValues.Remove(dictValue);
        await _context.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// 删除字典及其所有字典值
    /// </summary>
    /// <param name="id">字典ID</param>
    /// <returns>是否成功</returns>
    public override async Task<bool> DeleteAsync(ulong id)
    {
        // 先删除所有字典值
        var dictValues = await _context.DictValues.Where(dv => dv.DictId == id).ToListAsync();
        _context.DictValues.RemoveRange(dictValues);

        // 再删除字典
        return await base.DeleteAsync(id);
    }

    /// <summary>
    /// 搜索字典
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>分页结果</returns>
    public async Task<(List<Dict> items, int total)> SearchAsync(string? keyword, int pageIndex, int pageSize)
    {
        var query = _context.Dicts.AsQueryable();

        if (!string.IsNullOrEmpty(keyword))
        {
            query = query.Where(d => d.DictName.Contains(keyword) || d.DictCode.Contains(keyword));
        }

        var total = await query.CountAsync();
        var items = await query
            .OrderBy(d => d.Id)
            .Skip((pageIndex - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        return (items, total);
    }
}
