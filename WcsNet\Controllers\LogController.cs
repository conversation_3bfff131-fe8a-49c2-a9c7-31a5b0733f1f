using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 日志管理控制器
/// </summary>
[ApiController]
[Route("api/sys/logs")]
public class LogController : ControllerBase
{
    private readonly WcsDbContext _context;

    public LogController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取登录日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="username">用户名（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <param name="start_time">开始时间（可选）</param>
    /// <param name="end_time">结束时间（可选）</param>
    /// <returns>登录日志列表</returns>
    [HttpGet("loginlog")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<object>>>> GetLoginLogs(
        int page_size = 10,
        int page_no = 1,
        string? username = null,
        int? status = null,
        string? start_time = null,
        string? end_time = null)
    {
        try
        {
            var query = _context.LoginLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(username))
            {
                query = query.Where(l => l.Username.Contains(username));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            if (!string.IsNullOrEmpty(start_time) && DateTime.TryParse(start_time, out var startDate))
            {
                query = query.Where(l => l.CreatedAt >= startDate);
            }

            if (!string.IsNullOrEmpty(end_time) && DateTime.TryParse(end_time, out var endDate))
            {
                query = query.Where(l => l.CreatedAt <= endDate);
            }

            var total = await query.CountAsync();
            var logs = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new
                {
                    id = l.Id,
                    username = l.Username,
                    ip = l.Ip,
                    location = l.Location ?? "",
                    browser = l.Browser,
                    os = l.Os,
                    status = l.Status,
                    msg = l.Msg ?? "",
                    login_time = l.CreatedAt
                })
                .ToListAsync();

            var response = new SimplePagedResponse<object>
            {
                List = logs.Cast<object>().ToList(),
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 清除登录日志
    /// </summary>
    /// <returns>清除结果</returns>
    [HttpDelete("loginlog")]
    public async Task<ActionResult<ApiResponse<string>>> ClearLoginLogs()
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE log_login");
            return Ok(ApiResponse<string>.Success("登录日志清除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 批量删除登录日志
    /// </summary>
    /// <param name="ids">日志ID列表，用逗号分隔</param>
    /// <returns>删除结果</returns>
    [HttpDelete("loginbatchdel/{ids}")]
    public async Task<ActionResult<ApiResponse<string>>> BatchDeleteLoginLogs(string ids)
    {
        try
        {
            var idList = ids.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => ulong.Parse(id.Trim()))
                .ToList();

            var logsToDelete = await _context.LoginLogs
                .Where(l => idList.Contains(l.Id))
                .ToListAsync();

            _context.LoginLogs.RemoveRange(logsToDelete);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success($"成功删除 {logsToDelete.Count} 条登录日志"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取操作日志列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="module">模块（可选）</param>
    /// <param name="status">状态（可选）</param>
    /// <param name="start_time">开始时间（可选）</param>
    /// <param name="end_time">结束时间（可选）</param>
    /// <returns>操作日志列表</returns>
    [HttpGet("operation")]
    public async Task<ActionResult<ApiResponse<SimplePagedResponse<object>>>> GetOperationLogs(
        int page_size = 10,
        int page_no = 1,
        string? module = null,
        int? status = null,
        string? start_time = null,
        string? end_time = null)
    {
        try
        {
            var query = _context.OperationLogs.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrEmpty(module))
            {
                query = query.Where(l => l.Module.Contains(module));
            }

            if (status.HasValue)
            {
                query = query.Where(l => l.Status == status.Value);
            }

            if (!string.IsNullOrEmpty(start_time) && DateTime.TryParse(start_time, out var startDate))
            {
                query = query.Where(l => l.CreatedAt >= startDate);
            }

            if (!string.IsNullOrEmpty(end_time) && DateTime.TryParse(end_time, out var endDate))
            {
                query = query.Where(l => l.CreatedAt <= endDate);
            }

            var total = await query.CountAsync();
            var logs = await query
                .OrderByDescending(l => l.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(l => new
                {
                    id = l.Id,
                    oper = l.Oper,
                    module = l.Module,
                    uri = l.Uri,
                    ip = l.Ip,
                    os = l.Os,
                    browser = l.Browser,
                    status = l.Status,
                    created_at = l.CreatedAt
                })
                .ToListAsync();

            var response = new SimplePagedResponse<object>
            {
                List = logs.Cast<object>().ToList(),
                Total = total
            };

            return Ok(ApiResponse<SimplePagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<SimplePagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 清除操作日志
    /// </summary>
    /// <returns>清除结果</returns>
    [HttpDelete("operation")]
    public async Task<ActionResult<ApiResponse<string>>> ClearOperationLogs()
    {
        try
        {
            await _context.Database.ExecuteSqlRawAsync("TRUNCATE TABLE log_operation");
            return Ok(ApiResponse<string>.Success("操作日志清除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 批量删除操作日志
    /// </summary>
    /// <param name="ids">日志ID列表，用逗号分隔</param>
    /// <returns>删除结果</returns>
    [HttpDelete("operationbatchdel/{ids}")]
    public async Task<ActionResult<ApiResponse<string>>> BatchDeleteOperationLogs(string ids)
    {
        try
        {
            var idList = ids.Split(',', StringSplitOptions.RemoveEmptyEntries)
                .Select(id => ulong.Parse(id.Trim()))
                .ToList();

            var logsToDelete = await _context.OperationLogs
                .Where(l => idList.Contains(l.Id))
                .ToListAsync();

            _context.OperationLogs.RemoveRange(logsToDelete);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success($"成功删除 {logsToDelete.Count} 条操作日志"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}
