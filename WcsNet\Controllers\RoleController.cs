using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using WcsNet.DTOs;
using WcsNet.Models;

namespace WcsNet.Controllers;

/// <summary>
/// 角色管理控制器
/// </summary>
[ApiController]
[Route("api/sys/roles")]
public class RoleController : ControllerBase
{
    private readonly WcsDbContext _context;

    public RoleController(WcsDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// 获取角色列表
    /// </summary>
    /// <param name="page">页码</param>
    /// <param name="pageSize">每页大小</param>
    /// <param name="keyword">搜索关键词</param>
    /// <returns>角色列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<object>>>> GetRoles(
        int page = 1, 
        int pageSize = 10, 
        string? keyword = null)
    {
        try
        {
            var query = _context.Roles.AsQueryable();

            // 搜索过滤
            if (!string.IsNullOrEmpty(keyword))
            {
                query = query.Where(r => r.RoleName.Contains(keyword) || 
                                        r.RoleCode.Contains(keyword));
            }

            var total = await query.CountAsync();
            var roles = await query
                .OrderBy(r => r.SortNumber)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(r => new
                {
                    id = r.Id,
                    name = r.RoleName,  // 前端期望的字段名
                    code = r.RoleCode,  // 前端期望的字段名
                    type = r.RoleType,
                    sort = r.SortNumber,
                    status = r.Status,
                    remark = r.Remark,
                    perms = r.Perms,
                    createdAt = r.CreatedAt  // 添加创建时间
                })
                .ToListAsync();

            var response = new PagedResponse<object>
            {
                Items = roles.Cast<object>().ToList(),
                Total = total,
                PageIndex = page,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<object>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取所有角色（简化版本）
    /// </summary>
    /// <returns>角色列表</returns>
    [HttpGet("all")]
    public async Task<ActionResult<ApiResponse<List<object>>>> GetAllRoles()
    {
        try
        {
            var roles = await _context.Roles
                .Where(r => r.Status == 1)
                .OrderBy(r => r.SortNumber)
                .Select(r => new
                {
                    id = r.Id,
                    name = r.RoleName,  // 前端期望的字段名
                    code = r.RoleCode   // 前端期望的字段名
                })
                .ToListAsync();

            return Ok(ApiResponse<List<object>>.Success(roles.Cast<object>().ToList()));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<object>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建角色
    /// </summary>
    /// <param name="request">角色信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<string>>> CreateRole([FromBody] CreateRoleRequest request)
    {
        try
        {
            // 检查角色编码是否已存在
            var existingRole = await _context.Roles.FirstOrDefaultAsync(r => r.RoleCode == request.RoleCode);
            if (existingRole != null)
            {
                return Ok(ApiResponse<string>.Error(400, "角色编码已存在"));
            }

            var role = new Role
            {
                RoleName = request.RoleName,
                RoleCode = request.RoleCode,
                RoleType = request.RoleType ?? 3,
                SortNumber = request.SortNumber ?? 0,
                Status = request.Status ?? 1,
                Remark = request.Remark ?? "",
                Perms = ""
            };

            _context.Roles.Add(role);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("角色创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">角色信息</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateRole(uint id, [FromBody] UpdateRoleRequest request)
    {
        try
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null)
            {
                return Ok(ApiResponse<string>.Error(404, "角色不存在"));
            }

            // 检查角色编码是否被其他角色使用
            if (!string.IsNullOrEmpty(request.RoleCode) && request.RoleCode != role.RoleCode)
            {
                var existingRole = await _context.Roles.FirstOrDefaultAsync(r => r.RoleCode == request.RoleCode && r.Id != id);
                if (existingRole != null)
                {
                    return Ok(ApiResponse<string>.Error(400, "角色编码已被其他角色使用"));
                }
                role.RoleCode = request.RoleCode;
            }

            if (!string.IsNullOrEmpty(request.RoleName)) role.RoleName = request.RoleName;
            if (request.RoleType.HasValue) role.RoleType = request.RoleType.Value;
            if (request.SortNumber.HasValue) role.SortNumber = request.SortNumber.Value;
            if (request.Status.HasValue) role.Status = request.Status.Value;
            if (request.Remark != null) role.Remark = request.Remark;

            // role.UpdatedAt = DateTime.Now; // Role实体没有UpdatedAt字段

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("角色更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除角色
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteRole(uint id)
    {
        try
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null)
            {
                return Ok(ApiResponse<string>.Error(404, "角色不存在"));
            }

            // 检查是否有用户使用此角色
            var userCount = await _context.Users.CountAsync(u => u.RoleId == id);
            if (userCount > 0)
            {
                return Ok(ApiResponse<string>.Error(400, $"该角色下还有 {userCount} 个用户，无法删除"));
            }

            _context.Roles.Remove(role);
            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("角色删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取角色的菜单权限ID列表
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <returns>菜单权限ID列表</returns>
    [HttpGet("menus/{id}")]
    public async Task<ActionResult<ApiResponse<List<int>>>> GetRoleMenuIds(uint id)
    {
        try
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null)
            {
                return Ok(ApiResponse<List<int>>.Error(404, "角色不存在"));
            }

            var menuIds = new List<int>();
            if (!string.IsNullOrEmpty(role.Perms))
            {
                menuIds = role.Perms.Split(',', StringSplitOptions.RemoveEmptyEntries)
                    .Select(id => int.Parse(id.Trim()))
                    .ToList();
            }

            return Ok(ApiResponse<List<int>>.Success(menuIds));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 设置角色的菜单权限
    /// </summary>
    /// <param name="id">角色ID</param>
    /// <param name="request">菜单权限设置请求</param>
    /// <returns>设置结果</returns>
    [HttpPut("menus/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> SetRoleMenus(uint id, [FromBody] SetRoleMenusRequest request)
    {
        try
        {
            var role = await _context.Roles.FindAsync(id);
            if (role == null)
            {
                return Ok(ApiResponse<string>.Error(404, "角色不存在"));
            }

            role.Perms = string.Join(",", request.MenuIds);
            // role.UpdatedAt = DateTime.Now; // Role实体没有UpdatedAt字段

            await _context.SaveChangesAsync();

            return Ok(ApiResponse<string>.Success("角色权限设置成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}

/// <summary>
/// 创建角色请求
/// </summary>
public class CreateRoleRequest
{
    public string RoleName { get; set; } = string.Empty;
    public string RoleCode { get; set; } = string.Empty;
    public sbyte? RoleType { get; set; }
    public int? SortNumber { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// 更新角色请求
/// </summary>
public class UpdateRoleRequest
{
    public string? RoleName { get; set; }
    public string? RoleCode { get; set; }
    public sbyte? RoleType { get; set; }
    public int? SortNumber { get; set; }
    public sbyte? Status { get; set; }
    public string? Remark { get; set; }
}

/// <summary>
/// 设置角色菜单权限请求
/// </summary>
public class SetRoleMenusRequest
{
    public List<int> MenuIds { get; set; } = new();
}
