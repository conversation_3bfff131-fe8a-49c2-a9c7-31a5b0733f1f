{"version": 3, "sources": ["../../.pnpm/plus-pro-components@0.1.16__aaf977867e0d6890aab47b8c12fc3786/node_modules/plus-pro-components/es/locale/lang/zh-cn.mjs"], "sourcesContent": ["var zhCn = {\n  name: \"zh-cn\",\n  plus: {\n    dialog: {\n      confirmText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\",\n      title: \"\\u5F39\\u7A97\"\n    },\n    datepicker: {\n      startPlaceholder: \"\\u8BF7\\u9009\\u62E9\\u5F00\\u59CB\\u65F6\\u95F4\",\n      endPlaceholder: \"\\u8BF7\\u9009\\u62E9\\u7ED3\\u675F\\u65F6\\u95F4\"\n    },\n    dialogForm: {\n      title: \"\\u5F39\\u7A97\\u8868\\u5355\"\n    },\n    drawerForm: {\n      title: \"\\u62BD\\u5C49\\u8868\\u5355\",\n      confirmText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\"\n    },\n    form: {\n      submitText: \"\\u63D0\\u4EA4\",\n      resetText: \"\\u91CD\\u7F6E\",\n      errorTip: \"\\u8BF7\\u5B8C\\u6574\\u586B\\u5199\\u8868\\u5355\\u7136\\u540E\\u518D\\u6B21\\u63D0\\u4EA4\\uFF01\"\n    },\n    field: {\n      pleaseEnter: \"\\u8BF7\\u8F93\\u5165\",\n      pleaseSelect: \"\\u8BF7\\u9009\\u62E9\"\n    },\n    popover: {\n      confirmText: \"\\u786E\\u5B9A\",\n      cancelText: \"\\u53D6\\u6D88\"\n    },\n    search: {\n      searchText: \"\\u641C\\u7D22\",\n      resetText: \"\\u91CD\\u7F6E\",\n      expand: \"\\u5C55\\u5F00\",\n      retract: \"\\u6536\\u8D77\"\n    },\n    table: {\n      title: \"\\u8868\\u683C\",\n      density: \"\\u5BC6\\u5EA6\",\n      refresh: \"\\u5237\\u65B0\",\n      columnSettings: \"\\u5217\\u8BBE\\u7F6E\",\n      selectAll: \"\\u5168\\u9009\",\n      default: \"\\u9ED8\\u8BA4\",\n      loose: \"\\u5BBD\\u677E\",\n      compact: \"\\u7D27\\u51D1\",\n      action: \"\\u64CD\\u4F5C\",\n      more: \"\\u66F4\\u591A\",\n      confirmToPerformThisOperation: \"\\u786E\\u5B9A\\u6267\\u884C\\u672C\\u6B21\\u64CD\\u4F5C?\",\n      prompt: \"\\u63D0\\u793A\",\n      sort: \"\\u6392\\u5E8F\"\n    },\n    stepsForm: {\n      nextText: \"\\u4E0B\\u4E00\\u6B65\",\n      preText: \"\\u4E0A\\u4E00\\u6B65\",\n      submitText: \"\\u63D0\\u4EA4\"\n    },\n    inputTag: {\n      placeholder: \"\\u8BF7\\u8F93\\u5165\\u5173\\u952E\\u5B57\\u540E\\u6309\\u56DE\\u8F66/\\u7A7A\\u683C\\u952E\"\n    },\n    header: {\n      logout: \"\\u9000\\u51FA\\u767B\\u5F55\"\n    }\n  }\n};\n\nexport { zhCn as default };\n"], "mappings": ";;;AAAA,IAAI,OAAO;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,IACJ,QAAQ;AAAA,MACN,aAAa;AAAA,MACb,YAAY;AAAA,MACZ,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,IAClB;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,IACT;AAAA,IACA,YAAY;AAAA,MACV,OAAO;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,MAAM;AAAA,MACJ,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,MACb,cAAc;AAAA,IAChB;AAAA,IACA,SAAS;AAAA,MACP,aAAa;AAAA,MACb,YAAY;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACN,YAAY;AAAA,MACZ,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,MACP,SAAS;AAAA,MACT,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,WAAW;AAAA,MACX,SAAS;AAAA,MACT,OAAO;AAAA,MACP,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,+BAA+B;AAAA,MAC/B,QAAQ;AAAA,MACR,MAAM;AAAA,IACR;AAAA,IACA,WAAW;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT,YAAY;AAAA,IACd;AAAA,IACA,UAAU;AAAA,MACR,aAAa;AAAA,IACf;AAAA,IACA,QAAQ;AAAA,MACN,QAAQ;AAAA,IACV;AAAA,EACF;AACF;", "names": []}