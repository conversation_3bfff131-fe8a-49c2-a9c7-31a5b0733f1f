{"name": "@intlify/vue-i18n-extensions", "description": "vue-i18n extensions", "version": "6.2.0", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-extensions/issues"}, "homepage": "https://github.com/intlify/vue-i18n-extensions#readme", "keywords": ["extensions", "i18n", "optimaization", "server-side-rendering", "vue", "vue-i18n"], "license": "MIT", "dependencies": {"@babel/parser": "^7.24.6", "@intlify/shared": "^9.0.0", "@vue/compiler-dom": "^3.2.45", "vue-i18n": "^9.0.0"}, "devDependencies": {"@babel/types": "^7.24.6", "@eslint/js": "^9.2.0", "@microsoft/api-extractor": "^7.18.4", "@types/eslint": "^8.56.10", "@types/node": "^20.13.0", "@vitest/coverage-v8": "^1.6.0", "@vue/compiler-core": "^3.2.45", "@vue/compiler-sfc": "^3.2.45", "@vue/compiler-ssr": "^3.2.45", "@vue/runtime-dom": "^3.2.45", "@vue/server-renderer": "^3.2.45", "api-docs-gen": "^0.4.0", "bumpp": "^9.4.1", "eslint": "^9.2.0", "get-port-please": "^3.1.2", "gh-changelogen": "^0.2.8", "globals": "^15.0.0", "jsdom": "^24.0.0", "lint-staged": "^15.2.5", "npm-run-all2": "^6.0.0", "opener": "^1.5.2", "pkg-types": "^1.1.1", "playwright": "^1.44.0", "prettier": "^3.2.5", "tsx": "^4.11.0", "typescript": "^5.4.5", "typescript-eslint": "^7.9.0", "unbuild": "^2.0.0", "vitest": "^1.6.0", "vue": "^3.2.45"}, "peerDependencies": {"@intlify/shared": "^9.0.0 || ^10.0.0", "@vue/compiler-dom": "^3.0.0", "vue": "^3.0.0", "vue-i18n": "^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"@intlify/shared": {"optional": true}, "@vue/compiler-dom": {"optional": true}, "vue": {"optional": true}, "vue-i18n": {"optional": true}}, "files": ["dist/idnex.cjs", "dist/index.mjs", "dist/index.d.ts", "dist/index.d.mts", "dist/index.d.cts"], "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-extensions.git"}, "engines": {"node": ">= 18"}, "lint-staged": {"*.{json,md,yml}": ["prettier --write"], "*.{js,mjs,cjs}": ["prettier --write", "eslint --fix"], "*.ts?(x)": ["prettier --parser=typescript --write", "eslint --fix"]}, "scripts": {"changelog": "gh-changelogen --repo=intlify/vue-i18n-extensions", "release": "bumpp --commit \"release: v%s\" --push --tag", "build": "pnpm build:transpile && pnpm build:extract && pnpm build:docs", "build:nightly": "pnpm build:transpile && pnpm build:extract", "build:docs": "api-docs-gen ./temp/vue-i18n-extensions.api.json -c ./docsgen.config.js -o ./docs", "build:extract": "api-extractor run -l -c ./api-extractor.json --typescript-compiler-folder node_modules/typescript", "build:transpile": "unbuild", "check-install": "tsx scripts/playwright.ts", "clean": "git clean -fdx", "coverage": "opener coverage/lcov-report/index.html", "example": "webpack serve --config example/webpack.config.js --inline --hot", "lint": "run-p \"lint:* {@}\" --", "lint:prettier": "prettier . --check", "lint:eslint": "eslint .", "fix": "run-p \"fix:* {@}\" --", "fix:prettier": "prettier . --write", "fix:eslint": "eslint . --fix", "test": "pnpm test:cover && pnpm check-install && pnpm test:e2e", "test:cover": "pnpm test:unit --coverage --coverage.include=src", "test:e2e": "vitest -c ./vitest.e2e.config.ts run", "test:unit": "vitest run", "test:watch": "vitest --no-cache --watch", "watch": "tsc -p tsconfig.cjs.json --watch"}}