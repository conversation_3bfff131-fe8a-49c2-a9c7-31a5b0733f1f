using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("sys_parameter")]
public class Parameter : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("conf_name")]
    public string ConfName { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("conf_key")]
    public string ConfKey { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    [Column("conf_value")]
    public string ConfValue { get; set; } = string.Empty;

    [Column("conf_type")]
    public int ConfType { get; set; } = 1; // 1:字符串 2:数字 3:布尔值

    [StringLength(255)]
    public string Remark { get; set; } = string.Empty;
}
