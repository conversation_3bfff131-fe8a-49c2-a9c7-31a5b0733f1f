using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/alarms")]
public class AlarmController : ControllerBase
{
    /// <summary>
    /// 获取告警列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <returns>告警列表</returns>
    [HttpGet]
    public ActionResult<ApiResponse<AlarmPagedResponse>> GetAlarms(int page_size = 10, int page_no = 1)
    {
        try
        {
            var alarms = new List<AlarmDto>();
            var random = new Random();

            for (int i = 1; i <= page_size; i++)
            {
                alarms.Add(new AlarmDto
                {
                    Id = i,
                    InterfaceName = "", // 接口名称为空，对应截图中的空白
                    StartTime = DateTime.Now.AddMinutes(-random.Next(1, 1440)),
                    EndTime = DateTime.Now.AddMinutes(-random.Next(1, 1440)),
                    Status = "失败",
                    CreateTime = DateTime.Now.AddMinutes(-random.Next(1, 1440))
                });
            }

            var response = new AlarmPagedResponse
            {
                List = alarms,
                Total = 50 // 模拟总数
            };

            return Ok(ApiResponse<AlarmPagedResponse>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<AlarmPagedResponse>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除告警
    /// </summary>
    /// <param name="id">告警ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public ActionResult<ApiResponse<object>> DeleteAlarm(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "告警删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 处理告警
    /// </summary>
    /// <param name="id">告警ID</param>
    /// <returns>处理结果</returns>
    [HttpPost("{id}/handle")]
    public ActionResult<ApiResponse> HandleAlarm(int id)
    {
        try
        {
            // 这里应该更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"告警 {id} 处理成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 批量处理告警
    /// </summary>
    /// <param name="ids">告警ID列表</param>
    /// <returns>处理结果</returns>
    [HttpPost("batch-handle")]
    public ActionResult<ApiResponse> BatchHandleAlarms([FromBody] List<int> ids)
    {
        try
        {
            // 这里应该批量更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"批量处理 {ids.Count} 个告警成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取告警统计
    /// </summary>
    /// <returns>告警统计数据</returns>
    [HttpGet("stats")]
    public ActionResult<ApiResponse<object>> GetAlarmStats()
    {
        try
        {
            var random = new Random();
            var stats = new
            {
                TotalAlarms = random.Next(20, 100),
                UnhandledAlarms = random.Next(5, 30),
                TodayAlarms = random.Next(3, 15),
                HighLevelAlarms = random.Next(1, 8),
                AlarmsByLevel = new
                {
                    Low = random.Next(5, 20),
                    Medium = random.Next(3, 15),
                    High = random.Next(1, 8),
                    Critical = random.Next(0, 3)
                },
                AlarmsByType = new
                {
                    DeviceFault = random.Next(2, 10),
                    Communication = random.Next(1, 8),
                    TaskTimeout = random.Next(1, 5),
                    Safety = random.Next(0, 3),
                    System = random.Next(1, 6)
                }
            };

            return Ok(ApiResponse<object>.Success(stats));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }
}
