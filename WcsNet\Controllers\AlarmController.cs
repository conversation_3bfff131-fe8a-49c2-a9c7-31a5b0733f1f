using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using Microsoft.EntityFrameworkCore;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/alarms")]
public class AlarmController : ControllerBase
{
    private readonly WcsDbContext _context;

    public AlarmController(WcsDbContext context)
    {
        _context = context;
    }
    /// <summary>
    /// 获取告警列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <param name="page_no">页码</param>
    /// <param name="alarm_type">告警类型</param>
    /// <param name="device_id">设备ID</param>
    /// <returns>告警列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<AlarmPagedResponse>>> GetAlarms(
        int page_size = 10,
        int page_no = 1,
        int? alarm_type = null,
        uint? device_id = null)
    {
        try
        {
            // 从数据库获取告警数据
            var query = _context.Alarms.AsQueryable();

            // 应用筛选条件
            if (alarm_type.HasValue)
            {
                query = query.Where(a => a.AlarmType == alarm_type.Value);
            }

            if (device_id.HasValue)
            {
                query = query.Where(a => a.DeviceId == device_id.Value);
            }

            var total = await query.CountAsync();
            var alarms = await query
                .OrderByDescending(a => a.CreatedAt)
                .Skip((page_no - 1) * page_size)
                .Take(page_size)
                .Select(a => new AlarmDto
                {
                    Id = (int)a.Id,
                    InterfaceName = a.Remark, // 使用备注作为接口名称
                    StartTime = a.CreatedAt.HasValue ? a.CreatedAt.Value : DateTime.Now,
                    EndTime = a.CreatedAt.HasValue ? a.CreatedAt.Value.AddMinutes(30) : DateTime.Now.AddMinutes(30), // 假设告警持续30分钟
                    Status = a.AlarmType == 1 ? "成功" : "失败",
                    CreateTime = a.CreatedAt.HasValue ? a.CreatedAt.Value : DateTime.Now
                })
                .ToListAsync();

            var response = new AlarmPagedResponse
            {
                List = alarms,
                Total = total
            };

            return Ok(ApiResponse<AlarmPagedResponse>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<AlarmPagedResponse>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除告警
    /// </summary>
    /// <param name="id">告警ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public ActionResult<ApiResponse<object>> DeleteAlarm(int id)
    {
        try
        {
            // 模拟删除操作
            return Ok(ApiResponse<object>.Success(new { message = "告警删除成功" }));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 处理告警
    /// </summary>
    /// <param name="id">告警ID</param>
    /// <returns>处理结果</returns>
    [HttpPost("{id}/handle")]
    public ActionResult<ApiResponse> HandleAlarm(int id)
    {
        try
        {
            // 这里应该更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"告警 {id} 处理成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 批量处理告警
    /// </summary>
    /// <param name="ids">告警ID列表</param>
    /// <returns>处理结果</returns>
    [HttpPost("batch-handle")]
    public ActionResult<ApiResponse> BatchHandleAlarms([FromBody] List<int> ids)
    {
        try
        {
            // 这里应该批量更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"批量处理 {ids.Count} 个告警成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取告警统计
    /// </summary>
    /// <returns>告警统计数据</returns>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<object>>> GetAlarmStats()
    {
        try
        {
            // 从数据库获取真实的告警统计数据
            var totalAlarms = await _context.Alarms.CountAsync();
            var todayStart = DateTime.Today;
            var todayAlarms = await _context.Alarms
                .Where(a => a.CreatedAt >= todayStart)
                .CountAsync();

            // 按告警类型统计
            var alarmsByType = await _context.Alarms
                .GroupBy(a => a.AlarmType)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .ToListAsync();

            // 按设备统计
            var alarmsByDevice = await _context.Alarms
                .GroupBy(a => a.DeviceId)
                .Select(g => new { DeviceId = g.Key, Count = g.Count() })
                .ToListAsync();

            var stats = new
            {
                TotalAlarms = totalAlarms,
                UnhandledAlarms = totalAlarms, // 假设所有告警都未处理
                TodayAlarms = todayAlarms,
                HighLevelAlarms = alarmsByType.Where(x => x.Type >= 3).Sum(x => x.Count),
                AlarmsByLevel = new
                {
                    Low = alarmsByType.Where(x => x.Type == 1).Sum(x => x.Count),
                    Medium = alarmsByType.Where(x => x.Type == 2).Sum(x => x.Count),
                    High = alarmsByType.Where(x => x.Type == 3).Sum(x => x.Count),
                    Critical = alarmsByType.Where(x => x.Type >= 4).Sum(x => x.Count)
                },
                AlarmsByType = new
                {
                    DeviceFault = alarmsByType.Where(x => x.Type == 1).Sum(x => x.Count),
                    Communication = alarmsByType.Where(x => x.Type == 2).Sum(x => x.Count),
                    TaskTimeout = alarmsByType.Where(x => x.Type == 3).Sum(x => x.Count),
                    Safety = alarmsByType.Where(x => x.Type == 4).Sum(x => x.Count),
                    System = alarmsByType.Where(x => x.Type == 5).Sum(x => x.Count)
                },
                AlarmsByDevice = alarmsByDevice.Take(10).ToList() // 取前10个设备的告警统计
            };

            return Ok(ApiResponse<object>.Success(stats));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }
}
