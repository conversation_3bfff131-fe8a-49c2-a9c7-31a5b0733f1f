using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/alarms")]
public class AlarmController : ControllerBase
{
    /// <summary>
    /// 获取告警列表
    /// </summary>
    /// <param name="page_size">页大小</param>
    /// <returns>告警列表</returns>
    [HttpGet]
    public ActionResult<ApiResponse<PagedResponse<AlarmStatDto>>> GetAlarms(int page_size = 10)
    {
        try
        {
            var alarms = new List<AlarmStatDto>();
            var random = new Random();
            var alarmTypes = new[] { "设备故障", "通信异常", "任务超时", "安全告警", "系统异常" };
            var levels = new[] { "低", "中", "高", "紧急" };
            var devices = new[] { "AGV001", "AGV002", "堆垛机001", "输送线001", "扫码器001" };

            for (int i = 1; i <= page_size; i++)
            {
                var isHandled = random.Next(0, 2) == 1;
                alarms.Add(new AlarmStatDto
                {
                    Id = i,
                    AlarmType = alarmTypes[random.Next(alarmTypes.Length)],
                    Level = levels[random.Next(levels.Length)],
                    Message = $"告警消息{i:D3} - 设备运行异常，请及时处理",
                    DeviceName = devices[random.Next(devices.Length)],
                    AlarmTime = DateTime.Now.AddMinutes(-random.Next(1, 1440)), // 最近24小时内
                    IsHandled = isHandled,
                    HandleTime = isHandled ? DateTime.Now.AddMinutes(-random.Next(1, 60)) : null
                });
            }

            // 按告警时间倒序排列
            alarms = alarms.OrderByDescending(a => a.AlarmTime).ToList();

            var response = new PagedResponse<AlarmStatDto>
            {
                Items = alarms,
                Total = 50, // 模拟总数
                PageIndex = 1,
                PageSize = page_size
            };

            return Ok(ApiResponse<PagedResponse<AlarmStatDto>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<AlarmStatDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 处理告警
    /// </summary>
    /// <param name="id">告警ID</param>
    /// <returns>处理结果</returns>
    [HttpPost("{id}/handle")]
    public ActionResult<ApiResponse> HandleAlarm(int id)
    {
        try
        {
            // 这里应该更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"告警 {id} 处理成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 批量处理告警
    /// </summary>
    /// <param name="ids">告警ID列表</param>
    /// <returns>处理结果</returns>
    [HttpPost("batch-handle")]
    public ActionResult<ApiResponse> BatchHandleAlarms([FromBody] List<int> ids)
    {
        try
        {
            // 这里应该批量更新数据库中的告警状态
            // 模拟处理成功
            return Ok(ApiResponse.Success($"批量处理 {ids.Count} 个告警成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取告警统计
    /// </summary>
    /// <returns>告警统计数据</returns>
    [HttpGet("stats")]
    public ActionResult<ApiResponse<object>> GetAlarmStats()
    {
        try
        {
            var random = new Random();
            var stats = new
            {
                TotalAlarms = random.Next(20, 100),
                UnhandledAlarms = random.Next(5, 30),
                TodayAlarms = random.Next(3, 15),
                HighLevelAlarms = random.Next(1, 8),
                AlarmsByLevel = new
                {
                    Low = random.Next(5, 20),
                    Medium = random.Next(3, 15),
                    High = random.Next(1, 8),
                    Critical = random.Next(0, 3)
                },
                AlarmsByType = new
                {
                    DeviceFault = random.Next(2, 10),
                    Communication = random.Next(1, 8),
                    TaskTimeout = random.Next(1, 5),
                    Safety = random.Next(0, 3),
                    System = random.Next(1, 6)
                }
            };

            return Ok(ApiResponse<object>.Success(stats));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<object>.Error(500, ex.Message));
        }
    }
}
