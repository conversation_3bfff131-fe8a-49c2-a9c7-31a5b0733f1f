{"Version": 1, "WorkspaceRootPath": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\deptcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\deptcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet - 副本\\Controllers\\DeptController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:WcsNet - 副本\\Controllers\\DeptController.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\dictcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\dictcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\apilogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\apilogcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\stockcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\stockcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\sysusercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\sysusercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\syscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\syscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\simpleusercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\simpleusercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\menucontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\menucontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\statcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\statcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\rolecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\rolecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\logcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\logcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\lanecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\lanecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\initcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\initcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\devicecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\devicecontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\apitestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\apitestcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\alarmcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\alarmcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\taskcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\taskcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\uploadcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\uploadcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\utils\\tokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\utils\\tokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|e:\\sourcecode\\test\\temptest\\ai\\wcs\\wcsnet\\configuration\\appconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{2AEB42AA-47D7-C1DA-4FC4-1EC7645E6E06}|WcsNet\\WcsNet.csproj|solutionrelative:wcsnet\\configuration\\appconfig.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 35, "Children": [{"$type": "Bookmark", "Name": "ST:43:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:35:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:36:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:37:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:38:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:39:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:40:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:41:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:42:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:28:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:29:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:30:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:32:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:33:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:34:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:26:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:27:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:24:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:25:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:23:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:20:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:21:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:22:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:18:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:16:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Bookmark", "Name": "ST:0:0:{d78612c7-9962-4b83-95d9-268046dad23a}"}, {"$type": "Bookmark", "Name": "ST:0:0:{65ddf8c3-8f89-4077-a6c6-dbb8853aab13}"}, {"$type": "Bookmark", "Name": "ST:19:0:{2456bd12-ecf7-4988-a4a6-67d49173f565}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "DeptController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet - 副本\\Controllers\\DeptController.cs", "RelativeDocumentMoniker": "WcsNet - 副本\\Controllers\\DeptController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet - 副本\\Controllers\\DeptController.cs", "RelativeToolTip": "WcsNet - 副本\\Controllers\\DeptController.cs", "ViewState": "AgIAACoAAAAAAAAAAADwvzoAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T01:24:50.885Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Program.cs", "RelativeDocumentMoniker": "WcsNet\\Program.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Program.cs", "RelativeToolTip": "WcsNet\\Program.cs", "ViewState": "AgIAADAAAAAAAAAAAAAAADwAAABtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-25T01:01:11.753Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "StatController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\StatController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\StatController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\StatController.cs", "RelativeToolTip": "WcsNet\\Controllers\\StatController.cs", "ViewState": "AgIAAEsAAAAAAAAAAAAcwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:18:47.335Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "StockController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\StockController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\StockController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\StockController.cs", "RelativeToolTip": "WcsNet\\Controllers\\StockController.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:18:29.253Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "SysController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SysController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\SysController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SysController.cs", "RelativeToolTip": "WcsNet\\Controllers\\SysController.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAYwDEAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T08:14:18.726Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ApiLogController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\ApiLogController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\ApiLogController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\ApiLogController.cs", "RelativeToolTip": "WcsNet\\Controllers\\ApiLogController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:29:46.493Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "appsettings.json", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\appsettings.json", "RelativeDocumentMoniker": "WcsNet\\appsettings.json", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\appsettings.json", "RelativeToolTip": "WcsNet\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-23T07:00:52.175Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "DeptController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DeptController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\DeptController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DeptController.cs", "RelativeToolTip": "WcsNet\\Controllers\\DeptController.cs", "ViewState": "AgIAADAAAAAAAAAAAAAmwEcAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:32:39.506Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "SimpleUserController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SimpleUserController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\SimpleUserController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SimpleUserController.cs", "RelativeToolTip": "WcsNet\\Controllers\\SimpleUserController.cs", "ViewState": "AgIAAHQAAAAAAAAAAAAiwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:19:08.328Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "SysUserController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SysUserController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\SysUserController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\SysUserController.cs", "RelativeToolTip": "WcsNet\\Controllers\\SysUserController.cs", "ViewState": "AgIAACcAAAAAAAAAAAAYwD0AAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:39:22.927Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "DictController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DictController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\DictController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DictController.cs", "RelativeToolTip": "WcsNet\\Controllers\\DictController.cs", "ViewState": "AgIAAD8AAAAAAAAAAAAawEkAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T08:54:17.964Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "RoleController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\RoleController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\RoleController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\RoleController.cs", "RelativeToolTip": "WcsNet\\Controllers\\RoleController.cs", "ViewState": "AgIAADMBAAAAAAAAAAAIwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:38:53.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "MenuController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\MenuController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\MenuController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\MenuController.cs", "RelativeToolTip": "WcsNet\\Controllers\\MenuController.cs", "ViewState": "AgIAAFIBAAAAAAAAAAAgwFkBAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T00:56:22.551Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "LogController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\LogController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\LogController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\LogController.cs", "RelativeToolTip": "WcsNet\\Controllers\\LogController.cs", "ViewState": "AgIAAA0BAAAAAAAAAAAAAFAAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:19:36.905Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "LaneController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\LaneController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\LaneController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\LaneController.cs", "RelativeToolTip": "WcsNet\\Controllers\\LaneController.cs", "ViewState": "AgIAAKsAAAAAAAAAAAAmwEMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:32:20.627Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "InitController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\InitController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\InitController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\InitController.cs", "RelativeToolTip": "WcsNet\\Controllers\\InitController.cs", "ViewState": "AgIAAEgBAAAAAAAAAAAqwFcBAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T00:53:29.779Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "DeviceController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DeviceController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\DeviceController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\DeviceController.cs", "RelativeToolTip": "WcsNet\\Controllers\\DeviceController.cs", "ViewState": "AgIAAGkAAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:20:02.486Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "ApiTestController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\ApiTestController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\ApiTestController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\ApiTestController.cs", "RelativeToolTip": "WcsNet\\Controllers\\ApiTestController.cs", "ViewState": "AgIAAE8AAAAAAAAAAAAUwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T07:20:12.067Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "AlarmController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\AlarmController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\AlarmController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\AlarmController.cs", "RelativeToolTip": "WcsNet\\Controllers\\AlarmController.cs", "ViewState": "AgIAAJUAAAAAAAAAAAAAAH0AAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:24:27.593Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "UploadController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UploadController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\UploadController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UploadController.cs", "RelativeToolTip": "WcsNet\\Controllers\\UploadController.cs", "ViewState": "AgIAANUAAAAAAAAAAAAqwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:39:11.675Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "TokenGenerator.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Utils\\TokenGenerator.cs", "RelativeDocumentMoniker": "WcsNet\\Utils\\TokenGenerator.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Utils\\TokenGenerator.cs", "RelativeToolTip": "WcsNet\\Utils\\TokenGenerator.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAswBcAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:30:14.235Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "TaskController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\TaskController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\TaskController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\TaskController.cs", "RelativeToolTip": "WcsNet\\Controllers\\TaskController.cs", "ViewState": "AgIAAPUBAAAAAAAAAAAIwAMCAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:29:58.007Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "UserController.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "WcsNet\\Controllers\\UserController.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Controllers\\UserController.cs", "RelativeToolTip": "WcsNet\\Controllers\\UserController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACIAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T06:58:25.573Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "AppConfig.cs", "DocumentMoniker": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Configuration\\AppConfig.cs", "RelativeDocumentMoniker": "WcsNet\\Configuration\\AppConfig.cs", "ToolTip": "E:\\SourceCode\\Test\\TempTest\\Ai\\wcs\\WcsNet\\Configuration\\AppConfig.cs", "RelativeToolTip": "WcsNet\\Configuration\\AppConfig.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAswAUAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-23T07:00:54.986Z", "EditorCaption": ""}]}]}]}