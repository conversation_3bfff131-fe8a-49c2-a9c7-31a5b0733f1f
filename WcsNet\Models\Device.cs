using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_device")]
public class Device : BaseModel
{
    [Required]
    [StringLength(128)]
    [Column("device_code")]
    public string DeviceCode { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    [Column("device_name")]
    public string DeviceName { get; set; } = string.Empty;

    [Column("device_type")]
    public sbyte DeviceType { get; set; } = 0; // 设备类型

    [Column("comm_type")]
    public sbyte CommType { get; set; } = 0; // 通信方式

    [StringLength(128)]
    public string Addr { get; set; } = string.Empty; // 通信地址

    public long Port { get; set; } = 0; // 端口号

    public sbyte Online { get; set; } = 1; // 1未知 2在线 3离线

    [Column("run_status")]
    public sbyte RunStatus { get; set; } = 1; // 1未知 2运行 3停止 4报警

    public sbyte Status { get; set; } = 1; // 1启用 2停用

    public int Position { get; set; } = 0; // x轴位置

    [Column("created_id")]
    public long CreatedId { get; set; } = 0;

    [Column("updated_id")]
    public long UpdatedId { get; set; } = 0;

    [Column("created_at")]
    public DateTime? CreatedAt { get; set; }

    [Column("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}
