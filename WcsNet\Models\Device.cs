﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_device")]
public class Device : BaseModel
{
    [Required]
    [StringLength(64)]
    [Column("device_code")]
    public string DeviceCode { get; set; } = string.Empty;

    [Required]
    [StringLength(64)]
    [Column("device_name")]
    public string DeviceName { get; set; } = string.Empty;

    [Column("device_type")]
    public sbyte DeviceType { get; set; } = 1;

    [Column("comm_type")]
    public sbyte CommType { get; set; } = 1;

    [StringLength(15)]
    public string Addr { get; set; } = string.Empty;

    public long Port { get; set; } = 0;

    public sbyte Online { get; set; } = 1; // 1未知 2在线 3离线

    [Column("run_status")]
    public sbyte RunStatus { get; set; } = 1; // 1未知 2运行 3停止 4报警

    public sbyte Status { get; set; } = 1; // 1启用 2停用

    public int Position { get; set; } = 0;
}
