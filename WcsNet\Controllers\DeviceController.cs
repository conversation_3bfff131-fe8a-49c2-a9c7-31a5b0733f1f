using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Services;

namespace WcsNet.Controllers;

/// <summary>
/// 设备管理控制器
/// </summary>
[ApiController]
[Route("api")]
[Authorize]
public class DeviceController : ControllerBase
{
    private readonly DeviceService _deviceService;
    private readonly UserService _userService;

    public DeviceController(DeviceService deviceService, UserService userService)
    {
        _deviceService = deviceService;
        _userService = userService;
    }

    /// <summary>
    /// 获取设备列表
    /// </summary>
    [HttpGet("devices")]
    public async Task<ActionResult<ApiResponse<PagedResponse<DeviceDto>>>> GetDevices([FromQuery] DeviceListRequest request)
    {
        try
        {
            var result = await _deviceService.GetDeviceListAsync(request);
            return Ok(ApiResponse<PagedResponse<DeviceDto>>.Success(result));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<DeviceDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    [HttpGet("devices/{id}")]
    public async Task<ActionResult<ApiResponse<DeviceDto>>> GetDevice(ulong id)
    {
        try
        {
            var device = await _deviceService.GetDeviceByIdAsync(id);
            if (device == null)
            {
                return Ok(ApiResponse<DeviceDto>.Error(404, "设备不存在"));
            }

            return Ok(ApiResponse<DeviceDto>.Success(device));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DeviceDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    [HttpPost("devices")]
    public async Task<ActionResult<ApiResponse<DeviceDto>>> CreateDevice([FromBody] DeviceRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Ok(ApiResponse<DeviceDto>.Error(400, "请求参数无效"));
            }

            var userId = _userService.GetCurrentUserId(HttpContext);
            var device = await _deviceService.CreateDeviceAsync(request, (ulong)userId);
            return Ok(ApiResponse<DeviceDto>.Success(device));
        }
        catch (InvalidOperationException ex)
        {
            return Ok(ApiResponse<DeviceDto>.Error(400, ex.Message));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DeviceDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    [HttpPut("devices/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDevice(ulong id, [FromBody] DeviceRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Ok(ApiResponse<string>.Error(400, "请求参数无效"));
            }

            var userId = _userService.GetCurrentUserId(HttpContext);
            var success = await _deviceService.UpdateDeviceAsync(id, request, (ulong)userId);

            if (!success)
            {
                return Ok(ApiResponse<string>.Error(404, "设备不存在"));
            }

            return Ok(ApiResponse<string>.Success("更新成功"));
        }
        catch (InvalidOperationException ex)
        {
            return Ok(ApiResponse<string>.Error(400, ex.Message));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    [HttpDelete("devices/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDevice(ulong id)
    {
        try
        {
            var success = await _deviceService.DeleteDeviceAsync(id);
            if (!success)
            {
                return Ok(ApiResponse<string>.Error(404, "设备不存在"));
            }

            return Ok(ApiResponse<string>.Success("删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取设备属性列表
    /// </summary>
    [HttpGet("devices/props/{deviceId}")]
    public async Task<ActionResult<ApiResponse<List<DevicePropertyDto>>>> GetDeviceProperties(ulong deviceId)
    {
        try
        {
            var properties = await _deviceService.GetDevicePropertiesAsync(deviceId);
            return Ok(ApiResponse<List<DevicePropertyDto>>.Success(properties));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<DevicePropertyDto>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取设备属性
    /// </summary>
    [HttpGet("devices/property/{id}")]
    public async Task<ActionResult<ApiResponse<DevicePropertyDto>>> GetDeviceProperty(ulong id)
    {
        try
        {
            var property = await _deviceService.GetDevicePropertyByIdAsync(id);
            if (property == null)
            {
                return Ok(ApiResponse<DevicePropertyDto>.Error(404, "设备属性不存在"));
            }

            return Ok(ApiResponse<DevicePropertyDto>.Success(property));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DevicePropertyDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建设备属性
    /// </summary>
    [HttpPost("devices/property")]
    public async Task<ActionResult<ApiResponse<DevicePropertyDto>>> CreateDeviceProperty([FromBody] DevicePropertyRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Ok(ApiResponse<DevicePropertyDto>.Error(400, "请求参数无效"));
            }

            var userId = _userService.GetCurrentUserId(HttpContext);
            var property = await _deviceService.CreateDevicePropertyAsync(request, (ulong)userId);
            return Ok(ApiResponse<DevicePropertyDto>.Success(property));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<DevicePropertyDto>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新设备属性
    /// </summary>
    [HttpPut("devices/property/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> UpdateDeviceProperty(ulong id, [FromBody] DevicePropertyRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return Ok(ApiResponse<string>.Error(400, "请求参数无效"));
            }

            var userId = _userService.GetCurrentUserId(HttpContext);
            var success = await _deviceService.UpdateDevicePropertyAsync(id, request, (ulong)userId);

            if (!success)
            {
                return Ok(ApiResponse<string>.Error(404, "设备属性不存在"));
            }

            return Ok(ApiResponse<string>.Success("更新成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除设备属性
    /// </summary>
    [HttpDelete("devices/property/{id}")]
    public async Task<ActionResult<ApiResponse<string>>> DeleteDeviceProperty(ulong id)
    {
        try
        {
            var success = await _deviceService.DeleteDevicePropertyAsync(id);
            if (!success)
            {
                return Ok(ApiResponse<string>.Error(404, "设备属性不存在"));
            }

            return Ok(ApiResponse<string>.Success("删除成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<string>.Error(500, ex.Message));
        }
    }
}
