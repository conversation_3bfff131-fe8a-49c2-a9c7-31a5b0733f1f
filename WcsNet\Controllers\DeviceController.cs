using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using WcsNet.DTOs;
using WcsNet.Models;
using WcsNet.Services;

namespace WcsNet.Controllers;

[ApiController]
[Route("api/[controller]")]
[Route("api/devices")] // 添加别名路由
public class DeviceController : ControllerBase
{
    private readonly DeviceService _deviceService;

    public DeviceController(DeviceService deviceService)
    {
        _deviceService = deviceService;
    }

    /// <summary>
    /// 获取设备列表
    /// </summary>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <param name="laneId">巷道ID（可选）</param>
    /// <param name="deviceType">设备类型（可选）</param>
    /// <returns>设备列表</returns>
    [HttpGet]
    public async Task<ActionResult<ApiResponse<PagedResponse<Device>>>> GetDevices(
        int pageIndex = 1, 
        int pageSize = 20, 
        uint? laneId = null, 
        sbyte? deviceType = null)
    {
        try
        {
            var (items, total) = await _deviceService.GetPagedAsync(pageIndex, pageSize, query =>
            {
                // 由于新的Device模型没有LaneId字段，暂时注释掉
                // if (laneId.HasValue)
                //     query = query.Where(d => d.LaneId == laneId.Value);

                if (deviceType.HasValue)
                    query = query.Where(d => d.DeviceType == deviceType.Value);

                return query.OrderBy(d => d.Id);
            });

            var response = new PagedResponse<Device>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Device>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Device>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据ID获取设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>设备信息</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<ApiResponse<Device>>> GetDevice(ulong id)
    {
        try
        {
            var device = await _deviceService.FindByIdAsync(id);
            if (device == null)
            {
                return Ok(ApiResponse<Device>.Error(404, "设备不存在"));
            }

            return Ok(ApiResponse<Device>.Success(device));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Device>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 创建设备
    /// </summary>
    /// <param name="device">设备信息</param>
    /// <returns>创建结果</returns>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<Device>>> CreateDevice([FromBody] Device device)
    {
        try
        {
            var createdDevice = await _deviceService.CreateAsync(device);
            return Ok(ApiResponse<Device>.Success(createdDevice, "设备创建成功"));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Device>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="updates">更新字段</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<ApiResponse>> UpdateDevice(ulong id, [FromBody] Dictionary<string, object> updates)
    {
        try
        {
            var success = await _deviceService.UpdateAsync(id, updates);
            
            if (success)
            {
                return Ok(ApiResponse.Success("设备更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "设备不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 删除设备
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <returns>删除结果</returns>
    [HttpDelete("{id}")]
    public async Task<ActionResult<ApiResponse>> DeleteDevice(ulong id)
    {
        try
        {
            var success = await _deviceService.DeleteAsync(id);
            
            if (success)
            {
                return Ok(ApiResponse.Success("设备删除成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "设备不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 更新设备状态
    /// </summary>
    /// <param name="id">设备ID</param>
    /// <param name="status">新状态</param>
    /// <returns>更新结果</returns>
    [HttpPut("{id}/status")]
    public async Task<ActionResult<ApiResponse>> UpdateDeviceStatus(ulong id, [FromBody] sbyte status)
    {
        try
        {
            var success = await _deviceService.UpdateStatusAsync(id, status);
            
            if (success)
            {
                return Ok(ApiResponse.Success("设备状态更新成功"));
            }
            else
            {
                return Ok(ApiResponse.Error(404, "设备不存在"));
            }
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 获取设备统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet("statistics")]
    public async Task<ActionResult<ApiResponse<Dictionary<string, int>>>> GetDeviceStatistics()
    {
        try
        {
            var statistics = await _deviceService.GetDeviceStatisticsAsync();
            return Ok(ApiResponse<Dictionary<string, int>>.Success(statistics));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<Dictionary<string, int>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 搜索设备
    /// </summary>
    /// <param name="keyword">关键词</param>
    /// <param name="pageIndex">页码</param>
    /// <param name="pageSize">页大小</param>
    /// <returns>搜索结果</returns>
    [HttpGet("search")]
    public async Task<ActionResult<ApiResponse<PagedResponse<Device>>>> SearchDevices(
        string keyword, 
        int pageIndex = 1, 
        int pageSize = 20)
    {
        try
        {
            var (items, total) = await _deviceService.SearchDevicesAsync(keyword, pageIndex, pageSize);

            var response = new PagedResponse<Device>
            {
                Items = items,
                Total = total,
                PageIndex = pageIndex,
                PageSize = pageSize
            };

            return Ok(ApiResponse<PagedResponse<Device>>.Success(response));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<PagedResponse<Device>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据巷道ID获取设备列表
    /// </summary>
    /// <param name="laneId">巷道ID</param>
    /// <returns>设备列表</returns>
    [HttpGet("lane/{laneId}")]
    public async Task<ActionResult<ApiResponse<List<Device>>>> GetDevicesByLane(ulong laneId)
    {
        try
        {
            var devices = await _deviceService.GetDevicesByLaneAsync(laneId);
            return Ok(ApiResponse<List<Device>>.Success(devices));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<Device>>.Error(500, ex.Message));
        }
    }

    /// <summary>
    /// 根据设备类型获取设备列表
    /// </summary>
    /// <param name="deviceType">设备类型</param>
    /// <returns>设备列表</returns>
    [HttpGet("type/{deviceType}")]
    public async Task<ActionResult<ApiResponse<List<Device>>>> GetDevicesByType(sbyte deviceType)
    {
        try
        {
            var devices = await _deviceService.GetDevicesByTypeAsync(deviceType);
            return Ok(ApiResponse<List<Device>>.Success(devices));
        }
        catch (Exception ex)
        {
            return Ok(ApiResponse<List<Device>>.Error(500, ex.Message));
        }
    }
}
