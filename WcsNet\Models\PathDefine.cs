using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WcsNet.Models;

[Table("wcs_path_define")]
public class PathDefine : BaseModel
{
    [Required]
    [StringLength(255)]
    [Column("path_name")]
    public string PathName { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    [Column("start_point")]
    public string StartPoint { get; set; } = string.Empty;

    [Required]
    [StringLength(128)]
    [Column("end_point")]
    public string EndPoint { get; set; } = string.Empty;

    [Column("transit_time")]
    public int TransitTime { get; set; } = 0; // 通过时间

    [Column("block_time")]
    public int BlockTime { get; set; } = 0; // 阻塞时间
}
