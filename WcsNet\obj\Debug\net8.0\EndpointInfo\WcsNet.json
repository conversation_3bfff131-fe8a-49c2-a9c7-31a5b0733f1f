{"openapi": "3.0.1", "info": {"title": "WcsNet", "version": "1.0"}, "paths": {"/api/Device": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "laneId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "deviceType", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}}}}}, "post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Device"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Device"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Device"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}}}}}}, "/api/Device/{id}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceApiResponse"}}}}}}, "put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Device/{id}/status": {"put": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Device/statistics": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}}}}}}, "/api/Device/search": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DevicePagedResponseApiResponse"}}}}}}}, "/api/Device/lane/{laneId}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "laneId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}}}}}}, "/api/Device/type/{deviceType}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "deviceType", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeviceListApiResponse"}}}}}}}, "/api/simple/login": {"post": {"tags": ["SimpleUser"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}}}}}}, "/api/simple/logout": {"get": {"tags": ["SimpleUser"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/simple/mine": {"get": {"tags": ["SimpleUser"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/health": {"get": {"tags": ["SimpleUser"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/": {"get": {"tags": ["SimpleUser"], "responses": {"200": {"description": "OK"}}}}, "/api/Stock": {"get": {"tags": ["Stock"], "parameters": [{"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "laneCode", "in": "query", "schema": {"type": "string"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}}}}}, "post": {"tags": ["Stock"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Stock"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Stock"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Stock"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}}}}}}, "/api/Stock/{id}": {"get": {"tags": ["Stock"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockApiResponse"}}}}}}, "put": {"tags": ["Stock"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"tags": ["Stock"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Stock/{id}/inbound": {"post": {"tags": ["Stock"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InboundRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/InboundRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/InboundRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Stock/{id}/outbound": {"post": {"tags": ["Stock"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Stock/empty/{laneId}": {"get": {"tags": ["Stock"], "parameters": [{"name": "laneCode", "in": "query", "schema": {"type": "string"}}, {"name": "laneId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}}}}}}, "/api/Stock/occupied/{laneId}": {"get": {"tags": ["Stock"], "parameters": [{"name": "laneCode", "in": "query", "schema": {"type": "string"}}, {"name": "laneId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}}}}}}, "/api/Stock/statistics": {"get": {"tags": ["Stock"], "parameters": [{"name": "laneCode", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}}}}}}, "/api/Stock/search": {"get": {"tags": ["Stock"], "parameters": [{"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockPagedResponseApiResponse"}}}}}}}, "/api/Stock/goods/{goodsCode}": {"get": {"tags": ["Stock"], "parameters": [{"name": "goodsCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StockListApiResponse"}}}}}}}, "/api/Upload": {"post": {"tags": ["Upload"], "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}, "delete": {"tags": ["Upload"], "parameters": [{"name": "filePath", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Upload/avatar": {"post": {"tags": ["Upload"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}}}}, "/api/User/login": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}}}}}}, "/api/User/logout": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/User/refreshtoken": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginResponseApiResponse"}}}}}}}, "/api/User/mine": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}}}}}}, "/api/User": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}, "text/json": {"schema": {"$ref": "#/components/schemas/User"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserApiResponse"}}}}}}, "get": {"tags": ["User"], "parameters": [{"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserPagedResponseApiResponse"}}}}}}}, "/api/User/{id}": {"put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Work": {"get": {"tags": ["Work"], "parameters": [{"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}, {"name": "laneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "status", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}}}}}, "post": {"tags": ["Work"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Work"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Work"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Work"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}}}}}}, "/api/Work/{id}": {"get": {"tags": ["Work"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkApiResponse"}}}}}}, "put": {"tags": ["Work"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}, "delete": {"tags": ["Work"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Work/steps/{workId}": {"get": {"tags": ["Work"], "parameters": [{"name": "workId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkStepListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkStepListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkStepListApiResponse"}}}}}}}, "/api/Work/{id}/status": {"put": {"tags": ["Work"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "integer", "format": "int32"}}, "text/json": {"schema": {"type": "integer", "format": "int32"}}, "application/*+json": {"schema": {"type": "integer", "format": "int32"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/Work/statistics": {"get": {"tags": ["Work"], "parameters": [{"name": "laneId", "in": "query", "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StringInt32DictionaryApiResponse"}}}}}}}, "/api/Work/search": {"get": {"tags": ["Work"], "parameters": [{"name": "keyword", "in": "query", "schema": {"type": "string"}}, {"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}}}}}}, "/api/Work/type/{taskType}": {"get": {"tags": ["Work"], "parameters": [{"name": "taskType", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/TaskType"}}, {"name": "pageIndex", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkPagedResponseApiResponse"}}}}}}}}, "components": {"schemas": {"ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "Device": {"required": ["deviceCode", "deviceName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "deviceCode": {"maxLength": 128, "minLength": 0, "type": "string"}, "deviceName": {"maxLength": 128, "minLength": 0, "type": "string"}, "deviceType": {"type": "integer", "format": "int32"}, "commType": {"type": "integer", "format": "int32"}, "addr": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "port": {"type": "integer", "format": "int64"}, "online": {"type": "integer", "format": "int32"}, "runStatus": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "position": {"type": "integer", "format": "int32"}, "createdId": {"type": "integer", "format": "int64"}, "updatedId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DeviceApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/Device"}}, "additionalProperties": false}, "DeviceListApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Device"}, "nullable": true}}, "additionalProperties": false}, "DevicePagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Device"}, "nullable": true}, "total": {"type": "integer", "format": "int64"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "DevicePagedResponseApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/DevicePagedResponse"}}, "additionalProperties": false}, "InboundRequest": {"type": "object", "properties": {"goodsCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"required": ["account", "password"], "type": "object", "properties": {"account": {"minLength": 1, "type": "string"}, "password": {"minLength": 1, "type": "string"}, "days": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "LoginResponse": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "username": {"type": "string", "nullable": true}, "realname": {"type": "string", "nullable": true}, "photo": {"type": "string", "nullable": true}, "deptId": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "mobile": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expires": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "LoginResponseApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/LoginResponse"}}, "additionalProperties": false}, "ObjectApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}}, "additionalProperties": false}, "Stock": {"required": ["stockCode", "stockName"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "stockName": {"maxLength": 128, "minLength": 0, "type": "string"}, "stockCode": {"maxLength": 24, "minLength": 0, "type": "string"}, "laneCode": {"maxLength": 24, "minLength": 0, "type": "string", "nullable": true}, "shelves": {"maxLength": 24, "minLength": 0, "type": "string", "nullable": true}, "x": {"maxLength": 16, "minLength": 0, "type": "string", "nullable": true}, "y": {"maxLength": 16, "minLength": 0, "type": "string", "nullable": true}, "z": {"maxLength": 16, "minLength": 0, "type": "string", "nullable": true}, "layer": {"type": "integer", "format": "int32"}, "deep": {"type": "integer", "format": "int32"}, "side": {"type": "integer", "format": "int32"}, "abcType": {"maxLength": 8, "minLength": 0, "type": "string", "nullable": true}, "goodsCode": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "createdId": {"type": "integer", "format": "int64"}, "updatedId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "hasGoods": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "StockApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/Stock"}}, "additionalProperties": false}, "StockListApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/Stock"}, "nullable": true}}, "additionalProperties": false}, "StockPagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Stock"}, "nullable": true}, "total": {"type": "integer", "format": "int64"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "StockPagedResponseApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/StockPagedResponse"}}, "additionalProperties": false}, "StringInt32DictionaryApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "TaskType": {"enum": [1, 2], "type": "integer", "format": "int32"}, "User": {"required": ["account", "password", "realname"], "type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "account": {"maxLength": 64, "minLength": 0, "type": "string"}, "password": {"maxLength": 128, "minLength": 0, "type": "string"}, "realname": {"maxLength": 64, "minLength": 0, "type": "string"}, "staffCode": {"maxLength": 24, "minLength": 0, "type": "string", "nullable": true}, "photo": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "deptId": {"type": "integer", "format": "int64"}, "roleId": {"type": "integer", "format": "int64"}, "mobile": {"maxLength": 15, "minLength": 0, "type": "string", "nullable": true}, "email": {"maxLength": 255, "minLength": 0, "type": "string", "nullable": true}, "gender": {"type": "integer", "format": "int32"}, "status": {"type": "integer", "format": "int32"}, "userSource": {"type": "integer", "format": "int32"}, "remark": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "createdId": {"type": "integer", "format": "int64"}, "updatedId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UserApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/User"}}, "additionalProperties": false}, "UserPagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/User"}, "nullable": true}, "total": {"type": "integer", "format": "int64"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "UserPagedResponseApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/UserPagedResponse"}}, "additionalProperties": false}, "Work": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "taskType": {"type": "integer", "format": "int32"}, "laneId": {"type": "integer", "format": "int64"}, "itemCode": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "parameters": {"type": "string", "nullable": true}, "retries": {"type": "integer", "format": "int32"}, "maxRetries": {"type": "integer", "format": "int32"}, "errMsg": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "createdId": {"type": "integer", "format": "int64"}, "updatedId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "WorkApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/Work"}}, "additionalProperties": false}, "WorkPagedResponse": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Work"}, "nullable": true}, "total": {"type": "integer", "format": "int64"}, "pageIndex": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "WorkPagedResponseApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/WorkPagedResponse"}}, "additionalProperties": false}, "WorkStep": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "workId": {"type": "integer", "format": "int64"}, "stepNumber": {"type": "integer", "format": "int32"}, "actuatorId": {"type": "integer", "format": "int64"}, "deviceType": {"type": "integer", "format": "int32"}, "opType": {"type": "integer", "format": "int32"}, "params": {"type": "string", "nullable": true}, "endTime": {"type": "string", "format": "date-time", "nullable": true}, "status": {"type": "integer", "format": "int32"}, "remark": {"maxLength": 128, "minLength": 0, "type": "string", "nullable": true}, "createdId": {"type": "integer", "format": "int64"}, "updatedId": {"type": "integer", "format": "int64"}, "createdAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "work": {"$ref": "#/components/schemas/Work"}}, "additionalProperties": false}, "WorkStepListApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/WorkStep"}, "nullable": true}}, "additionalProperties": false}}}}