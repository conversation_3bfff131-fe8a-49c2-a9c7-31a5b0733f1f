[{"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "GetApiInfo", "RelativePath": "", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WcsNet.Controllers.AlarmController", "Method": "GetAlarms", "RelativePath": "api/alarms", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "alarm_type", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "device_id", "Type": "System.Nullable`1[[System.UInt32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.AlarmController", "Method": "DeleteAlarm", "RelativePath": "api/alarms/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.AlarmController", "Method": "HandleAlarm", "RelativePath": "api/alarms/{id}/handle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.AlarmController", "Method": "BatchHandleAlarms", "RelativePath": "api/alarms/batch-handle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.AlarmController", "Method": "GetAlarmStats", "RelativePath": "api/alarms/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiLogController", "Method": "GetApiLogs", "RelativePath": "api/apilogs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "api_name", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiLogController", "Method": "GetApiLog", "RelativePath": "api/apilogs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.ApiLogDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevices", "RelativePath": "api/devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "device_name", "Type": "System.String", "IsRequired": false}, {"Name": "device_code", "Type": "System.String", "IsRequired": false}, {"Name": "device_type", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "CreateDevice", "RelativePath": "api/devices", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.DeviceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevice", "RelativePath": "api/devices/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DeviceDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "UpdateDevice", "RelativePath": "api/devices/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.DTOs.DeviceRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "DeleteDevice", "RelativePath": "api/devices/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDeviceProperties", "RelativePath": "api/devices/props/{deviceId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceId", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.InitController", "Method": "InitializeDatabase", "RelativePath": "api/Init/database", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.InitController", "Method": "InitializeLogs", "RelativePath": "api/Init/logs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.InitController", "Method": "ResetDatabase", "RelativePath": "api/Init/reset", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.InitController", "Method": "GetDatabaseStatus", "RelativePath": "api/Init/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "GetLanes", "RelativePath": "api/lanes", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "CreateLane", "RelativePath": "api/lanes", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateLaneRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "GetLane", "RelativePath": "api/lanes/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LaneDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "UpdateLane", "RelativePath": "api/lanes/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateLaneRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "DeleteLane", "RelativePath": "api/lanes/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "GetLaneDeviceIds", "RelativePath": "api/lanes/deviceids", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "GetLaneDevices", "RelativePath": "api/lanes/devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "lane_id", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.LaneDeviceDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LaneController", "Method": "GetUseableLaneDevices", "RelativePath": "api/lanes/useable/devices", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.LaneDeviceDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDeviceProperties", "RelativePath": "api/property", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "CreateDeviceProperty", "RelativePath": "api/property", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.DevicePropertyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDeviceProperty", "RelativePath": "api/property/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.DevicePropertyDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "UpdateDeviceProperty", "RelativePath": "api/property/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.DTOs.DevicePropertyRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "DeleteDeviceProperty", "RelativePath": "api/property/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/simple/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "Logout", "RelativePath": "api/simple/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "GetCurrentUser", "RelativePath": "api/simple/mine", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StatController", "Method": "GetTaskStats", "RelativePath": "api/stat/tasks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StatController", "Method": "GetWeekTaskStats", "RelativePath": "api/stat/weektasks/{weekId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "weekId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.StatTaskDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStocks", "RelativePath": "api/Stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "CreateStock", "RelativePath": "api/Stock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "UpdateStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "DeleteStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Inbound", "RelativePath": "api/Stock/{id}/inbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.InboundRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Outbound", "RelativePath": "api/Stock/{id}/outbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetEmptyStocks", "RelativePath": "api/Stock/empty/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "FindByGoodsCode", "RelativePath": "api/Stock/goods/{goodsCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "goodsCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetOccupiedStocks", "RelativePath": "api/Stock/occupied/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "SearchStocks", "RelativePath": "api/Stock/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStockStatistics", "RelativePath": "api/Stock/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStocks", "RelativePath": "api/stocks", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "CreateStock", "RelativePath": "api/stocks", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateStockRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStock", "RelativePath": "api/stocks/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "UpdateStock", "RelativePath": "api/stocks/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "DeleteStock", "RelativePath": "api/stocks/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Inbound", "RelativePath": "api/stocks/{id}/inbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.InboundRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Outbound", "RelativePath": "api/stocks/{id}/outbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetEmptyStocks", "RelativePath": "api/stocks/empty/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "FindByGoodsCode", "RelativePath": "api/stocks/goods/{goodsCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "goodsCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetOccupiedStocks", "RelativePath": "api/stocks/occupied/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "SearchStocks", "RelativePath": "api/stocks/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStockStatistics", "RelativePath": "api/stocks/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ConfController", "Method": "GetConfs", "RelativePath": "api/sys/confs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": false}, {"Name": "key", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ConfController", "Method": "CreateConf", "RelativePath": "api/sys/confs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateConfRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ConfController", "Method": "GetConf", "RelativePath": "api/sys/confs/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ConfController", "Method": "UpdateConf", "RelativePath": "api/sys/confs/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateConfRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ConfController", "Method": "DeleteConf", "RelativePath": "api/sys/confs/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeptController", "Method": "GetDeptList", "RelativePath": "api/sys/deptlist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeptController", "Method": "GetDepts", "RelativePath": "api/sys/depts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeptController", "Method": "CreateDept", "RelativePath": "api/sys/depts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateDeptRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeptController", "Method": "UpdateDept", "RelativePath": "api/sys/depts/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateDeptRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeptController", "Method": "DeleteDept", "RelativePath": "api/sys/depts/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "GetDicts", "RelativePath": "api/sys/dicts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "CreateDict", "RelativePath": "api/sys/dicts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateDictRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "UpdateDict", "RelativePath": "api/sys/dicts/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateDictRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "DeleteDict", "RelativePath": "api/sys/dicts/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "CreateDictValue", "RelativePath": "api/sys/dictvalues", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateDictValueRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "GetDictValues", "RelativePath": "api/sys/dictvalues/{dictId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dictId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "UpdateDictValue", "RelativePath": "api/sys/dictvalues/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateDictValueRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DictController", "Method": "DeleteDictValue", "RelativePath": "api/sys/dictvalues/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "BatchDeleteLoginLogs", "RelativePath": "api/sys/logs/loginbatchdel/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "GetLoginLogs", "RelativePath": "api/sys/logs/loginlog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "start_time", "Type": "System.String", "IsRequired": false}, {"Name": "end_time", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.SimplePagedResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "ClearLoginLogs", "RelativePath": "api/sys/logs/loginlog", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "GetOperationLogs", "RelativePath": "api/sys/logs/operation", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "module", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "start_time", "Type": "System.String", "IsRequired": false}, {"Name": "end_time", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.SimplePagedResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "ClearOperationLogs", "RelativePath": "api/sys/logs/operation", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.LogController", "Method": "BatchDeleteOperationLogs", "RelativePath": "api/sys/logs/operationbatchdel/{ids}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "ids", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.MenuController", "Method": "GetMenuList", "RelativePath": "api/sys/menulist", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.MenuController", "Method": "GetMenus", "RelativePath": "api/sys/menus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.MenuController", "Method": "CreateMenu", "RelativePath": "api/sys/menus", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateMenuRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.MenuController", "Method": "UpdateMenu", "RelativePath": "api/sys/menus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt32", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateMenuRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.MenuController", "Method": "DeleteMenu", "RelativePath": "api/sys/menus/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysController", "Method": "GetRoleMenus", "RelativePath": "api/Sys/rolemenus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.DTOs.MenuDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "GetRoles", "RelativePath": "api/sys/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "CreateRole", "RelativePath": "api/sys/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "UpdateRole", "RelativePath": "api/sys/roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt32", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateRoleRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "DeleteRole", "RelativePath": "api/sys/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "GetAllRoles", "RelativePath": "api/sys/roles/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "GetRoleMenuIds", "RelativePath": "api/sys/roles/menus/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt32", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.RoleController", "Method": "SetRoleMenus", "RelativePath": "api/sys/roles/menus/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt32", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.SetRoleMenusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "GetUsers", "RelativePath": "api/sys/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}, {"Name": "keyword", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.UserPagedResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "CreateUser", "RelativePath": "api/sys/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "UpdateUser", "RelativePath": "api/sys/users/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "DeleteUser", "RelativePath": "api/sys/users/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "ResetPassword", "RelativePath": "api/sys/users/resetpassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SysUserController", "Method": "ChangeUserStatus", "RelativePath": "api/sys/users/status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.ChangeStatusRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "GetTasks", "RelativePath": "api/Task", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.DTOs.TaskDto, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "GetActGroups", "RelativePath": "api/Task/actgroups", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "CreateActGroup", "RelativePath": "api/Task/actgroups", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateActGroupRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "UpdateActGroup", "RelativePath": "api/Task/actgroups/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateActGroupRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "DeleteActGroup", "RelativePath": "api/Task/actgroups/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "GetActs", "RelativePath": "api/Task/acts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "act_name", "Type": "System.String", "IsRequired": false}, {"Name": "group_id", "Type": "System.Nullable`1[[System.UInt64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "CreateAct", "RelativePath": "api/Task/acts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreateActRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "UpdateAct", "RelativePath": "api/Task/acts/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdateActRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "GetPaths", "RelativePath": "api/Task/paths", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Task/paths", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.CreatePathRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "UpdatePath", "RelativePath": "api/Task/paths/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.UpdatePathRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.TaskController", "Method": "DeletePath", "RelativePath": "api/Task/paths/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiTestController", "Method": "TestConnectivity", "RelativePath": "api/test/connectivity", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.Controllers.TestConnectivityRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiTestController", "Method": "GenerateTestData", "RelativePath": "api/test/data/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": true}, {"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiTestController", "Method": "GetEndpoints", "RelativePath": "api/test/endpoints", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.ApiTestController", "Method": "GetSystemStatus", "RelativePath": "api/test/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "UploadFile", "RelativePath": "api/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "DeleteFile", "RelativePath": "api/Upload", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "filePath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "UploadAvatar", "RelativePath": "api/Upload/avatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "WcsNet.Models.User", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "GetUsers", "RelativePath": "api/User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/User/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/User/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "Logout", "RelativePath": "api/User/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "GetCurrentUser", "RelativePath": "api/User/mine", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "RefreshToken", "RelativePath": "api/User/refreshtoken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.RefreshTokenRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorks", "RelativePath": "api/Work", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "task_type", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "start_time", "Type": "System.String", "IsRequired": false}, {"Name": "end_time", "Type": "System.String", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "CreateWork", "RelativePath": "api/Work", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "work", "Type": "WcsNet.Models.Work", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWork", "RelativePath": "api/Work/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWork", "RelativePath": "api/Work/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "DeleteWork", "RelativePath": "api/Work/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWorkStatus", "RelativePath": "api/Work/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "status", "Type": "System.SByte", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "SearchWorks", "RelativePath": "api/Work/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkStatistics", "RelativePath": "api/Work/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneId", "Type": "System.Nullable`1[[System.UInt64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkSteps", "RelativePath": "api/Work/steps/{workId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workId", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.WorkStep, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorksByType", "RelativePath": "api/Work/type/{taskType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskType", "Type": "System.String", "IsRequired": true}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorks", "RelativePath": "api/works", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "task_type", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "start_time", "Type": "System.String", "IsRequired": false}, {"Name": "end_time", "Type": "System.String", "IsRequired": false}, {"Name": "page_size", "Type": "System.Int32", "IsRequired": false}, {"Name": "page_no", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "CreateWork", "RelativePath": "api/works", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "work", "Type": "WcsNet.Models.Work", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWork", "RelativePath": "api/works/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWork", "RelativePath": "api/works/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "DeleteWork", "RelativePath": "api/works/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWorkStatus", "RelativePath": "api/works/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "status", "Type": "System.SByte", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "SearchWorks", "RelativePath": "api/works/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkStatistics", "RelativePath": "api/works/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneId", "Type": "System.Nullable`1[[System.UInt64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkSteps", "RelativePath": "api/works/steps/{workId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workId", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.WorkStep, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorksByType", "RelativePath": "api/works/type/{taskType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskType", "Type": "System.String", "IsRequired": true}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "Health", "RelativePath": "health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]