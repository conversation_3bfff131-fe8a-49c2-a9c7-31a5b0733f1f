[{"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "GetApiInfo", "RelativePath": "", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevices", "RelativePath": "api/Device", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "laneId", "Type": "System.Nullable`1[[System.UInt32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "deviceType", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "CreateDevice", "RelativePath": "api/Device", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "device", "Type": "WcsNet.Models.Device", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevice", "RelativePath": "api/Device/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "UpdateDevice", "RelativePath": "api/Device/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "DeleteDevice", "RelativePath": "api/Device/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "UpdateDeviceStatus", "RelativePath": "api/Device/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "status", "Type": "System.SByte", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevicesByLane", "RelativePath": "api/Device/lane/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneId", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "SearchDevices", "RelativePath": "api/Device/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDeviceStatistics", "RelativePath": "api/Device/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.DeviceController", "Method": "GetDevicesByType", "RelativePath": "api/Device/type/{deviceType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "deviceType", "Type": "System.SByte", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Device, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/simple/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "Logout", "RelativePath": "api/simple/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "GetCurrentUser", "RelativePath": "api/simple/mine", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStocks", "RelativePath": "api/Stock", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "CreateStock", "RelativePath": "api/Stock", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "stock", "Type": "WcsNet.Models.Stock", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "UpdateStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "DeleteStock", "RelativePath": "api/Stock/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Inbound", "RelativePath": "api/Stock/{id}/inbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "request", "Type": "WcsNet.Controllers.InboundRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "Outbound", "RelativePath": "api/Stock/{id}/outbound", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetEmptyStocks", "RelativePath": "api/Stock/empty/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "FindByGoodsCode", "RelativePath": "api/Stock/goods/{goodsCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "goodsCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetOccupiedStocks", "RelativePath": "api/Stock/occupied/{laneId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}, {"Name": "laneId", "Type": "", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "SearchStocks", "RelativePath": "api/Stock/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Stock, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.StockController", "Method": "GetStockStatistics", "RelativePath": "api/Stock/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneCode", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "UploadFile", "RelativePath": "api/Upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "DeleteFile", "RelativePath": "api/Upload", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "filePath", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UploadController", "Method": "UploadAvatar", "RelativePath": "api/Upload/avatar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "CreateUser", "RelativePath": "api/User", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "user", "Type": "WcsNet.Models.User", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "GetUsers", "RelativePath": "api/User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "DeleteUser", "RelativePath": "api/User/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "<PERSON><PERSON>", "RelativePath": "api/User/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "WcsNet.DTOs.LoginRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "Logout", "RelativePath": "api/User/logout", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "GetCurrentUser", "RelativePath": "api/User/mine", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.User, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.UserController", "Method": "RefreshToken", "RelativePath": "api/User/refreshtoken", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.LoginResponse, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorks", "RelativePath": "api/Work", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "laneId", "Type": "System.Nullable`1[[System.UInt64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.Nullable`1[[System.SByte, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "CreateWork", "RelativePath": "api/Work", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "work", "Type": "WcsNet.Models.Work", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWork", "RelativePath": "api/Work/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWork", "RelativePath": "api/Work/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "updates", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "DeleteWork", "RelativePath": "api/Work/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "UpdateWorkStatus", "RelativePath": "api/Work/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.UInt64", "IsRequired": true}, {"Name": "status", "Type": "System.SByte", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "SearchWorks", "RelativePath": "api/Work/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "keyword", "Type": "System.String", "IsRequired": false}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkStatistics", "RelativePath": "api/Work/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "laneId", "Type": "System.Nullable`1[[System.UInt64, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorkSteps", "RelativePath": "api/Work/steps/{workId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workId", "Type": "System.UInt64", "IsRequired": true}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Collections.Generic.List`1[[WcsNet.Models.WorkStep, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.WorkController", "Method": "GetWorksByType", "RelativePath": "api/Work/type/{taskType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "taskType", "Type": "System.String", "IsRequired": true}, {"Name": "pageIndex", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[WcsNet.DTOs.PagedResponse`1[[WcsNet.Models.Work, WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]], WcsNet, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "WcsNet.Controllers.SimpleUserController", "Method": "Health", "RelativePath": "health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "WcsNet.DTOs.ApiResponse`1[[System.Object, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]